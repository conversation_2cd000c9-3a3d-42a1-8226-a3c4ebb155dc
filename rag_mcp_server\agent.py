import os
import sys
import logging
import traceback
import json
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>
import webvtt
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai
from langchain_community.vectorstores import FAISS
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)
logger = logging.getLogger('rag_mcp_agent')

# Load environment variables
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable is not set")
else:
    genai.configure(api_key=GOOGLE_API_KEY)

# Project paths
current_project_path = os.path.abspath(os.path.dirname(__file__))
documents_dir = os.path.join(current_project_path, "documents")
vector_store_path = os.path.join(current_project_path, "faiss_index")
os.makedirs(documents_dir, exist_ok=True)

processed_documents = False

def list_files_in_documents_dir() -> dict:
    try:
        files = os.listdir(documents_dir)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        vtt_files = [f for f in files if f.lower().endswith('.vtt')]
        text_files = [f for f in files if f.lower().endswith(('.txt', '.md', '.text'))]
        return {
            "documents_dir": documents_dir,
            "pdf_files": pdf_files,
            "vtt_files": vtt_files,
            "text_files": text_files
        }
    except Exception as e:
        return {"error": str(e), "documents_dir": documents_dir}

def get_pdf_text(pdf_path):
    text = ""
    try:
        pdf_reader = PdfReader(pdf_path)
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        return f"Error processing PDF: {str(e)}"

def get_text_file_content(text_path):
    try:
        with open(text_path, 'r', encoding='utf-8') as file:
            text = file.read()
        return text
    except UnicodeDecodeError:
        try:
            with open(text_path, 'r', encoding='latin-1') as file:
                text = file.read()
            return text
        except Exception as e:
            return f"Error processing text file: {str(e)}"
    except Exception as e:
        return f"Error processing text file: {str(e)}"

def get_vtt_text(vtt_path):
    text = ""
    try:
        for caption in webvtt.read(vtt_path):
            text += f"{caption.text}\n"
        return text
    except Exception as e:
        return f"Error processing VTT: {str(e)}"

def get_text_chunks(text):
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
    return text_splitter.split_text(text)

def create_vector_store(text_chunks):
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
    os.makedirs(os.path.dirname(vector_store_path), exist_ok=True)
    vector_store.save_local(vector_store_path)
    return "Vector store created successfully"

def process_all_files() -> dict:
    global processed_documents
    try:
        files = list_files_in_documents_dir()
        pdf_files = files.get("pdf_files", [])
        vtt_files = files.get("vtt_files", [])
        text_files = files.get("text_files", [])
        all_text = ""
        processed_count = 0
        for pdf_file in pdf_files:
            file_path = os.path.join(documents_dir, pdf_file)
            text = get_pdf_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for vtt_file in vtt_files:
            file_path = os.path.join(documents_dir, vtt_file)
            text = get_vtt_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for text_file in text_files:
            file_path = os.path.join(documents_dir, text_file)
            text = get_text_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        if all_text:
            chunks = get_text_chunks(all_text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": f"Processed {processed_count} files and created vector store"}
        else:
            return {"status": "warning", "message": "No text was extracted from any files"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def process_file(file_path: str) -> dict:
    global processed_documents
    try:
        text = ""
        if file_path.lower().endswith('.pdf'):
            text = get_pdf_text(file_path)
        elif file_path.lower().endswith('.vtt'):
            text = get_vtt_text(file_path)
        elif file_path.lower().endswith(('.txt', '.md', '.text')):
            text = get_text_file_content(file_path)
        else:
            return {"status": "error", "message": "Unsupported file type."}
        if text and not text.startswith("Error"):
            chunks = get_text_chunks(text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": "File processed and vector store created"}
        else:
            return {"status": "error", "message": text}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def create_vector_store_from_text(text: str) -> dict:
    global processed_documents
    try:
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        return {"status": "success", "message": "Text processed and vector store created"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def process_and_create_vector_store(file_path: str) -> dict:
    result = process_file(file_path)
    if result.get("status") == "success":
        return {"status": "success", "message": "File processed and vector store created successfully"}
    else:
        return result

def retrieve_relevant_documents(query: str) -> dict:
    """
    Retrieve relevant document chunks for a given query from the vector store.
    Returns the most relevant document contents as context for the agent LLM.
    """
    global processed_documents
    try:
        if not processed_documents:
            return {"status": "error", "context": [], "message": "No documents have been processed yet. Please process documents first."}
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        docs = vector_store.similarity_search(query)
        return {"status": "success", "context": [doc.page_content for doc in docs]}
    except Exception as e:
        return {"status": "error", "context": [], "message": f"Error: {str(e)}"}

def execute_local_api_call(api_response: str) -> dict:
    """
    Execute a local GET API call based on the response from the LLM or a direct HTTP method/endpoint string.
    Supports both verbose LLM responses and simple 'GET /pet/findByStatus?status=available' format.
    """
    import re
    try:
        # Try to extract endpoint from verbose LLM response
        endpoint_match = re.search(r"Endpoint: ([^\n]+)", api_response)
        if endpoint_match:
            endpoint = endpoint_match.group(1)
        else:
            # Try to parse simple format like 'GET /pet/findByStatus?status=available'
            simple_match = re.match(r"(GET)\s+(.+)", api_response.strip(), re.IGNORECASE)
            if simple_match:
                endpoint = simple_match.group(2)
            else:
                return {"status": "error", "message": "Could not parse API response. Missing endpoint."}

        # Determine which local JSON file to use based on the endpoint
        data_dir = os.path.join(current_project_path, "data")
        if "/pet" in endpoint:
            data_file = os.path.join(data_dir, "PetData.json")
            data_key = "pets"
        elif "/user" in endpoint:
            data_file = os.path.join(data_dir, "UserData.json")
            data_key = "users"
        elif "/store" in endpoint or "/order" in endpoint:
            data_file = os.path.join(data_dir, "OrderData.json")
            data_key = "orders"
        else:
            return {"status": "error", "message": f"Unknown endpoint: {endpoint}"}

        # Check if file exists
        if not os.path.exists(data_file):
            return {"status": "error", "message": f"Data file not found: {data_file}"}

        # Load data from JSON file
        with open(data_file, 'r') as f:
            file_data = json.load(f)
            if isinstance(file_data, dict) and data_key in file_data:
                data = file_data[data_key]
            else:
                data = file_data

        # Filter data based on endpoint
        result = data
        id_match = re.search(r"/(\\d+)", endpoint)
        if id_match:
            item_id = int(id_match.group(1))
            result = next((item for item in data if item.get('id') == item_id), None)
            if not result:
                return {"status": "error", "message": f"Item with ID {item_id} not found"}
        if "status=" in endpoint:
            status = endpoint.split("status=")[1].split("&")[0]
            result = [item for item in data if item.get('status') == status]
        return {"status": "success", "data": result}
    except Exception as e:
        return {"status": "error", "message": f"Error executing local API call: {str(e)}"}
    
def ask_question(question: str) -> dict:
    """
    A unified function to handle both retrieving API documentation and executing the call.
    This allows for a seamless experience where the user only needs to ask a question once.
    """
    # Step 1: Retrieve relevant API documentation based on the question
    retrieval_result = retrieve_relevant_documents(question)
    
    if retrieval_result.get("status") == "error":
        return retrieval_result
    
    context = retrieval_result.get("context", [])
    if not context:
        return {"status": "warning", "message": "No relevant API documentation found for your question."}
    
    # Use Gemini to identify the correct API endpoint from the context
    try:
        genai.configure(api_key=GOOGLE_API_KEY)
        model = genai.GenerativeModel('gemini-2.0-flash')
        
        prompt = f"""
        Based on the following API documentation and the user question: "{question}", 
        determine the exact API endpoint that should be called.
        
        API Documentation:
        {' '.join(context)}
        
        Return ONLY the complete API endpoint in the format 'GET /path/to/endpoint?param=value' 
        without any additional explanation. Example: 'GET /pet/findByStatus?status=available'
        """
        
        response = model.generate_content(prompt)
        endpoint = response.text.strip()
        
        # Step 2: Execute the API call using the identified endpoint
        api_result = execute_local_api_call(endpoint)
        
        # Return combined results for transparency
        return {
            "status": api_result.get("status", "error"),
            "endpoint_used": endpoint,
            "data": api_result.get("data", {}),
            "message": api_result.get("message", "")
        }
    except Exception as e:
        return {"status": "error", "message": f"Error processing request: {str(e)}"}

# --- Define Model Constants for easier use ---

MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"

# Note: Specific model names might change. Refer to LiteLLM/Provider documentation.
MODEL_GPT_4O = "openai/gpt-4o"
MODEL_CLAUDE_SONNET = "anthropic/claude-3-sonnet-20240229"
MODEL_GROQ_LLAMA_3_8B_8192 = "groq/llama-3.1-8b-instant"


print("\nEnvironment configured.")

root_agent = Agent(
    name="rag_mcp_agent",
    # model=LiteLlm(model=MODEL_GROQ_LLAMA_3_8B_8192),
    model="gemini-2.0-flash",
    # model="gemini-1.5-pro",
    description=(
        "Agent to answer user questions by retrieving relevant API documentation and executing local API calls automatically. "
        "The agent handles the complete flow - from understanding the question to returning API results."
    ),
    instruction=(
        "You are an intelligent API assistant specialized in helping users interact with a pet store API."
        " When a user asks a question like 'How can I find all available pets for adoption?' or 'How do I update a pet's information?':"
        "\n\n1. Understand what API functionality they're looking for"
        "\n2. Use the ask_question tool to handle both finding the right API endpoint and executing the call in one seamless operation"
        "\n3. Present the results in a clear, user-friendly way, explaining what the data means"
        "\n\nIf the user's query isn't related to API functionality or is ambiguous, ask clarifying questions."
        "\n\nAlways explain API responses in simple terms, highlighting the most relevant information first."
        "\n\nIf a user uploads documents, use the process_file or process_all_files tool to handle them, then confirm the documents are ready to use."
    ),
    tools=[
        list_files_in_documents_dir,
        process_all_files,
        process_file,
        create_vector_store_from_text,
        process_and_create_vector_store,
        retrieve_relevant_documents,
        execute_local_api_call,
        ask_question
    ],
) 