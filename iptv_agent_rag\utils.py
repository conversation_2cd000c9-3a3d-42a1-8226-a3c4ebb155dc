"""
Utility functions for the IPTV Agent
"""

import json
from typing import Dict, Any, List, Optional

def limit_response_tokens(data: Dict[str, Any], 
                         max_items: int = 10, 
                         max_description_length: int = 100, 
                         max_total_tokens: int = 5000) -> Dict[str, Any]:
    """
    Limit the size of the API response to prevent token limit errors.
    
    Args:
        data (dict): The API response data
        max_items (int): Maximum number of items to include in lists
        max_description_length (int): Maximum length for text fields
        max_total_tokens (int): Approximate max tokens for entire response
        
    Returns:
        dict: Truncated data that should fit within token limits
    """
    if not data or not isinstance(data, dict):
        return data
        
    # Handle data lists
    if "data" in data and isinstance(data["data"], list):
        # Limit number of items
        data["data"] = data["data"][:max_items]
        
        # Truncate long text fields in each item
        for item in data["data"]:
            if isinstance(item, dict):
                for key, value in item.items():
                    if isinstance(value, str) and len(value) > max_description_length:
                        item[key] = value[:max_description_length] + "..."
    
    # Estimate token size and further truncate if needed
    json_str = json.dumps(data)
    estimated_tokens = len(json_str) / 4  # Rough approximation: 4 chars ~= 1 token
    
    if estimated_tokens > max_total_tokens and "data" in data and isinstance(data["data"], list):
        # Further reduce items if still too large
        reduced_items = max(1, int(max_items * (max_total_tokens / estimated_tokens)))
        data["data"] = data["data"][:reduced_items]
        data["note"] = f"Response truncated to {reduced_items} items due to token limits."
    
    return data 