2025-05-29 16:40:53,585 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 16:48:17,648 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 16:56:10,735 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 16:59:48,604 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 16:59:50,914 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: How to add a pet to store?
2025-05-29 16:59:56,515 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (a:Api)-[:HAS_ENDPOINT]->(e:Endpoint)
WHERE e.path CONTAINS "/pet" AND e.method = "POST"
RETURN a, e

2025-05-29 17:05:14,879 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 17:05:17,145 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: How to find the pet status
2025-05-29 17:05:21,799 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (a:Datamodel)-[:HAS_PROPERTY]->(b:Property)
WHERE b.id = "pet status"
RETURN a, b

2025-05-29 17:06:10,790 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: Which api to call to get the list of pets
2025-05-29 17:06:12,532 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (a:Api)-[:HAS_ENDPOINT]->(e:Endpoint)
WHERE e.path CONTAINS '/pets'
RETURN a

2025-05-29 17:06:23,508 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: List all api endpoints
2025-05-29 17:06:25,935 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (n:Endpoint) RETURN n

2025-05-29 17:07:02,625 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the relationships of all the endpoints
2025-05-29 17:07:05,068 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (n:Endpoint)-[r]->(m)
RETURN startNode(r) AS source, type(r) AS relationship, endNode(r) AS target

2025-05-29 17:07:48,646 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: What is available in store?
2025-05-29 17:07:49,606 - pet_store_agent.services.knowledge_graph_service - ERROR - Error querying knowledge graph: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input 'There': expected 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'FOREACH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"There is no information about what is available in store in the graph database."
 ^}
2025-05-29 17:10:38,221 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 17:10:40,366 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: How to add a pet to store? what are the api calls required for this
2025-05-29 17:10:46,089 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Endpoint)-[:HAS_REQUEST_BODY]->(d:Datamodel)
WHERE d.id = "Pet"
RETURN e

2025-05-29 18:09:40,094 - pet_store_agent.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-29 18:09:43,901 - pet_store_agent.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the api endpoints
2025-05-29 18:09:48,834 - pet_store_agent.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (n:Endpoint) RETURN n

