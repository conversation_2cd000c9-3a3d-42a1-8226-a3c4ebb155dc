import os
import logging
import requests
from typing import List, Dict, Optional, Any, Type, Union, TypeVar, Generic
import urllib3
from datetime import datetime
import os.path
from .models import (
    ApiRequest, ApiResponse,
    ChannelRequest, ChannelsResponse, Channel,
    FeedRequest, FeedsResponse, Feed,
    StreamRequest, StreamsResponse, Stream,
    CategoriesResponse, Category,
    LanguagesResponse, Language,
    CountriesResponse, Country,
    SubdivisionsResponse, Subdivision,
    RegionsResponse, Region,
    TimezonesResponse, Timezone,
    BlocklistResponse, BlockItem,
    GuidesResponse, Guide,
    QueryRequest, QueryResponse, QueryResponseItem
)

# Suppress SSL warnings - only use this in development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Configure logging to a file
current_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(current_dir, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Create a timestamp for the log file
log_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(logs_dir, f"epg_client_{log_timestamp}.log")

# Configure logger
logger = logging.getLogger('epg_client')
logger.setLevel(logging.DEBUG)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add the file handler to the logger
logger.addHandler(file_handler)

# Also add a console handler for immediate feedback
console_handler = logging.StreamHandler()
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

logger.info(f"EPG Client logging initialized. Log file: {log_file}")

T = TypeVar('T', bound=ApiResponse)

class EpgApiClient:
    """Client for interacting with the EPG API"""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the EPG API client
        
        Args:
            base_url: Base URL for the EPG API. Defaults to the REMOTE_API_BASE_URL environment
                     variable or "https://iptv-org.github.io/api"
        """
        self.base_url = base_url or os.getenv("REMOTE_API_BASE_URL", "https://iptv-org.github.io/api")
        logger.info(f"Initialized EPG API client with base URL: {self.base_url}")
    
    def _make_request(self, 
                     endpoint: str, 
                     params: Optional[Dict[str, Any]] = None, 
                     response_model: Type[T] = ApiResponse) -> T:
        """
        Make a request to the EPG API
        
        Args:
            endpoint: The API endpoint to request (e.g., "channels.json")
            params: Optional query parameters
            response_model: Pydantic model to use for the response
            
        Returns:
            The API response parsed into the specified model
        """
        url = f"{self.base_url}/{endpoint}"
        logger.info(f"Making request to {url}")
        
        try:
            # Disable SSL verification - this is a workaround for SSL certificate issues
            # In production, you should fix the certificate issue instead of disabling verification
            response = requests.get(url, params=params, verify=False)
            response.raise_for_status()
            
            data = response.json()
            
            # For list responses, create the appropriate response object
            if isinstance(data, list):
                # Get the total number of items
                total_items = len(data)
                
                # For large responses, limit the amount of data we return
                if total_items > 100:
                    data = data[:100]
                    logger.info(f"Limited response to 100 items (total: {total_items})")
                
                # Create the response object
                return response_model(
                    status="success",
                    data=data,
                    total_items=total_items
                )
            else:
                # For single-item responses
                return response_model(
                    status="success",
                    data=[data]
                )
        
        except requests.RequestException as e:
            logger.error(f"Request error: {str(e)}")
            return response_model(
                status="error",
                message=f"Request error: {str(e)}",
                data=[]
            )
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return response_model(
                status="error",
                message=f"Error parsing response: {str(e)}",
                data=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return response_model(
                status="error",
                message=f"Unexpected error: {str(e)}",
                data=[]
            )
    
    def get_channels(self, request: Optional[ChannelRequest] = None) -> ChannelsResponse:
        """
        Get channels from the EPG API
        
        Args:
            request: Optional request parameters
            
        Returns:
            ChannelsResponse: The channels response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("channels.json", params, ChannelsResponse)
    
    def get_channel(self, channel_id: str) -> ChannelsResponse:
        """
        Get a specific channel by ID
        
        Args:
            channel_id: The channel ID
            
        Returns:
            ChannelsResponse: The channel response
        """
        all_channels = self.get_channels()
        if all_channels.status != "success":
            return all_channels
        
        # Find the specific channel
        matching_channels = [ch for ch in all_channels.data if ch.id == channel_id]
        
        if matching_channels:
            return ChannelsResponse(
                status="success",
                data=matching_channels,
                total_items=len(matching_channels)
            )
        else:
            return ChannelsResponse(
                status="error",
                message=f"Channel not found: {channel_id}",
                data=[],
                total_items=0
            )
    
    def get_feeds(self, request: Optional[FeedRequest] = None) -> FeedsResponse:
        """
        Get feeds from the EPG API
        
        Args:
            request: Optional request parameters
            
        Returns:
            FeedsResponse: The feeds response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("feeds.json", params, FeedsResponse)
    
    def get_feed(self, feed_id: str) -> FeedsResponse:
        """
        Get a specific feed by ID
        
        Args:
            feed_id: The feed ID
            
        Returns:
            FeedsResponse: The feed response
        """
        all_feeds = self.get_feeds()
        if all_feeds.status != "success":
            return all_feeds
        
        # Find the specific feed
        matching_feeds = [f for f in all_feeds.data if f.id == feed_id]
        
        if matching_feeds:
            return FeedsResponse(
                status="success",
                data=matching_feeds,
                total_items=len(matching_feeds)
            )
        else:
            return FeedsResponse(
                status="error",
                message=f"Feed not found: {feed_id}",
                data=[],
                total_items=0
            )
    
    def get_streams(self, request: Optional[StreamRequest] = None) -> StreamsResponse:
        """
        Get streams from the EPG API
        
        Args:
            request: Optional request parameters
            
        Returns:
            StreamsResponse: The streams response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("streams.json", params, StreamsResponse)
    
    def get_categories(self) -> CategoriesResponse:
        """
        Get categories from the EPG API
        
        Returns:
            CategoriesResponse: The categories response
        """
        return self._make_request("categories.json", None, CategoriesResponse)
    
    def get_languages(self) -> LanguagesResponse:
        """
        Get languages from the EPG API
        
        Returns:
            LanguagesResponse: The languages response
        """
        return self._make_request("languages.json", None, LanguagesResponse)
    
    def get_countries(self) -> CountriesResponse:
        """
        Get countries from the EPG API
        
        Returns:
            CountriesResponse: The countries response
        """
        return self._make_request("countries.json", None, CountriesResponse)
    
    def get_subdivisions(self) -> SubdivisionsResponse:
        """
        Get subdivisions from the EPG API
        
        Returns:
            SubdivisionsResponse: The subdivisions response
        """
        return self._make_request("subdivisions.json", None, SubdivisionsResponse)
    
    def get_regions(self) -> RegionsResponse:
        """
        Get regions from the EPG API
        
        Returns:
            RegionsResponse: The regions response
        """
        return self._make_request("regions.json", None, RegionsResponse)
    
    def get_timezones(self) -> TimezonesResponse:
        """
        Get timezones from the EPG API
        
        Returns:
            TimezonesResponse: The timezones response
        """
        return self._make_request("timezones.json", None, TimezonesResponse)
    
    def get_blocklist(self) -> BlocklistResponse:
        """
        Get blocklist from the EPG API
        
        Returns:
            BlocklistResponse: The blocklist response
        """
        return self._make_request("blocklist.json", None, BlocklistResponse)
    
    def get_guides(self, channel_id: Optional[str] = None) -> GuidesResponse:
        """
        Get program guides information from the EPG API
        
        Args:
            channel_id: Optional channel ID to filter by
            
        Returns:
            GuidesResponse: The guides response
        """
        all_guides = self._make_request("guides.json", None, GuidesResponse)
        
        # If a channel ID is provided, filter the guides
        if channel_id and all_guides.status == "success":
            matching_guides = [g for g in all_guides.data if g.channel == channel_id]
            return GuidesResponse(
                status="success",
                data=matching_guides,
                total_items=len(matching_guides)
            )
        return all_guides
    
    def query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query by determining which EPG endpoints to query
        
        Args:
            request: The query request
            
        Returns:
            QueryResponse: The query response with data from relevant endpoints
        """
        query_lower = request.query.lower()
        endpoints_to_query = []
        
        # Determine which endpoints to query based on the query content
        if "channel" in query_lower or "tv" in query_lower:
            endpoints_to_query.append(("channels.json", ChannelsResponse))
        if "feed" in query_lower:
            endpoints_to_query.append(("feeds.json", FeedsResponse))
        if "stream" in query_lower or "url" in query_lower:
            endpoints_to_query.append(("streams.json", StreamsResponse))
        if "categor" in query_lower:
            endpoints_to_query.append(("categories.json", CategoriesResponse))
        if "language" in query_lower:
            endpoints_to_query.append(("languages.json", LanguagesResponse))
        if "country" in query_lower or "countries" in query_lower:
            endpoints_to_query.append(("countries.json", CountriesResponse))
        if "subdivision" in query_lower or "province" in query_lower or "state" in query_lower:
            endpoints_to_query.append(("subdivisions.json", SubdivisionsResponse))
        if "region" in query_lower:
            endpoints_to_query.append(("regions.json", RegionsResponse))
        if "timezone" in query_lower:
            endpoints_to_query.append(("timezones.json", TimezonesResponse))
        if "block" in query_lower:
            endpoints_to_query.append(("blocklist.json", BlocklistResponse))
        
        # If no specific endpoint was determined, query common endpoints
        if not endpoints_to_query:
            endpoints_to_query = [
                ("channels.json", ChannelsResponse),
                ("categories.json", CategoriesResponse)
            ]
        
        # Query all relevant endpoints
        query_items = []
        endpoint_names = []
        
        for endpoint_info in endpoints_to_query:
            endpoint, response_model = endpoint_info
            endpoint_names.append(endpoint)
            
            # Make the request
            response = self._make_request(endpoint, None, response_model)
            
            if response.status == "success" and response.data:
                # Limit the number of items per endpoint to avoid information overload
                max_per_endpoint = min(5, request.max_results // len(endpoints_to_query))
                endpoint_data = response.data[:max_per_endpoint]
                
                # Create a QueryResponseItem for each endpoint
                query_items.append(
                    QueryResponseItem(
                        source=endpoint,
                        data=endpoint_data
                    )
                )
        
        # Create the final response
        return QueryResponse(
            status="success",
            query=request.query,
            data=query_items,
            endpoints_queried=endpoint_names
        ) 