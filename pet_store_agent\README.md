# Pet Store API Agent with LangChain Neo4j Integration

This project provides a simplified agent for identifying the correct Pet Store API endpoints from a Neo4j knowledge graph using LangChain's built-in Neo4j integration.

## Overview

The system consists of a single agent with direct access to Neo4j via LangChain's GraphCypherQAChain:

1. **API Agent**: Analyzes user questions and identifies the correct API endpoint to call
2. **Knowledge Graph Service**: Manages Neo4j connection and query execution using LangChain

## Key Features

- LangChain-based Neo4j integration for API documentation storage
- Automatic translation of natural language to Cypher queries
- Single agent architecture for API endpoint identification
- Direct knowledge graph querying to find matching API endpoints
- Returns structured API information (endpoint, method, parameters)
- Clean, focused codebase with minimal dependencies

## Setup

### Requirements

- Python 3.8+
- Required packages: langchain, langchain_community, langchain_google_genai, neo4j, google-generativeai, google-adk

### Installation

1. Install the required packages:

```bash
pip install langchain langchain_community langchain_google_genai neo4j google-generativeai google-adk
```

2. Set up environment variables:

```bash
export GOOGLE_API_KEY=your_google_api_key
export NEO4J_URI=your_neo4j_uri
export NEO4J_USERNAME=your_neo4j_username
export NEO4J_PASSWORD=your_neo4j_password
export NEO4J_DATABASE=your_neo4j_database
```

### Starting the System

Run the main script to test the agent's capabilities:

```bash
python main.py
```

## Architecture

The system uses a simplified architecture leveraging LangChain:

### Single Agent

The main agent that:
1. Receives user queries
2. Passes queries directly to the knowledge graph service
3. Presents the results to the user

### Knowledge Graph Service

A streamlined service that:
1. Uses LangChain's Neo4j integration
2. Translates natural language to Cypher
3. Executes queries against the Neo4j database
4. Returns structured results

## Using the Agent

The agent provides a simple interface for querying the knowledge graph:

### API Endpoint Identification Functions

- `query_graph(question)`: Query the knowledge graph to find matching API endpoints

The function returns a structured result with:
- `answer`: The recommended API endpoint and method
- `cypher_query`: The generated Cypher query used to find the endpoint
- `context`: Additional API documentation context
- `status`: Success or error indicator

## Example Queries

You can ask questions like:

- "How to add a pet to store?"
- "How to update pet information?"
- "How to delete a pet from the store?"
- "How to get pet details by ID?"
- "How to find pets by status?"
- "How to upload a pet image?"
- "How to get store inventory?"
- "How to place an order?"

## Architecture Diagram

```
User Query → Agent → Knowledge Graph Service → Neo4j Database
                 ↑                   ↓
                 └───← Result ←──────┘
```

## Project Structure

```
pet_store_agent/
├── agents/                   # Agent implementations
│   ├── agent.py              # Single agent with query function
│   └── __init__.py           # Agent exports
├── services/                 # Service implementations
│   ├── knowledge_graph_service.py # LangChain Neo4j integration
│   └── __init__.py           # Service exports
├── config/                   # Configuration
│   ├── settings.py           # Application settings
│   └── __init__.py           # Config exports
├── utils/                    # Utility functions
│   ├── logger.py             # Logging utilities
│   └── __init__.py           # Utility exports
└── __init__.py               # Package initialization
```

## Troubleshooting

- If the knowledge graph connection fails, check your Neo4j credentials and ensure the database is running
- If queries return no results, check that the knowledge graph contains relevant data
- If the agent is not responding, check that the GOOGLE_API_KEY environment variable is set correctly
- If responses are not helpful, try more specific queries or provide more context in your questions