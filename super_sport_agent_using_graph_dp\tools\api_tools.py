"""
API tools for SuperSport Agent
"""

from typing import Dict, Any, Optional, List, Callable
from datetime import datetime, timedelta
import re

from ..utils.logger import get_logger
from ..services.api_service import SuperSportApiService
from ..models.api_models import TVGuideRequest, QueryRequest

# Get logger for this module
logger = get_logger(__name__)

# Singleton API service
_api_service = None

def get_api_service() -> SuperSportApiService:
    """Get or create the singleton API service"""
    global _api_service
    if _api_service is None:
        _api_service = SuperSportApiService()
    return _api_service

def _parse_date(date_str: str) -> str:
    """
    Parse a date string and convert special patterns to actual dates
    
    Args:
        date_str: Date string, can be YYYY-MM-DD or special values like "today", "tomorrow", "yesterday", 
                 "last week", "N days ago", etc.
        
    Returns:
        Date string in YYYY-MM-DD format
    """
    # If it's already in the correct format, validate and return
    if re.match(r'^\d{4}-\d{2}-\d{2}$', date_str):
        try:
            # Validate date
            datetime.strptime(date_str, '%Y-%m-%d')
            return date_str
        except ValueError:
            # If invalid date, fall back to today's date
            logger.warning(f"Invalid date format: {date_str}, using today's date instead")
            return datetime.now().strftime('%Y-%m-%d')
    
    # Handle special date strings
    date_str = date_str.lower().strip()
    today = datetime.now()
    
    # Current day references
    if date_str == "today":
        return today.strftime('%Y-%m-%d')
    elif date_str == "tomorrow":
        return (today + timedelta(days=1)).strftime('%Y-%m-%d')
    elif date_str == "yesterday":
        return (today - timedelta(days=1)).strftime('%Y-%m-%d')
    
    # Relative future with "today+N" format
    elif date_str.startswith("today+"):
        try:
            days = int(date_str.split("+")[1])
            return (today + timedelta(days=days)).strftime('%Y-%m-%d')
        except (ValueError, IndexError):
            logger.warning(f"Could not parse date expression: {date_str}, using today's date instead")
            return today.strftime('%Y-%m-%d')
    
    # Relative past with "today-N" format
    elif date_str.startswith("today-"):
        try:
            days = int(date_str.split("-")[1])
            return (today - timedelta(days=days)).strftime('%Y-%m-%d')
        except (ValueError, IndexError):
            logger.warning(f"Could not parse date expression: {date_str}, using today's date instead")
            return today.strftime('%Y-%m-%d')
    
    # "N days ago" format
    elif re.match(r'^\d+\s+days?\s+ago$', date_str):
        try:
            days = int(re.match(r'^(\d+)\s+days?\s+ago$', date_str).group(1))
            return (today - timedelta(days=days)).strftime('%Y-%m-%d')
        except (ValueError, AttributeError):
            logger.warning(f"Could not parse date expression: {date_str}, using today's date instead")
            return today.strftime('%Y-%m-%d')
    
    # "last week", "last month", etc.
    elif date_str == "last week":
        return (today - timedelta(days=7)).strftime('%Y-%m-%d')
    elif date_str == "last month":
        # Approximate a month as 30 days
        return (today - timedelta(days=30)).strftime('%Y-%m-%d')
    elif date_str == "last year":
        # Approximate a year as 365 days
        return (today - timedelta(days=365)).strftime('%Y-%m-%d')
    
    # Try to parse month and day formats like "May 10" or "10 May"
    try:
        # Try "Month Day" format (e.g., "May 10")
        for fmt in ["%b %d", "%B %d"]:
            try:
                # Add current year to the date string
                current_year = today.year
                parsed_date = datetime.strptime(f"{date_str} {current_year}", f"{fmt} %Y")
                
                # If the resulting date is in the future, it's likely from the previous year
                if parsed_date > today:
                    parsed_date = datetime.strptime(f"{date_str} {current_year-1}", f"{fmt} %Y")
                    
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                pass
        
        # Try "Day Month" format (e.g., "10 May")
        for fmt in ["%d %b", "%d %B"]:
            try:
                # Add current year to the date string
                current_year = today.year
                parsed_date = datetime.strptime(f"{date_str} {current_year}", f"{fmt} %Y")
                
                # If the resulting date is in the future, it's likely from the previous year
                if parsed_date > today:
                    parsed_date = datetime.strptime(f"{date_str} {current_year-1}", f"{fmt} %Y")
                    
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                pass
                
    except Exception:
        pass
    
    # For any other format, use today's date
    logger.warning(f"Unrecognized date format: {date_str}, using today's date instead")
    return today.strftime('%Y-%m-%d')

def super_sport_get_tv_guide(
    country_code: str,
    start_date_time: str,
    end_date_time: str,
    channel_only: Optional[bool] = None,
    live_only: Optional[bool] = None,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get TV guide data from the SuperSport API
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        start_date_time: Start date for the program guide (YYYY-MM-DD or "today", "tomorrow", etc.)
        end_date_time: End date for the program guide (YYYY-MM-DD or "today", "tomorrow", etc.)
        channel_only: When true, returns only channel information without program data
        live_only: When true, returns only live events
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        TVGuideResponse as dict
    """
    try:
        # Set default values
        if channel_only is None:
            channel_only = False
        if live_only is None:
            live_only = False
        
        # Parse date strings and convert special formats to actual dates
        parsed_start_date = _parse_date(start_date_time)
        parsed_end_date = _parse_date(end_date_time)
        
        logger.info(f"Parsed dates: start={parsed_start_date}, end={parsed_end_date} (original: start={start_date_time}, end={end_date_time})")
            
        # Create request model
        request = TVGuideRequest(
            countryCode=country_code,
            startDateTime=parsed_start_date,
            endDateTime=parsed_end_date,
            channelOnly=channel_only,
            liveOnly=live_only
        )
        
        # Make request with query for intelligent preprocessing
        api_service = get_api_service()
        response = api_service.get_tv_guide(request, query=query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_tv_guide: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_get_live_sports(
    country_code: str,
    date: Optional[str] = None,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get currently broadcasting live sports events
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        date: Date to check for live events (default: today), can use special values like "today", "tomorrow"
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        LiveSportsResponse as dict
    """
    try:
        # Parse date or use today by default
        if date is None:
            date = "today"
        
        parsed_date = _parse_date(date)
        logger.info(f"Getting live sports for date: {parsed_date} (original: {date})")
        
        # Make request with query for intelligent preprocessing
        api_service = get_api_service()
        response = api_service.get_live_sports(country_code, date=parsed_date, query=query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_live_sports: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_get_upcoming_sports(
    country_code: str,
    days_ahead: Optional[int] = None,
    start_date: Optional[str] = None,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get upcoming sports events for a specified number of days ahead
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        days_ahead: Number of days to look ahead (default: 7)
        start_date: Starting date (default: today), can use special values like "today", "tomorrow"
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        UpcomingSportsResponse as dict
    """
    try:
        # Set default value for days_ahead
        if days_ahead is None:
            days_ahead = 7
            
        # Set the start date (default to today)
        if start_date is None:
            start_date = "today"
            
        # Parse the start date
        parsed_start_date = _parse_date(start_date)
        
        # Calculate the end date based on start date + days_ahead
        start_dt = datetime.strptime(parsed_start_date, "%Y-%m-%d")
        end_dt = start_dt + timedelta(days=days_ahead)
        end_date = end_dt.strftime("%Y-%m-%d")
        
        logger.info(f"Getting upcoming sports from {parsed_start_date} to {end_date}")
            
        api_service = get_api_service()
        response = api_service.get_upcoming_sports(
            country_code, 
            start_date=parsed_start_date,
            end_date=end_date,
            query=query
        )
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_upcoming_sports: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_get_past_matches(
    country_code: str,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    days_back: Optional[int] = None,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get past sports matches from a specified date range
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        start_date: Start date for the past period (default: 7 days ago), can use special values like "last week"
        end_date: End date for the past period (default: yesterday), can use special values like "yesterday"
        days_back: Number of days to look back if start_date isn't specified (default: 7)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        TVGuideResponse as dict with past matches
    """
    try:
        # Set default values for date range
        if days_back is None:
            days_back = 7
            
        # Set default end date to yesterday if not specified
        if end_date is None:
            end_date = "yesterday"
            
        # Set default start date to N days ago if not specified
        if start_date is None:
            start_date = f"{days_back} days ago"
            
        # Parse date strings and convert special formats to actual dates
        parsed_start_date = _parse_date(start_date)
        parsed_end_date = _parse_date(end_date)
        
        logger.info(f"Getting past matches from {parsed_start_date} to {parsed_end_date}")
            
        # Create request model using TV guide endpoint with past dates
        request = TVGuideRequest(
            countryCode=country_code,
            startDateTime=parsed_start_date,
            endDateTime=parsed_end_date
        )
        
        # Use TV guide endpoint but specify we're looking for past events
        api_service = get_api_service()
        response = api_service.get_tv_guide(request, query=query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_past_matches: {str(e)}")
        return {
            "status": "error",
            "message": f"Error retrieving past matches: {str(e)}",
            "data": []
        }

def super_sport_get_sport_categories(
    country_code: str,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get sport categories available in the SuperSport API
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        SportCategoryResponse as dict
    """
    try:
        api_service = get_api_service()
        response = api_service.get_sport_categories(country_code, query=query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_sport_categories: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_get_channels(
    country_code: str,
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get channels from the SuperSport API
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        api_service = get_api_service()
        response = api_service.get_channels(country_code, query=query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_get_channels: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_search_programs(
    country_code: str,
    search_query: str,
) -> Dict[str, Any]:
    """
    Search for programs matching the query
    
    Args:
        country_code: Country code (e.g., "za" for South Africa)
        search_query: Text to search for in program titles and descriptions
        
    Returns:
        TVGuideResponse with matched programs as dict
    """
    try:
        api_service = get_api_service()
        response = api_service.search_programs(country_code, search_query)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_search_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def super_sport_query(
    query: str
) -> Dict[str, Any]:
    """
    General query endpoint for the SuperSport API
    
    Args:
        query: Natural language query to process
        
    Returns:
        QueryResponse with results from multiple endpoints as dict
    """
    try:
        # Create query request
        request = QueryRequest(query=query)
        
        # Make the query
        api_service = get_api_service()
        response = api_service.query(request)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in super_sport_query: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "query": query,
            "data": [],
            "endpoints_queried": []
        }

def super_sport_call_api(
    url: str
) -> Dict[str, Any]:
    """
    Call the SuperSport API with a complete URL
    
    Args:
        url: Complete URL including base URL, endpoint path, and query parameters
        
    Returns:
        API response as dict
    """
    try:
        # Process any date placeholders in the URL
        current_date = datetime.now().strftime('%Y-%m-%d')
        processed_url = url.replace('TODAY', current_date)
        processed_url = processed_url.replace('TOMORROW', (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d'))
        processed_url = processed_url.replace('YESTERDAY', (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'))
        
        logger.info(f"Calling SuperSport API with URL: {processed_url}")
        
        # Make the API call
        api_service = get_api_service()
        response = api_service.call_direct_url(processed_url)
        
        # Return response as dict
        return {
            "status": "success",
            "message": "API call successful",
            "url": processed_url,
            "data": response
        }
    except Exception as e:
        logger.error(f"Error in super_sport_call_api: {str(e)}")
        return {
            "status": "error",
            "message": f"Error calling API: {str(e)}",
            "url": url,
            "data": []
        }

# List of API tools for easy reference
API_TOOLS: List[Callable] = [
    super_sport_get_tv_guide,
    super_sport_get_live_sports,
    super_sport_get_upcoming_sports,
    super_sport_get_past_matches,
    super_sport_get_sport_categories,
    super_sport_get_channels,
    super_sport_search_programs,
    super_sport_query,
    super_sport_call_api
] 