"""
Knowledge Graph tools for SuperSport Agent
"""

from typing import Dict, Any, Optional, List, Callable
import traceback

from ..utils.logger import get_logger
from ..services.document_service import DocumentService
from ..services.knowledge_graph_service import KnowledgeGraphService
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.prompts import PromptTemplate
from ..config.settings import MODEL_GEMINI_2_0_FLASH

# Get logger for this module
logger = get_logger(__name__)

# Singleton services
_document_service = None

def get_document_service() -> DocumentService:
    """Get or create the singleton document service"""
    global _document_service
    if _document_service is None:
        _document_service = DocumentService()
    return _document_service

def list_files_in_documents_dir() -> Dict[str, Any]:
    """
    List all available files in the project's documents directory.
    Returns a dictionary with lists of PDF, VTT, and text files.
    """
    try:
        document_service = get_document_service()
        return document_service.list_files()
    except Exception as e:
        logger.error(f"Error in list_files_in_documents_dir: {str(e)}")
        return {
            "status": "error",
            "message": f"Error listing files: {str(e)}"
        }

def process_all_files() -> Dict[str, Any]:
    """
    Process all PDF, VTT, and text files in the documents directory automatically.
    Creates a knowledge graph from the content with focus on SuperSport API documentation.
    Returns a summary of the processing results.
    """
    try:
        # Always process documents and create the knowledge graph, do not check if it exists
        knowledge_graph_service = KnowledgeGraphService()
        logger.info("Starting process_all_files in knowledge_graph_tools...")
        document_service = get_document_service()
        logger.info("Calling document_service.process_all_files()...")
        result = document_service.process_all_files()
        
        logger.info(f"Document processing result status: {result.status}, processed files: {len(result.processed_files) if hasattr(result, 'processed_files') else 0}")
        if hasattr(result, 'content'):
            logger.info(f"Content length: {len(result.content) if result.content else 0} characters")
        else:
            logger.warning("No content attribute in result")
        
        # If processing is successful and we have content, create knowledge graph
        if result.status == "success" and hasattr(result, 'content') and result.content:
            logger.info("Document processing successful with content. Creating SuperSport API knowledge graph...")
            kg_result = knowledge_graph_service.create_knowledge_graph(result.content, source="supersport_api_documentation")
            
            logger.info(f"Knowledge graph creation result: {kg_result.status}")
            
            # Add knowledge graph creation result to the response
            result_dict = result.dict()
            result_dict["knowledge_graph"] = {
                "status": kg_result.status,
                "message": kg_result.message,
                "total_nodes": kg_result.total_nodes,
                "total_relationships": kg_result.total_relationships
            }
            
            logger.info("Returning combined document and SuperSport API knowledge graph result")
            return result_dict
        
        # If no content or error, just return the document processing result
        if result.status != "success":
            logger.warning(f"Document processing was not successful: {result.message}")
        elif not hasattr(result, 'content') or not result.content:
            logger.warning("No content available to create knowledge graph")
        
        logger.info("Returning document processing result only")
        return result.dict()
    except Exception as e:
        stack_trace = traceback.format_exc()
        logger.error(f"Error in process_all_files: {str(e)}\n{stack_trace}")
        return {
            "status": "error",
            "message": f"Error processing files: {str(e)}",
            "file_count": 0
        }

def process_file(file_path: str) -> Dict[str, Any]:
    """
    Process a file (PDF, VTT, text) from a given file path.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Dictionary with status and extracted text
    """
    try:
        document_service = get_document_service()
        result = document_service.process_file(file_path)
        
        # Convert to dict for returning to the agent
        return result.dict()
    except Exception as e:
        logger.error(f"Error in process_file: {str(e)}")
        return {
            "status": "error",
            "message": f"Error processing file: {str(e)}",
            "file_count": 0
        }

def process_and_create_knowledge_graph(file_path: str) -> Dict[str, Any]:
    """
    Process a file and create a knowledge graph from its content.
    Specially designed for processing SuperSport API documentation.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Dictionary with processing and knowledge graph creation status
    """
    try:
        # First process the file
        document_service = get_document_service()
        process_result = document_service.process_file(file_path)
        
        # Check if processing was successful
        if process_result.status != "success" or not hasattr(process_result, 'content') or not process_result.content:
            return process_result.dict()
        
        # Create knowledge graph from the processed content
        knowledge_graph_service = KnowledgeGraphService()
        source = "supersport_api_documentation"
        if file_path.endswith(".md") and "supersport" in file_path.lower():
            source = "supersport_api_documentation"
            
        kg_result = knowledge_graph_service.create_knowledge_graph(process_result.content, source=source)
        
        # Combine results
        result = process_result.dict()
        result["knowledge_graph"] = {
            "status": kg_result.status,
            "message": kg_result.message,
            "total_nodes": kg_result.total_nodes,
            "total_relationships": kg_result.total_relationships
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in process_and_create_knowledge_graph: {str(e)}")
        return {
            "status": "error",
            "message": f"Error processing file and creating knowledge graph: {str(e)}",
            "file_count": 0
        }

def ask_question(question: str) -> Dict[str, Any]:
    """
    Ask a question about the SuperSport API documentation using the knowledge graph.
    Uses a direct Cypher query to construct a complete URL with parameters.
    
    Args:
        question: The question to ask
        
    Returns:
        Dictionary with answer and context
    """
    try:
        logger.info(f"Processing SuperSport API question: {question}")
        
        # Use specified Cypher query to construct complete URL
        direct_query = """
        MATCH (n:Query)-[r:USES_ENDPOINT]->(m:Endpoint)
        WITH m.base_url AS baseUrl, m.name as endpoint, r.parameters AS params
        RETURN baseUrl + endpoint + '?' + params AS completeUrl LIMIT 1
        """
        
        logger.info(f"Using Cypher query: {direct_query}")
        
        # Execute the query against the knowledge graph
        result = execute_cypher_query(direct_query)
        
        # Return the result directly since it's already a dictionary
        return result
        
    except Exception as e:
        logger.error(f"Error in ask_question: {str(e)}\n{traceback.format_exc()}")
        return {
            "status": "error",
            "question": question,
            "answer": f"An error occurred while processing your question about the SuperSport API: {str(e)}"
        }

def create_knowledge_graph_from_text(text: str) -> Dict[str, Any]:
    """
    Create a knowledge graph from text content focusing on SuperSport API documentation.
    
    Args:
        text: Text content to process
        
    Returns:
        Dictionary with knowledge graph creation status
    """
    try:
        knowledge_graph_service = KnowledgeGraphService()
        result = knowledge_graph_service.create_knowledge_graph(text, source="supersport_api_documentation")
        return result.dict()
    except Exception as e:
        logger.error(f"Error in create_knowledge_graph_from_text: {str(e)}")
        return {
            "status": "error",
            "message": f"Error creating knowledge graph: {str(e)}",
            "total_nodes": 0,
            "total_relationships": 0
        }

def get_cypher_query(question: str) -> Dict[str, Any]:
    """
    Get only the Cypher query for a question without executing it.
    
    Args:
        question: The question to generate a Cypher query for
        
    Returns:
        Dictionary with the generated Cypher query
    """
    try:
        logger.info(f"Generating Cypher query for: {question}")
        knowledge_graph_service = KnowledgeGraphService()
        result = knowledge_graph_service.get_cypher_query_only(question)
        return result.dict()
    except Exception as e:
        logger.error(f"Error in get_cypher_query: {str(e)}\n{traceback.format_exc()}")
        return {
            "status": "error",
            "question": question,
            "answer": f"An error occurred while generating a Cypher query: {str(e)}"
        }

def execute_cypher_query(cypher_query: str) -> Dict[str, Any]:
    """
    Execute a Cypher query directly against the knowledge graph database.
    
    Args:
        cypher_query: The Cypher query to execute
        
    Returns:
        Dictionary with the query results
    """
    try:
        logger.info(f"Executing Cypher query: {cypher_query}")
        knowledge_graph_service = KnowledgeGraphService()
        
        # Get Neo4j graph connection
        graph = knowledge_graph_service.get_neo4j_graph()
        if not graph:
            return {
                "status": "error",
                "message": "Neo4j connection not available",
                "results": []
            }
        
        # Execute the query directly
        results = graph.query(cypher_query)
        
        return {
            "status": "success",
            "message": "Cypher query executed successfully",
            "query": cypher_query,
            "results": results
        }
        
    except Exception as e:
        logger.error(f"Error executing Cypher query: {str(e)}\n{traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Error executing Cypher query: {str(e)}",
            "query": cypher_query,
            "results": []
        }

# List of knowledge graph tools for easy reference
KNOWLEDGE_GRAPH_TOOLS: List[Callable] = [
    list_files_in_documents_dir,
    process_file,
    process_all_files,
    process_and_create_knowledge_graph,
    ask_question,
    create_knowledge_graph_from_text,
    get_cypher_query,
    execute_cypher_query
] 