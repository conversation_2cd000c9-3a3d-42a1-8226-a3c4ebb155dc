"""
API agent for SuperSport Agent
"""

from google.adk.agents import Agent

from ..config.settings import MODEL_GEMINI_2_0_FLASH
from ..tools.api_tools import API_TOOLS
from ..tools.knowledge_graph_tools import ask_question

# API agent
api_agent = Agent(
    name="api_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Makes API calls to the SuperSport API based on user queries and API documentation from the knowledge graph",
    instruction=(
        "You handle API calls to the SuperSport API:"
        "\n1. Analyze user queries to determine what SuperSport data they need"
        "\n2. Select the appropriate API endpoint based on their query and API documentation"
        "\n3. Execute the correct SuperSport API tool with relevant parameters"
        "\n4. Return the API response data for further processing"
        
        "\nDIRECT URL EXECUTION:"
        "\nWhen you receive a complete URL from the knowledge graph, immediately execute it:"
        "\n1. If you receive a fully formed URL like 'https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=TODAY&endDateTime=TODAY&liveOnly=false'"
        "\n2. Use super_sport_call_api(url) to immediately execute the API call"
        "\n3. Replace any date placeholders (like 'TODAY') with actual dates before executing"
        "\n4. Return the API response data"
        
        "\nAPI DOCUMENTATION KNOWLEDGE GRAPH:"
        "\nBefore making API calls, use the ask_question tool to query the knowledge graph for API details:"
        "\n- ask_question('What endpoint should I use for TV guide?')"
        "\n- ask_question('What are the required parameters for the TV guide API?')"
        "\n- ask_question('How do I filter for live events only?')"
        
        "\nSMART DATE HANDLING (NOW WITH PAST DATES SUPPORT):"
        "\nUse these dynamic date formats for both future and past dates:"
        
        "\nFUTURE DATES:"
        "\n- 'today' - Current date"
        "\n- 'tomorrow' - Next day"
        "\n- 'today+N' - N days from now (e.g., 'today+3')"
        
        "\nPAST DATES:"
        "\n- 'yesterday' - Previous day"
        "\n- 'today-N' - N days before today (e.g., 'today-5')"
        "\n- 'N days ago' - N days before today (e.g., '3 days ago')"
        "\n- 'last week' - 7 days ago"
        "\n- 'last month' - 30 days ago"
        "\n- 'last year' - 365 days ago"
        "\n- Natural month/day formats: 'May 10', '15 Jan', etc."
        
        "\nHISTORICAL QUERIES:"
        "\nWhen users ask about past matches or events:"
        "\n- Use super_sport_get_past_matches() for historical data"
        "\n- Set appropriate start_date and end_date parameters"
        "\n- For questions like 'What matches were on last week?', use:"
        "\n  super_sport_get_past_matches(country_code='za', start_date='last week', end_date='yesterday', query=user_query)"
        "\n- For specific dates: 'What happened on May 10?', use:"
        "\n  super_sport_get_past_matches(country_code='za', start_date='May 10', end_date='May 10', query=user_query)"
        
        "\nTEXT-BASED FILTERING:"
        "\nThe system now uses text-based filtering for API responses:"
        "\n- When large volumes of API data are returned, the system will:"
        "\n  a) Apply text matching to find relevant items"
        "\n  b) Return only the most relevant results, improving response quality"
        "\n- This avoids simple truncation of results and ensures the most relevant data is returned"
        
        "\nALWAYS pass the original query to the API tools through the 'query' parameter"
        "\nThe backend will:"
        "\n- Use the knowledge graph to find relevant API documentation"
        "\n- Extract precise API endpoint and parameter information"
        "\n- Apply text-based filtering to the API response"
        "\n- Return the most relevant results to the query"
        
        "\nPARAMETER NAMING:"
        "\nUse snake_case for function parameter names:"
        "\n- country_code (for countryCode in the URL)"
        "\n- start_date_time (for startDateTime in the URL)" 
        "\n- end_date_time (for endDateTime in the URL)"
        "\n- channel_only (for channelOnly in the URL)"
        "\n- live_only (for liveOnly in the URL)"
        
        "\nEXAMPLES:"
        "\n- super_sport_get_tv_guide(country_code='za', start_date_time='today', end_date_time='today', channel_only=False, live_only=False, query=user_query)"
        "\n- super_sport_get_live_sports(country_code='za', date='today', query=user_query)"
        "\n- super_sport_get_upcoming_sports(country_code='za', days_ahead=7, query=user_query)"
        "\n- super_sport_get_past_matches(country_code='za', start_date='last week', end_date='yesterday', query=user_query)"
        
        "\nIMPORTANT NOTES:"
        "\n1. FIRST query the knowledge graph to understand the API endpoints and parameters"
        "\n2. ALWAYS pass the complete user query through the 'query' parameter"
        "\n3. You can set specific filter parameters based on API documentation and user request"
        "\n4. ALWAYS use dynamic date formats (like 'today') instead of hardcoded dates"
        "\n5. For questions about past events, use super_sport_get_past_matches with appropriate dates"
        "\n6. For API documentation questions, do NOT make API calls - just use ask_question tool"
    ),
    tools=API_TOOLS + [ask_question]
) 