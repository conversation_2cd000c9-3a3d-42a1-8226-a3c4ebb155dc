import os
import signal
import sys
import logging
import traceback
import json
from PyPDF2 import Pd<PERSON><PERSON><PERSON><PERSON>
import webvtt
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai
from langchain_community.vectorstores import FAISS
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from dotenv import load_dotenv
import tempfile
import re

# Import Starlette and Uvicorn for HTTP server
import uvicorn
from starlette.applications import Starlette
from starlette.requests import Request
from starlette.routing import Route, Mount

# Import MCP server components
from mcp.server.fastmcp import FastMCP
from mcp.server.sse import SseServerTransport

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('rag_mcp_server.log')
    ]
)
logger = logging.getLogger('rag_mcp_server')

# Load environment variables
load_dotenv()
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    logger.error("GOOGLE_API_KEY environment variable is not set")
    sys.exit(1)

genai.configure(api_key=GOOGLE_API_KEY)

# Initialize MCP server
mcp = FastMCP("APIDocumentChat")

# Global variables
processed_documents = False

# Get the current project path and set up directories
current_project_path = os.path.abspath(os.path.dirname(__file__))
documents_dir = os.path.join(current_project_path, "documents")
vector_store_path = os.path.join(current_project_path, "faiss_index")

# Ensure documents directory exists
os.makedirs(documents_dir, exist_ok=True)
logger.info(f"Using documents directory: {documents_dir}")
logger.info(f"Using vector store path: {vector_store_path}")

# Signal handling for graceful shutdown
def handle_signal(sig, frame):
    """Handle interrupt signals gracefully"""
    logger.info(f"Received signal {sig}, shutting down...")
    # Perform any necessary cleanup here
    logger.info("Shutdown complete")
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, handle_signal)  # Ctrl+C
signal.signal(signal.SIGTERM, handle_signal) # Termination signal

# Function to get text from PDF
def get_pdf_text(pdf_path):
    """Extract text from PDF file"""
    text = ""
    try:
        logger.info(f"Processing PDF: {pdf_path}")
        pdf_reader = PdfReader(pdf_path)
        for page in pdf_reader.pages:
            text += page.extract_text()
        logger.info(f"PDF processed successfully, extracted {len(text)} characters")
        return text
    except Exception as e:
        logger.error(f"Error processing PDF {pdf_path}: {e}", exc_info=True)
        return f"Error processing PDF: {str(e)}"

# Function to get text from text file
def get_text_file_content(text_path):
    """Extract text from a text file"""
    try:
        logger.info(f"Processing text file: {text_path}")
        with open(text_path, 'r', encoding='utf-8') as file:
            text = file.read()
        logger.info(f"Text file processed successfully, extracted {len(text)} characters")
        return text
    except UnicodeDecodeError:
        # Try with different encodings if UTF-8 fails
        try:
            with open(text_path, 'r', encoding='latin-1') as file:
                text = file.read()
            logger.info(f"Text file processed successfully with latin-1 encoding, extracted {len(text)} characters")
            return text
        except Exception as e:
            logger.error(f"Error processing text file {text_path} with latin-1 encoding: {e}", exc_info=True)
            return f"Error processing text file: {str(e)}"
    except Exception as e:
        logger.error(f"Error processing text file {text_path}: {e}", exc_info=True)
        return f"Error processing text file: {str(e)}"

# Function to get text from VTT
def get_vtt_text(vtt_path):
    """Extract text from VTT file"""
    text = ""
    try:
        logger.info(f"Processing VTT: {vtt_path}")
        for caption in webvtt.read(vtt_path):
            text += f"{caption.text}\n"
        logger.info(f"VTT processed successfully, extracted {len(text)} characters")
        return text
    except Exception as e:
        logger.error(f"Error processing VTT {vtt_path}: {e}", exc_info=True)
        return f"Error processing VTT: {str(e)}"

# Function to split text into chunks
def get_text_chunks(text):
    """Split text into manageable chunks"""
    try:
        if not isinstance(text, str):
            logger.error(f"Invalid input type to get_text_chunks: expected string, got {type(text)}")
            raise TypeError(f"Expected string, got {type(text)}")
            
        logger.info(f"Splitting text of length {len(text)} into chunks")
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
        chunks = text_splitter.split_text(text)
        logger.info(f"Text split into {len(chunks)} chunks")
        return chunks
    except Exception as e:
        logger.error(f"Error splitting text: {e}", exc_info=True)
        raise

# Function to create vector store
def create_vector_store(text_chunks):
    """Create FAISS vector store from text chunks"""
    try:
        # Validate input type
        if not isinstance(text_chunks, list):
            logger.error(f"Invalid input type to create_vector_store: expected list, got {type(text_chunks)}")
            raise TypeError(f"Expected list of text chunks, got {type(text_chunks)}")
            
        logger.info(f"Creating vector store from {len(text_chunks)} chunks")
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
        
        # Ensure the vector store directory exists
        os.makedirs(os.path.dirname(vector_store_path), exist_ok=True)
        vector_store.save_local(vector_store_path)
        logger.info(f"Vector store created and saved to {vector_store_path}")
        return "Vector store created successfully"
    except Exception as e:
        logger.error(f"Error creating vector store: {e}", exc_info=True)
        raise

# Function to get conversational chain
def get_conversational_chain():
    """Create and return a conversational chain for QA"""
    try:
        logger.info("Creating conversational chain")
        prompt_template = """
You are an intelligent API validation and retrieval agent. Given a user query in natural language, your task is to:

1. Identify the appropriate API(s) to invoke based on the API documentation.
2. Analyze which fields are REQUIRED for the API request based on the documentation.
3. Check if the user has provided all required fields in their query.

FIRST MESSAGE ANALYSIS:
For the user's initial query, check if all required fields are provided:
- If all required fields are present, return a complete response.
- If any required fields are missing, return ONLY the required and missing fields information.

FOLLOW-UP MESSAGE ANALYSIS:
For follow-up messages, carefully check if the user has provided values for previously missing fields:
- Look for patterns like "this is the [field name]: [value]" or "[field name] is [value]"
- When users provide values for missing fields, extract these values and incorporate them into the API call
- Update your internal tracking of missing fields

IF ALL REQUIRED FIELDS ARE PROVIDED (either initially or after follow-ups):
   Return a complete response with:
- **HTTP Method:** The appropriate method (GET, POST, etc.).  
- **Endpoint:** The full URL including the base URL.  
   - **Request Payload:** JSON request body with all parameters properly set (replace any placeholders with actual values).

IF ANY REQUIRED FIELDS ARE STILL MISSING:
   Return ONLY:
   - **Required Fields:** List all required fields for this API call.
   - **Missing Fields:** List any required fields the user hasn't provided with a brief description of what's needed.
   
   DO NOT include HTTP Method, Endpoint, or full Request Payload when fields are missing.

        Context:\n {context}?\n
        Question: \n{question}\n
        Answer:
        """

        model = ChatGoogleGenerativeAI(model="gemini-1.5-pro", temperature=0.3)
        prompt = PromptTemplate(template=prompt_template, input_variables=["context", "question"])
        chain = load_qa_chain(model, chain_type="stuff", prompt=prompt)
        return chain
    except Exception as e:
        logger.error(f"Error creating conversational chain: {e}", exc_info=True)
        raise

# Define MCP tools

@mcp.tool()
def list_files_in_documents_dir() -> dict:
    """
    List all available files in the project's documents directory.
    Returns a dictionary with lists of PDF, VTT, and text files.
    """
    try:
        logger.info(f"Listing files in project documents directory: {documents_dir}")
        if not os.path.exists(documents_dir):
            logger.warning(f"Project documents directory {documents_dir} does not exist")
            return {
                "documents_dir": documents_dir,
                "pdf_files": [], 
                "vtt_files": [], 
                "text_files": []
            }
        
        files = os.listdir(documents_dir)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        vtt_files = [f for f in files if f.lower().endswith('.vtt')]
        text_files = [f for f in files if f.lower().endswith(('.txt', '.md', '.text'))]
        
        logger.info(f"Found {len(pdf_files)} PDF files, {len(vtt_files)} VTT files, and {len(text_files)} text files")
        return {
            "documents_dir": documents_dir,
            "pdf_files": pdf_files, 
            "vtt_files": vtt_files, 
            "text_files": text_files
        }
    except Exception as e:
        logger.error(f"Error listing files: {e}", exc_info=True)
        return {
            "error": str(e), 
            "documents_dir": documents_dir,
            "pdf_files": [], 
            "vtt_files": [], 
            "text_files": []
        }

@mcp.tool()
def process_all_files() -> dict:
    """
    Process all PDF, VTT, and text files in the documents directory automatically.
    Returns a summary of the processing results.
    """
    global processed_documents
    
    try:
        logger.info("Processing all files in documents directory")
        files = list_files_in_documents_dir()
        pdf_files = files.get("pdf_files", [])
        vtt_files = files.get("vtt_files", [])
        text_files = files.get("text_files", [])
        
        all_text = ""
        processed_count = 0
        
        # Process PDF files
        for pdf_file in pdf_files:
            file_path = os.path.join(documents_dir, pdf_file)
            logger.info(f"Processing PDF file: {file_path}")
            text = get_pdf_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
                logger.info(f"Successfully processed PDF: {pdf_file}")
            else:
                logger.warning(f"Failed to process PDF: {pdf_file}")
        
        # Process VTT files
        for vtt_file in vtt_files:
            file_path = os.path.join(documents_dir, vtt_file)
            logger.info(f"Processing VTT file: {file_path}")
            text = get_vtt_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
                logger.info(f"Successfully processed VTT: {vtt_file}")
            else:
                logger.warning(f"Failed to process VTT: {vtt_file}")

        # Process text files
        for text_file in text_files:
            file_path = os.path.join(documents_dir, text_file)
            logger.info(f"Processing text file: {file_path}")
            text = get_text_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
                logger.info(f"Successfully processed text file: {text_file}")
            else:
                logger.warning(f"Failed to process text file: {text_file}")
        
        if all_text:
            logger.info(f"Processing combined text of length {len(all_text)}")
            chunks = get_text_chunks(all_text)
            create_vector_store(chunks)
            processed_documents = True
            logger.info(f"Processed {processed_count} files successfully")
            return {
                "status": "success", 
                "message": f"Processed {processed_count} files and created vector store", 
                "processed_count": processed_count,
                "documents_dir": documents_dir
            }
        else:
            logger.warning("No text was extracted from any files")
            return {
                "status": "warning", 
                "message": "No text was extracted from any files", 
                "processed_count": 0,
                "documents_dir": documents_dir
            }
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"Error processing all files: {str(e)}"
        logger.error(f"{error_msg}\n{tb}")
        return {
            "status": "error", 
            "message": error_msg, 
            "processed_count": 0,
            "documents_dir": documents_dir
        }

@mcp.tool()
def process_file(file_path: str) -> dict:
    """
    Process a file (PDF or VTT) from a given file path.
    This can be a local path or a URL.
    
    Returns a dict with status and extracted text.
    """
    global processed_documents
    
    try:
        logger.info(f"Processing file: {file_path}")
        text = ""
        if file_path.lower().endswith('.pdf'):
            text = get_pdf_text(file_path)
        elif file_path.lower().endswith('.vtt'):
            text = get_vtt_text(file_path)
        elif file_path.lower().endswith(('.txt', '.md', '.text')):
            text = get_text_file_content(file_path)
        else:
            error_msg = "Unsupported file type. Please use PDF, VTT, or text files (.txt, .md, .text)."
            logger.warning(error_msg)
            return {"status": "error", "message": error_msg, "text": ""}
        
        if text and not text.startswith("Error"):
            chunks = get_text_chunks(text)
            result = create_vector_store(chunks)  # Pass chunks directly to avoid type error
            processed_documents = True
            logger.info(f"File processed successfully: {file_path}")
            return {
                "status": "success", 
                "message": "File processed and vector store created", 
                "text": text[:500] + "..." if len(text) > 500 else text
            }
        else:
            logger.error(f"Failed to extract text from file: {file_path}")
            return {"status": "error", "message": text, "text": ""}
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"Error in process_file: {str(e)}"
        logger.error(f"{error_msg}\n{tb}")
        return {"status": "error", "message": error_msg, "text": ""}

@mcp.tool()
def create_vector_store_from_text(text: str) -> dict:
    """
    Process text content into chunks and create a vector store.
    Returns status of the operation.
    """
    global processed_documents
    
    try:
        logger.info(f"Processing text of length {len(text)}")
        chunks = get_text_chunks(text)
        result = create_vector_store(chunks)  # Pass chunks directly
        processed_documents = True
        logger.info("Text processed successfully")
        return {"status": "success", "message": result}
    except Exception as e:
        error_msg = f"Error in process_text: {str(e)}"
        logger.error(error_msg, exc_info=True)
        return {"status": "error", "message": error_msg}

@mcp.tool()
def process_and_create_vector_store(file_path: str) -> dict:
    """
    Process a file and create a vector store from its content in one step.
    This is a convenience function that combines process_file and create_vector_store.
    """
    logger.info(f"Processing and creating vector store for file: {file_path}")
    result = process_file(file_path)
    if result.get("status") == "success":
        logger.info(f"Successfully processed file and created vector store: {file_path}")
        return {"status": "success", "message": "File processed and vector store created successfully"}
    else:
        logger.error(f"Failed to process file and create vector store: {file_path}")
        return result


@mcp.tool()
def ask_question(question: str) -> dict:
    """Ask a question about the processed documents"""
    global processed_documents
    
    try:
        logger.info(f"Received question: {question}")
        
        if not processed_documents:
            error_msg = "No documents have been processed yet. Please process documents first."
            logger.warning(error_msg)
            return {"status": "error", "answer": error_msg}
        
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        docs = vector_store.similarity_search(question)
        logger.info(f"Found {len(docs)} relevant documents")
        
        chain = get_conversational_chain()
        response = chain.invoke({"input_documents": docs, "question": question})
        
        logger.info("Question answered successfully")
        return {"status": "success", "answer": response["output_text"]}
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"Error answering question: {str(e)}"
        logger.error(f"{error_msg}\n{tb}")
        return {"status": "error", "answer": f"Error: {str(e)}"}

# @mcp.tool()
# def search_similar_content(query: str, num_results: int = 3) -> dict:
#     """
#     Ask a question about the processed documents
#     Search for similar content in the vector store based on the query.
#     Returns the most relevant text chunks without processing through QA chain.
    
#     Args:
#         query (str): The search query
#         num_results (int, optional): Number of similar documents to return. Defaults to 3.
    
#     Returns:
#         dict: Dictionary containing status and search results
#     """
#     global processed_documents
    
#     try:
#         logger.info(f"Searching for content similar to: {query}")
        
#         if not processed_documents:
#             error_msg = "No documents have been processed yet. Please process documents first."
#             logger.warning(error_msg)
#             return {"status": "error", "message": error_msg, "results": []}
        
#         embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
#         vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        
#         # Perform similarity search
#         docs = vector_store.similarity_search(query, k=num_results)
        
#         # Extract and format the results
#         results = []
#         for i, doc in enumerate(docs, 1):
#             results.append({
#                 "chunk_number": i,
#                 "content": doc.page_content,
#                 "metadata": doc.metadata
#             })
        
#         logger.info(f"Found {len(results)} similar documents")
#         return {
#             "status": "success",
#             "message": f"Found {len(results)} similar documents",
#             "results": results
#         }
#     except Exception as e:
#         tb = traceback.format_exc()
#         error_msg = f"Error performing similarity search: {str(e)}"
#         logger.error(f"{error_msg}\n{tb}")
#         return {"status": "error", "message": error_msg, "results": []}

@mcp.tool()
def execute_local_api_call(api_response: str) -> dict:
    """
    Execute a local API call based on the response from the LLM.
    This function should ONLY be used after first getting an HTTP URL from the ask_question tool's response.
    DO NOT call this directly without first calling ask_question and getting a response with HTTP method and endpoint.
    
    Args:
        api_response (str): The LLM response containing HTTP method, endpoint, and request payload
        
    Returns:
        dict: Response data from the local JSON file
    """
    try:
        logger.info(f"Executing local API call with response: {api_response}")
        
        # Check if response contains only missing fields information
        required_fields_match = re.search(r"Required Fields:(.*?)(?:\n\n|\Z)", api_response, re.DOTALL)
        missing_fields_match = re.search(r"Missing Fields:(.*?)(?:\n\n|\Z)", api_response, re.DOTALL)
        
        # If we have missing fields mentioned but no HTTP method/endpoint, this is a validation-only response
        if missing_fields_match and not ("HTTP Method:" in api_response and "Endpoint:" in api_response):
            missing_fields_text = missing_fields_match.group(1).strip()
            if missing_fields_text and "None" not in missing_fields_text:
                # Parse the missing fields
                field_pattern = r"- (.*?)(?:\n|$)"
                missing_fields = re.findall(field_pattern, missing_fields_text)
                
                # Get required fields too if available
                required_fields = []
                if required_fields_match:
                    required_fields_text = required_fields_match.group(1).strip()
                    required_fields = re.findall(field_pattern, required_fields_text)
                
                return {
                    "status": "incomplete",
                    "message": "Missing required fields for API call",
                    "required_fields": required_fields,
                    "missing_fields": missing_fields,
                }
        
        # Extract HTTP method, endpoint, and request payload using regex
        http_method_match = re.search(r"HTTP Method: (\w+)", api_response)
        endpoint_match = re.search(r"Endpoint: ([^\n]+)", api_response)
        
        if not http_method_match or not endpoint_match:
            error_msg = "Could not parse API response. Missing HTTP method or endpoint."
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        http_method = http_method_match.group(1)
        endpoint = endpoint_match.group(1)
        
        # Check for missing fields in the full response format
        if missing_fields_match:
            missing_fields_text = missing_fields_match.group(1).strip()
            if missing_fields_text and "None" not in missing_fields_text:
                # Parse the missing fields
                field_pattern = r"- (.*?)(?:\n|$)"
                missing_fields = re.findall(field_pattern, missing_fields_text)
                
                # Get required fields too if available
                required_fields = []
                if required_fields_match:
                    required_fields_text = required_fields_match.group(1).strip()
                    required_fields = re.findall(field_pattern, required_fields_text)
                
                if missing_fields:
                    return {
                        "status": "incomplete",
                        "message": "Missing required fields for API call",
                        "required_fields": required_fields,
                        "missing_fields": missing_fields,
                        "http_method": http_method,
                        "endpoint": endpoint
                    }
        
        # Extract JSON payload if present
        payload = {}
        json_match = re.search(r"```json\s*([\s\S]*?)\s*```", api_response)
        if json_match:
            try:
                payload_text = json_match.group(1)
                
                # Check for required field placeholders
                required_placeholders = re.findall(r"\"<REQUIRED: (.*?)>\"", payload_text)
                if required_placeholders:
                    return {
                        "status": "incomplete",
                        "message": "Payload contains required fields that need user input",
                        "missing_fields": required_placeholders,
                        "http_method": http_method,
                        "endpoint": endpoint,
                        "payload_template": payload_text
                    }
                
                payload = json.loads(payload_text)
            except json.JSONDecodeError as e:
                logger.error(f"Error parsing JSON payload: {e}")
                return {"status": "error", "message": f"Invalid JSON payload: {str(e)}"}
        
        # Determine which local JSON file to use based on the endpoint
        data_dir = os.path.join(current_project_path, "data")
        
        if "/pet" in endpoint:
            data_file = os.path.join(data_dir, "PetData.json")
            data_key = "pets"
        elif "/user" in endpoint:
            data_file = os.path.join(data_dir, "UserData.json")
            data_key = "users"
        elif "/store" in endpoint or "/order" in endpoint:
            data_file = os.path.join(data_dir, "OrderData.json")
            data_key = "orders"
        else:
            error_msg = f"Unknown endpoint: {endpoint}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        # Check if file exists
        if not os.path.exists(data_file):
            error_msg = f"Data file not found: {data_file}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        # Load data from JSON file
        try:
            with open(data_file, 'r') as f:
                file_data = json.load(f)
                if isinstance(file_data, dict) and data_key in file_data:
                    data = file_data[data_key]
                else:
                    data = file_data
        except Exception as e:
            error_msg = f"Error loading data file {data_file}: {str(e)}"
            logger.error(error_msg)
            return {"status": "error", "message": error_msg}
        
        # Process request based on HTTP method
        if http_method == "GET":
            # For GET requests, filter data based on query parameters
            result = data
            
            # If there's an ID in the endpoint, extract and filter by it
            id_match = re.search(r"/(\d+)", endpoint)
            if id_match:
                item_id = int(id_match.group(1))
                result = next((item for item in data if item.get('id') == item_id), None)
                if not result:
                    return {"status": "error", "message": f"Item with ID {item_id} not found"}
            
            # Handle other common query parameters
            if "status" in endpoint:
                status = endpoint.split("status=")[1].split("&")[0]
                result = [item for item in data if item.get('status') == status]
            
            return {"status": "success", "data": result}
            
        elif http_method == "POST":
            # For POST requests, add the item to data
            # In a real implementation, you might want to save this back to the file
            if isinstance(data, list):
                # Generate new ID if not provided
                if not payload.get('id'):
                    max_id = max([item.get('id', 0) for item in data]) if data else 0
                    payload['id'] = max_id + 1
                
                # Check if item with same ID already exists
                existing_item = next((item for item in data if item.get('id') == payload.get('id')), None)
                if existing_item:
                    return {"status": "success", "message": "Item updated", "data": payload}
                else:
                    return {"status": "success", "message": "Item created", "data": payload}
            else:
                return {"status": "success", "message": "Item processed", "data": payload}
            
        elif http_method == "PUT":
            # For PUT requests, update an existing item
            if isinstance(data, list):
                item_id = payload.get('id')
                if not item_id:
                    return {"status": "error", "message": "ID is required for PUT requests"}
                
                existing_item = next((item for item in data if item.get('id') == item_id), None)
                if existing_item:
                    return {"status": "success", "message": "Item updated", "data": payload}
                else:
                    return {"status": "error", "message": f"Item with ID {item_id} not found"}
            else:
                return {"status": "success", "message": "Item updated", "data": payload}
            
        elif http_method == "DELETE":
            # For DELETE requests, remove an item
            id_match = re.search(r"/(\d+)", endpoint)
            if id_match:
                item_id = int(id_match.group(1))
                existing_item = next((item for item in data if item.get('id') == item_id), None)
                if existing_item:
                    return {"status": "success", "message": f"Item with ID {item_id} deleted"}
                else:
                    return {"status": "error", "message": f"Item with ID {item_id} not found"}
            else:
                return {"status": "error", "message": "ID is required for DELETE requests"}
        
        else:
            return {"status": "error", "message": f"Unsupported HTTP method: {http_method}"}
        
    except Exception as e:
        tb = traceback.format_exc()
        error_msg = f"Error executing local API call: {str(e)}"
        logger.error(f"{error_msg}\n{tb}")
        return {"status": "error", "message": error_msg}

# @mcp.tool()
# def complete_api_call(api_response: str, user_provided_values: dict = None) -> dict:
#     """
#     Complete an API call by incorporating user-provided values into the API response.
#     This is useful for handling follow-up responses where users provide missing field values.
    
#     Args:
#         api_response (str): The LLM response containing HTTP method, endpoint, and request payload
#         user_provided_values (dict, optional): Dictionary of user-provided field values
        
#     Returns:
#         dict: Response data from the local JSON file
#     """
#     try:
#         logger.info(f"Completing API call with response: {api_response}")
#         if user_provided_values:
#             logger.info(f"User provided values: {user_provided_values}")
        
#         # Extract HTTP method, endpoint, and request payload using regex
#         http_method_match = re.search(r"HTTP Method: (\w+)", api_response)
#         endpoint_match = re.search(r"Endpoint: ([^\n]+)", api_response)
        
#         if not http_method_match or not endpoint_match:
#             error_msg = "Could not parse API response. Missing HTTP method or endpoint."
#             logger.error(error_msg)
#             return {"status": "error", "message": error_msg}
        
#         http_method = http_method_match.group(1)
#         endpoint = endpoint_match.group(1)
        
#         # Extract JSON payload if present
#         payload = {}
#         json_match = re.search(r"```json\s*([\s\S]*?)\s*```", api_response)
#         if json_match:
#             try:
#                 payload_text = json_match.group(1)
                
#                 # Apply user-provided values to replace placeholders
#                 if user_provided_values:
#                     for field, value in user_provided_values.items():
#                         # Handle nested fields like photoUrls[0]
#                         if "[" in field and "]" in field:
#                             base_field = field.split("[")[0]
#                             index = int(field.split("[")[1].split("]")[0])
#                             pattern = fr'"{base_field}":\s*\[(.*?)\]'
#                             array_match = re.search(pattern, payload_text, re.DOTALL)
                            
#                             if array_match:
#                                 # Get the array content
#                                 array_content = array_match.group(1).strip()
                                
#                                 # Split by comma to get items (this is a simplified approach)
#                                 items = array_content.split(",")
                                
#                                 # Ensure there are enough items
#                                 while len(items) <= index:
#                                     items.append('""')
                                
#                                 # Replace the placeholder value at the specific index
#                                 items[index] = f'"{value}"'
                                
#                                 # Reconstruct the array
#                                 new_array = "[" + ", ".join(items) + "]"
                                
#                                 # Replace in the payload text
#                                 payload_text = re.sub(pattern, f'"{base_field}": {new_array}', payload_text)
#                         else:
#                             # Simple replacement for string fields
#                             pattern = fr'"{field}":\s*".*?"'
#                             replacement = f'"{field}": "{value}"'
#                             payload_text = re.sub(pattern, replacement, payload_text)
                            
#                             # Also try replacing placeholders
#                             placeholder_pattern = fr'"{field}":\s*"<REQUIRED:.*?>"'
#                             payload_text = re.sub(placeholder_pattern, replacement, payload_text)
                
#                 # Check if there are still any required field placeholders
#                 required_placeholders = re.findall(r"\"<REQUIRED: (.*?)>\"", payload_text)
#                 if required_placeholders:
#                     return {
#                         "status": "incomplete",
#                         "message": "Payload still contains required fields that need user input",
#                         "missing_fields": required_placeholders,
#                         "http_method": http_method,
#                         "endpoint": endpoint,
#                         "payload_template": payload_text
#                     }
                
#                 # Parse the updated payload
#                 payload = json.loads(payload_text)
#             except json.JSONDecodeError as e:
#                 logger.error(f"Error parsing JSON payload: {e}")
#                 return {"status": "error", "message": f"Invalid JSON payload: {str(e)}"}
        
#         # Determine which local JSON file to use based on the endpoint
#         data_dir = os.path.join(current_project_path, "data")
        
#         if "/pet" in endpoint:
#             data_file = os.path.join(data_dir, "PetData.json")
#             data_key = "pets"
#         elif "/user" in endpoint:
#             data_file = os.path.join(data_dir, "UserData.json")
#             data_key = "users"
#         elif "/store" in endpoint or "/order" in endpoint:
#             data_file = os.path.join(data_dir, "OrderData.json")
#             data_key = "orders"
#         else:
#             error_msg = f"Unknown endpoint: {endpoint}"
#             logger.error(error_msg)
#             return {"status": "error", "message": error_msg}
        
#         # Check if file exists
#         if not os.path.exists(data_file):
#             error_msg = f"Data file not found: {data_file}"
#             logger.error(error_msg)
#             return {"status": "error", "message": error_msg}
        
#         # Load data from JSON file
#         try:
#             with open(data_file, 'r') as f:
#                 file_data = json.load(f)
#                 if isinstance(file_data, dict) and data_key in file_data:
#                     data = file_data[data_key]
#                 else:
#                     data = file_data
#         except Exception as e:
#             error_msg = f"Error loading data file {data_file}: {str(e)}"
#             logger.error(error_msg)
#             return {"status": "error", "message": error_msg}
            
#         # Process request based on HTTP method
#         if http_method == "GET":
#             # For GET requests, filter data based on query parameters
#             result = data
            
#             # If there's an ID in the endpoint, extract and filter by it
#             id_match = re.search(r"/(\d+)", endpoint)
#             if id_match:
#                 item_id = int(id_match.group(1))
#                 result = next((item for item in data if item.get('id') == item_id), None)
#                 if not result:
#                     return {"status": "error", "message": f"Item with ID {item_id} not found"}
            
#             # Handle other common query parameters
#             if "status" in endpoint:
#                 status = endpoint.split("status=")[1].split("&")[0]
#                 result = [item for item in data if item.get('status') == status]
            
#             return {"status": "success", "data": result}
            
#         elif http_method == "POST":
#             # For POST requests, add the item to data
#             if isinstance(data, list):
#                 # Generate new ID if not provided
#                 if not payload.get('id'):
#                     max_id = max([item.get('id', 0) for item in data]) if data else 0
#                     payload['id'] = max_id + 1
                
#                 # Check if item with same ID already exists
#                 existing_item = next((item for item in data if item.get('id') == payload.get('id')), None)
#                 if existing_item:
#                     return {"status": "success", "message": "Item updated", "data": payload}
#                 else:
#                     # This would be where we'd add to the actual data file in a real implementation
#                     logger.info(f"Would add new item: {payload}")
#                     return {"status": "success", "message": "Item created successfully", "data": payload}
#             else:
#                 return {"status": "success", "message": "Item processed", "data": payload}
            
#         elif http_method == "PUT":
#             # For PUT requests, update an existing item
#             if isinstance(data, list):
#                 item_id = payload.get('id')
#                 if not item_id:
#                     return {"status": "error", "message": "ID is required for PUT requests"}
                
#                 existing_item = next((item for item in data if item.get('id') == item_id), None)
#                 if existing_item:
#                     return {"status": "success", "message": "Item updated", "data": payload}
#                 else:
#                     return {"status": "error", "message": f"Item with ID {item_id} not found"}
#             else:
#                 return {"status": "success", "message": "Item updated", "data": payload}
            
#         elif http_method == "DELETE":
#             # For DELETE requests, remove an item
#             id_match = re.search(r"/(\d+)", endpoint)
#             if id_match:
#                 item_id = int(id_match.group(1))
#                 existing_item = next((item for item in data if item.get('id') == item_id), None)
#                 if existing_item:
#                     return {"status": "success", "message": f"Item with ID {item_id} deleted"}
#                 else:
#                     return {"status": "error", "message": f"Item with ID {item_id} not found"}
#             else:
#                 return {"status": "error", "message": "ID is required for DELETE requests"}
        
#         else:
#             return {"status": "error", "message": f"Unsupported HTTP method: {http_method}"}
        
#     except Exception as e:
#         tb = traceback.format_exc()
#         error_msg = f"Error completing API call: {str(e)}"
#         logger.error(f"{error_msg}\n{tb}")
#         return {"status": "error", "message": error_msg}

# Set up the SSE transport for MCP
sse = SseServerTransport("/messages/")

async def handle_sse(request: Request) -> None:
    """Handle SSE connections for MCP"""
    logger.info("New SSE connection received")
    _server = mcp._mcp_server
    try:
        async with sse.connect_sse(
            request.scope,
            request.receive,
            request._send,
        ) as (reader, writer):
            await _server.run(reader, writer, _server.create_initialization_options())
    except Exception as e:
        logger.error(f"Error handling SSE connection: {e}", exc_info=True)

# Create the Starlette application with routes
app = Starlette(
    debug=True,
    routes=[
        # SSE endpoints
        Route("/sse", endpoint=handle_sse),
        Mount("/messages/", app=sse.handle_post_message),
    ],
)

# Run the application
if __name__ == "__main__":
    try:
        logger.info("Starting MCP server")
        uvicorn.run(app, host="0.0.0.0", port=8000)
    except KeyboardInterrupt:
        logger.info("MCP server stopped by keyboard interrupt")
    except Exception as e:
        logger.error(f"Error running MCP server: {e}", exc_info=True)
        sys.exit(1)
    finally:
        logger.info("MCP server shutdown complete")