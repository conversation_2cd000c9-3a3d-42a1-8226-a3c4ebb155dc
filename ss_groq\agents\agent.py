"""
Agent for SuperSport Knowledge Graph
"""

import google.generativeai as genai
from google.adk.agents import Agent
from google.adk.models.lite_llm import Li<PERSON><PERSON><PERSON>
from typing import Dict, Any

from ..utils.logger import get_logger
from ..config.settings import MODEL_GEMINI_2_0_FLASH, MODEL_GROQ_LLAMA_3_8B_8192, GOOGLE_API_KEY, GROQ_API_KEY
from ..services.knowledge_graph_service import KnowledgeGraphService

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI with API key
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY not set at agent level. Agent functionality will not work properly.")

# Check for GROQ API key
if not GROQ_API_KEY:
    logger.warning("GROQ_API_KEY not set at agent level. GROQ functionality will not work properly.")

# Create the knowledge graph service for use across the agent
knowledge_graph_service = KnowledgeGraphService()

def query_graph(question: str) -> Dict[str, Any]:
    """
    Query the knowledge graph using <PERSON><PERSON><PERSON><PERSON>'s GraphCypherQAChain
    
    Args:
        question: The question to ask about SuperSport data
        
    Returns:
        Dict containing the answer and query details
    """
    return knowledge_graph_service.query_knowledge_graph(question)

# Create a single agent for SuperSport
super_sport_agent = Agent(
    name="super_sport_agent",
    # model=MODEL_GEMINI_2_0_FLASH,
    model=LiteLlm(model=MODEL_GROQ_LLAMA_3_8B_8192, api_key=GROQ_API_KEY),
    description="SuperSport assistant that retrieves information from a Neo4j knowledge graph",
    instruction=(
        "You are a SuperSport assistant that answers questions using a Neo4j knowledge graph. Your role is to:"
        
        "\n\n1. Analyze user questions to understand what sports information they need"
        "\n2. Use the query_graph() function to retrieve relevant information from the knowledge graph"
        "\n3. Present the information in a clear, concise, and helpful format"
        
        "\n\nUSING THE KNOWLEDGE GRAPH:"
        "\n- The Neo4j knowledge graph contains comprehensive SuperSport data"
        "\n- Simply pass the user's question to query_graph() for information retrieval"
        "\n- The function will automatically translate the question to a Cypher query"
        "\n- It then queries the knowledge graph and returns relevant information"
        
        "\n\nQUERY WORKFLOW:"
        "\n1. Call query_graph(question) with the user's exact question"
        "\n2. The function returns a dictionary with:"
        "\n   - answer: The answer to the question"
        "\n   - cypher_query: The Cypher query used to retrieve the data"
        "\n   - context: Additional context from the knowledge graph"
        "\n   - status: Success or error status"
        
        "\n\nRESPONSE FORMATTING:"
        "\n- Present information in a clear, organized manner"
        "\n- Use markdown formatting for better readability"
        "\n- Include relevant details while avoiding information overload"
        "\n- Highlight the most important information"
        "\n- Reference the Cypher query only if it provides helpful context"
        
        "\n\nWhen the knowledge graph doesn't have information:"
        "\n1. Clearly state that the information is not available in the knowledge graph"
        "\n2. Provide any partial information that might be helpful"
        "\n3. Suggest related questions that might yield better results"
    ),
    tools=[query_graph]
)

# Export the agent for ADK compatibility
agent = super_sport_agent 