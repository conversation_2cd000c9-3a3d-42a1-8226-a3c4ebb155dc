"""
IPTV Tools for the IPTV Agent
These tools provide agent access to the IPTV API using proper request and response models
"""

import os
import logging
import sys
import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Callable
from .models import (
    ChannelRequest, ChannelsResponse,
    StreamRequest, StreamsResponse,
    CategoriesResponse, CountriesResponse,
    GuidesResponse,
    QueryRequest, QueryResponse
)
from .iptv_client import IptvApiClient
from .iptv_guide import get_guide_manager, format_guide_entry
from .utils import limit_response_tokens

# Import for vector storage
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS

# Import central logger
from .logger import get_logger

# Get logger for this module
logger = get_logger('iptv_tools')
logger.info("IPTV Tools logging initialized")

# Global IPTV API client instance
_client = None

# Global vector store for API responses
_api_vectorstore = None
_api_vectorstore_path = os.path.join(os.path.abspath(os.path.dirname(__file__)), "api_faiss_index")
os.makedirs(os.path.dirname(_api_vectorstore_path), exist_ok=True)

def get_client() -> IptvApiClient:
    """Get or create the global IPTV API client instance"""
    global _client
    if _client is None:
        _client = IptvApiClient()
    return _client

def get_api_vectorstore():
    """Get or create the global API vector store"""
    global _api_vectorstore
    if _api_vectorstore is None:
        # Check if vector store exists
        if os.path.exists(_api_vectorstore_path) and os.path.isdir(_api_vectorstore_path) and len(os.listdir(_api_vectorstore_path)) > 0:
            embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
            _api_vectorstore = FAISS.load_local(_api_vectorstore_path, embeddings, allow_dangerous_deserialization=True)
            logger.info("Loaded existing API vector store")
        else:
            # Initialize empty vector store
            embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
            _api_vectorstore = FAISS.from_texts(["IPTV API initialization"], embedding=embeddings)
            _api_vectorstore.save_local(_api_vectorstore_path)
            logger.info("Created new API vector store")
    return _api_vectorstore

def add_to_vectorstore(data: Dict[str, Any], metadata: Dict[str, Any] = None):
    """Add API response data to the vector store"""
    try:
        # Convert data to text chunks
        json_str = json.dumps(data, indent=2)
        
        # Create text splitter
        text_splitter = RecursiveCharacterTextSplitter(chunk_size=1000, chunk_overlap=100)
        chunks = text_splitter.split_text(json_str)
        
        # Add metadata to each chunk
        if metadata is None:
            metadata = {}
        
        # Get embeddings and vector store
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vectorstore = get_api_vectorstore()
        
        # Add chunks with metadata
        metadatas = [metadata for _ in chunks]
        vectorstore.add_texts(chunks, metadatas=metadatas)
        
        # Save updated vector store
        vectorstore.save_local(_api_vectorstore_path)
        logger.info(f"Added {len(chunks)} chunks to API vector store with metadata: {metadata}")
        
        return True
    except Exception as e:
        logger.error(f"Error adding to vector store: {str(e)}")
        return False

def search_api_responses(query: str, num_results: int = 5) -> Dict[str, Any]:
    """
    Search for API responses in the vector store based on the query.
    
    Args:
        query (str): The search query
        num_results (int, optional): Number of similar documents to return
        
    Returns:
        dict: Dictionary containing search results
    """
    try:
        vectorstore = get_api_vectorstore()
        docs = vectorstore.similarity_search(query, k=num_results)
        
        results = []
        for doc in docs:
            try:
                # Try to parse JSON content
                content = json.loads(doc.page_content)
                results.append({
                    "content": content,
                    "metadata": doc.metadata
                })
            except json.JSONDecodeError:
                # If not valid JSON, just use the text
                results.append({
                    "content": doc.page_content,
                    "metadata": doc.metadata
                })
        
        return {
            "status": "success",
            "query": query,
            "results": results
        }
    except Exception as e:
        logger.error(f"Error searching API vector store: {str(e)}")
        return {
            "status": "error",
            "message": f"Error searching API responses: {str(e)}",
            "query": query,
            "results": []
        }

# IPTV API Tools

def iptv_get_channels(
    country: Optional[str],
    category: Optional[str],
    language: Optional[str]
) -> Dict[str, Any]:
    """
    Get channels from the IPTV API and store in vector database
    
    Args:
        country: Country code to filter by (can be None)
        category: Category to filter by (can be None)
        language: Language code to filter by (can be None)
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        # Create request model
        request = None
        if country or category or language:
            request = ChannelRequest(
                country=country,
                category=category,
                language=language
            )
        
        # Make request
        client = get_client()
        response = client.get_channels(request)
        
        # Store in vector database with metadata
        metadata = {
            "source": "channels",
            "country": country,
            "category": category,
            "language": language,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_channels: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_channel(channel_id: str) -> Dict[str, Any]:
    """
    Get a specific channel by ID
    
    Args:
        channel_id: The channel ID
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_channel(channel_id)
        
        # Store in vector database with metadata
        metadata = {
            "source": "channel",
            "channel_id": channel_id,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_channel: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_streams(
    channel: Optional[str], 
    quality: Optional[str]
) -> Dict[str, Any]:
    """
    Get streams from the IPTV API
    
    Args:
        channel: Channel ID to filter by (can be None)
        quality: Quality to filter by (can be None)
        
    Returns:
        StreamsResponse as dict
    """
    try:
        # Create request model
        request = None
        if channel or quality:
            request = StreamRequest(
                channel=channel,
                quality=quality
            )
        
        # Make request
        client = get_client()
        response = client.get_streams(request)
        
        # Store in vector database with metadata
        metadata = {
            "source": "streams",
            "channel": channel,
            "quality": quality,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_streams: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_categories() -> Dict[str, Any]:
    """
    Get categories from the IPTV API
    
    Returns:
        CategoriesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_categories()
        
        # Store in vector database with metadata
        metadata = {
            "source": "categories",
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_categories: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_countries() -> Dict[str, Any]:
    """
    Get countries from the IPTV API
    
    Returns:
        CountriesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_countries()
        
        # Store in vector database with metadata
        metadata = {
            "source": "countries",
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_countries: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_guides(
    channel: Optional[str]
) -> Dict[str, Any]:
    """
    Get program guide information from the IPTV API
    
    Args:
        channel: Optional channel ID to filter by
        
    Returns:
        GuidesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_guides(channel)
        
        # Store in vector database with metadata
        metadata = {
            "source": "guides",
            "channel": channel,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_guides: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_current_programs(
    channel: Optional[str]
) -> Dict[str, Any]:
    """
    Get currently airing TV programs
    
    Args:
        channel: Optional channel ID to filter by
        
    Returns:
        GuidesResponse as dict with currently airing programs
    """
    try:
        guide_manager = get_guide_manager()
        response = guide_manager.get_current_programs(channel)
        
        # Store in vector database with metadata
        metadata = {
            "source": "current_programs",
            "channel": channel,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_current_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_upcoming_programs(
    channel: Optional[str], 
    hours_ahead: int
) -> Dict[str, Any]:
    """
    Get upcoming TV programs for the next hours
    
    Args:
        channel: Optional channel ID to filter by
        hours_ahead: Number of hours to look ahead (default: 24)
        
    Returns:
        GuidesResponse as dict with upcoming programs
    """
    try:
        if hours_ahead is None:
            hours_ahead = 24
            
        guide_manager = get_guide_manager()
        response = guide_manager.get_upcoming_programs(channel, hours_ahead)
        
        # Store in vector database with metadata
        metadata = {
            "source": "upcoming_programs",
            "channel": channel,
            "hours_ahead": hours_ahead,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_get_upcoming_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_search_programs(
    query: str,
    channel: Optional[str]
) -> Dict[str, Any]:
    """
    Search for TV programs matching a query
    
    Args:
        query: Search query to match against program titles and descriptions
        channel: Optional channel ID to filter by
        
    Returns:
        GuidesResponse as dict with matching programs
    """
    try:
        guide_manager = get_guide_manager()
        response = guide_manager.search_programs(query, channel)
        
        # Store in vector database with metadata
        metadata = {
            "source": "program_search",
            "query": query,
            "channel": channel,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_search_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_query(query: str) -> Dict[str, Any]:
    """
    Process a natural language query
    
    Args:
        query: The natural language query
        
    Returns:
        QueryResponse as dict
    """
    try:
        # Create request model
        request = QueryRequest(
            query=query,
            max_results=100  # Use larger default
        )
        
        # Make request
        client = get_client()
        response = client.query(request)
        
        # Store in vector database with metadata
        metadata = {
            "source": "natural_language_query",
            "query": query,
            "timestamp": datetime.now().isoformat()
        }
        add_to_vectorstore(response.dict(), metadata)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in iptv_query: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "query": query,
            "data": [],
            "endpoints_queried": []
        }

# List of IPTV tools for the agent to use
IPTV_TOOLS = [
    iptv_get_channels,
    iptv_get_channel,
    iptv_get_streams,
    iptv_get_categories,
    iptv_get_countries,
    iptv_get_guides,
    iptv_get_current_programs,
    iptv_get_upcoming_programs,
    iptv_search_programs,
    iptv_query,
    search_api_responses  # Add the new search function
] 