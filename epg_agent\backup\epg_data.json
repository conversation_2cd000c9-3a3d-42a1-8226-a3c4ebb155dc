{"channels": [{"id": "BBCOne.uk", "name": "BBC One", "alt_names": ["BBC1"], "network": "BBC", "owners": ["British Broadcasting Corporation"], "country": "UK", "subdivision": "UK-ENG", "city": "London", "categories": ["general"], "is_nsfw": false, "launched": "1936-11-02", "closed": null, "replaced_by": null, "website": "https://www.bbc.co.uk/bbcone", "logo": "https://example.com/bbc_one.png"}, {"id": "CNN.us", "name": "CNN", "alt_names": ["Cable News Network"], "network": "CNN", "owners": ["Warner Bros. Discovery"], "country": "US", "subdivision": "US-GA", "city": "Atlanta", "categories": ["news"], "is_nsfw": false, "launched": "1980-06-01", "closed": null, "replaced_by": null, "website": "https://www.cnn.com", "logo": "https://example.com/cnn.png"}], "feeds": [{"channel": "BBCOne.uk", "id": "EastMidlandsHD", "name": "East Midlands HD", "is_main": false, "broadcast_area": ["c/UK"], "timezones": ["Europe/London"], "languages": ["eng"], "format": "1080i"}, {"channel": "CNN.us", "id": "CNNHD", "name": "CNN HD", "is_main": true, "broadcast_area": ["c/US"], "timezones": ["America/New_York"], "languages": ["eng"], "format": "1080i"}], "streams": [{"channel": "BBCOne.uk", "feed": "EastMidlandsHD", "url": "http://example.com/bbc_one_stream.m3u8", "referrer": "http://example.com/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)", "quality": "720p"}, {"channel": "CNN.us", "feed": "CNNHD", "url": "http://example.com/cnn_stream.m3u8", "referrer": "http://example.com/", "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)", "quality": "1080p"}], "categories": [{"id": "general", "name": "General"}, {"id": "news", "name": "News"}, {"id": "documentary", "name": "Documentary"}, {"id": "sports", "name": "Sports"}, {"id": "entertainment", "name": "Entertainment"}], "countries": [{"name": "United Kingdom", "code": "UK", "languages": ["eng"], "flag": "🇬🇧"}, {"name": "United States", "code": "US", "languages": ["eng"], "flag": "🇺🇸"}]}