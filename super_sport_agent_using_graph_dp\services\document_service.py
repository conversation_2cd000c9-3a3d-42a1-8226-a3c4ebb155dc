"""
Document processing service
"""

import os
from pathlib import Path
from typing import List, Dict, Optional, Union
from PyPDF2 import PdfReader
import webvtt
import traceback

from ..utils.logger import get_logger
from ..config.settings import DOCUMENTS_DIR, SUPPORTED_EXTENSIONS
from ..models.agent_models import DocumentProcessingResult

# Get logger for this module
logger = get_logger(__name__)

class DocumentService:
    """Service for processing document files"""
    
    def __init__(self, documents_dir: Optional[Union[str, Path]] = None):
        """
        Initialize the document service
        
        Args:
            documents_dir: Directory to look for documents in. Defaults to the configured value.
        """
        self.documents_dir = Path(documents_dir) if documents_dir else Path(DOCUMENTS_DIR)
        logger.info(f"Initialized document service with directory: {self.documents_dir}")
    
    def list_files(self) -> Dict[str, List[str]]:
        """
        List all available files in the documents directory.
        Returns a dictionary with lists of PDF, VTT, markdown, and text files.
        """
        try:
            files = os.listdir(self.documents_dir)
            pdf_files = [f for f in files if f.lower().endswith(SUPPORTED_EXTENSIONS["pdf"])]
            vtt_files = [f for f in files if f.lower().endswith(SUPPORTED_EXTENSIONS["vtt"])]
            markdown_files = [f for f in files if f.lower().endswith(SUPPORTED_EXTENSIONS["markdown"])]
            
            # Handle text files which can have multiple extensions
            text_extensions = SUPPORTED_EXTENSIONS["text"]
            text_files = []
            for extension in text_extensions:
                text_files.extend([f for f in files if f.lower().endswith(extension)])
            
            return {
                "documents_dir": str(self.documents_dir),
                "pdf_files": pdf_files,
                "vtt_files": vtt_files,
                "text_files": text_files,
                "markdown_files": markdown_files
            }
        except Exception as e:
            logger.error(f"Error listing files: {str(e)}")
            return {
                "error": str(e), 
                "documents_dir": str(self.documents_dir)
            }
    
    def get_pdf_text(self, pdf_path: Union[str, Path]) -> str:
        """
        Extract text from a PDF file
        
        Args:
            pdf_path: Path to the PDF file
            
        Returns:
            Extracted text content
        """
        text = ""
        try:
            pdf_reader = PdfReader(pdf_path)
            for page in pdf_reader.pages:
                text += page.extract_text()
            return text
        except Exception as e:
            error_msg = f"Error processing PDF: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_vtt_text(self, vtt_path: Union[str, Path]) -> str:
        """
        Extract text from a VTT file
        
        Args:
            vtt_path: Path to the VTT file
            
        Returns:
            Extracted text content
        """
        text = ""
        try:
            for caption in webvtt.read(vtt_path):
                text += f"{caption.text}\n"
            return text
        except Exception as e:
            error_msg = f"Error processing VTT: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_text_file_content(self, text_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """
        Read content from a text file
        
        Args:
            text_path: Path to the text file
            encoding: File encoding to use
            
        Returns:
            File content
        """
        try:
            with open(text_path, 'r', encoding=encoding) as file:
                text = file.read()
            return text
        except UnicodeDecodeError:
            try:
                # Try with a different encoding
                with open(text_path, 'r', encoding='latin-1') as file:
                    text = file.read()
                return text
            except Exception as e:
                error_msg = f"Error processing text file with latin-1 encoding: {str(e)}"
                logger.error(error_msg)
                return error_msg
        except Exception as e:
            error_msg = f"Error processing text file: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def get_markdown_file_content(self, markdown_path: Union[str, Path]) -> str:
        """
        Read content from a markdown file
        
        Args:
            markdown_path: Path to the markdown file
            
        Returns:
            File content
        """
        try:
            with open(markdown_path, 'r', encoding='utf-8') as file:
                text = file.read()
            return text
        except Exception as e:
            error_msg = f"Error processing markdown file: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def process_file(self, file_path: Union[str, Path]) -> DocumentProcessingResult:
        """
        Process a file based on its extension
        
        Args:
            file_path: Path to the file to process
            
        Returns:
            DocumentProcessingResult with processing status and content
        """
        try:
            # Convert to Path object
            path = Path(file_path)
            
            # Make absolute path if not already
            if not path.is_absolute():
                path = self.documents_dir / path
            
            # Verify file exists
            if not path.exists():
                return DocumentProcessingResult(
                    status="error",
                    message=f"File not found: {path}",
                    file_count=0
                )
            
            # Process based on file extension
            extension = path.suffix.lower()
            content = ""
            
            if extension == SUPPORTED_EXTENSIONS["pdf"]:
                content = self.get_pdf_text(path)
            elif extension == SUPPORTED_EXTENSIONS["vtt"]:
                content = self.get_vtt_text(path)
            elif extension == SUPPORTED_EXTENSIONS["markdown"]:
                content = self.get_markdown_file_content(path)
            elif extension in SUPPORTED_EXTENSIONS["text"]:
                content = self.get_text_file_content(path)
            else:
                return DocumentProcessingResult(
                    status="error",
                    message=f"Unsupported file type: {extension}",
                    file_count=0
                )
            
            # Check if error occurred during processing
            if content.startswith("Error processing"):
                return DocumentProcessingResult(
                    status="error",
                    message=content,
                    file_count=0
                )
            
            return DocumentProcessingResult(
                status="success",
                message=f"Successfully processed {path.name}",
                processed_files=[str(path.name)],
                file_count=1,
                content=content  # Add additional field to hold the content
            )
            
        except Exception as e:
            logger.error(f"Error processing file: {str(e)}")
            return DocumentProcessingResult(
                status="error",
                message=f"Error processing file: {str(e)}",
                file_count=0
            )
    
    def process_all_files(self) -> DocumentProcessingResult:
        """
        Process all files in the documents directory
        
        Returns:
            DocumentProcessingResult with overall processing status
        """
        try:
            logger.info("Starting to process all files in the documents directory")
            files = self.list_files()
            
            pdf_files = files.get("pdf_files", [])
            vtt_files = files.get("vtt_files", [])
            text_files = files.get("text_files", [])
            markdown_files = files.get("markdown_files", [])
            
            logger.info(f"Found {len(pdf_files)} PDF files, {len(vtt_files)} VTT files, {len(text_files)} text files, and {len(markdown_files)} markdown files")
            
            all_text = ""
            processed_files = []
            processed_count = 0
            
            # Process PDF files
            logger.info("Processing PDF files...")
            for pdf_file in pdf_files:
                file_path = os.path.join(self.documents_dir, pdf_file)
                logger.debug(f"Processing PDF file: {file_path}")
                text = self.get_pdf_text(file_path)
                if not text.startswith("Error"):
                    logger.debug(f"Successfully extracted {len(text)} characters from PDF: {pdf_file}")
                    all_text += text + "\n\n"
                    processed_files.append(pdf_file)
                    processed_count += 1
                else:
                    logger.warning(f"Failed to extract text from PDF: {pdf_file} - {text}")
            
            # Process VTT files
            logger.info("Processing VTT files...")
            for vtt_file in vtt_files:
                file_path = os.path.join(self.documents_dir, vtt_file)
                logger.debug(f"Processing VTT file: {file_path}")
                text = self.get_vtt_text(file_path)
                if not text.startswith("Error"):
                    logger.debug(f"Successfully extracted {len(text)} characters from VTT: {vtt_file}")
                    all_text += text + "\n\n"
                    processed_files.append(vtt_file)
                    processed_count += 1
                else:
                    logger.warning(f"Failed to extract text from VTT: {vtt_file} - {text}")
            
            # Process text files
            logger.info("Processing text files...")
            for text_file in text_files:
                file_path = os.path.join(self.documents_dir, text_file)
                logger.debug(f"Processing text file: {file_path}")
                text = self.get_text_file_content(file_path)
                if not text.startswith("Error"):
                    logger.debug(f"Successfully extracted {len(text)} characters from text file: {text_file}")
                    all_text += text + "\n\n"
                    processed_files.append(text_file)
                    processed_count += 1
                else:
                    logger.warning(f"Failed to extract text from text file: {text_file} - {text}")
            
            # Process markdown files
            logger.info("Processing markdown files...")
            for md_file in markdown_files:
                file_path = os.path.join(self.documents_dir, md_file)
                logger.debug(f"Processing markdown file: {file_path}")
                text = self.get_markdown_file_content(file_path)
                if not text.startswith("Error"):
                    logger.debug(f"Successfully extracted {len(text)} characters from markdown: {md_file}")
                    all_text += text + "\n\n"
                    processed_files.append(md_file)
                    processed_count += 1
                else:
                    logger.warning(f"Failed to extract text from markdown: {md_file} - {text}")
            
            logger.info(f"Finished processing files. Total processed: {processed_count}, Total text length: {len(all_text)}")
            
            if all_text and processed_count > 0:
                logger.info(f"Successfully processed {processed_count} files with content length {len(all_text)}")
                return DocumentProcessingResult(
                    status="success",
                    message=f"Processed {processed_count} files",
                    processed_files=processed_files,
                    file_count=processed_count,
                    content=all_text  # Include the combined content
                )
            else:
                logger.warning("No text was extracted from any files")
                return DocumentProcessingResult(
                    status="warning",
                    message="No text was extracted from any files",
                    processed_files=[],
                    file_count=0
                )
                
        except Exception as e:
            stack_trace = traceback.format_exc()
            logger.error(f"Error processing all files: {str(e)}\n{stack_trace}")
            return DocumentProcessingResult(
                status="error",
                message=f"Error processing all files: {str(e)}",
                processed_files=[],
                file_count=0
            ) 