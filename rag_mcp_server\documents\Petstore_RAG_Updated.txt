# Petstore API Documentation for RAG Applications

## Overview
This documentation provides details about the Swagger Petstore API's GET methods, optimized for Retrieval Augmented Generation (RAG) applications. The Petstore API follows RESTful principles and allows you to retrieve data about pets, orders, and users.

- **Base URL**: `https://petstore.swagger.io/v2`
- **API Documentation**: `https://petstore.swagger.io/v2/swagger.json`
- **Version**: 1.0.7
- **OAS Version**: 2.0
- **Schemes**: https, http
- **Authorization**: API Key (`special-key`)
- **License**: Apache 2.0

## API Sections

The API is organized into three main sections:

1. **Pet**: Everything about pets
2. **Store**: Access to Petstore orders
3. **User**: Operations about users

## Pet API

### Find pets by status
- **Endpoint**: `GET /pet/findByStatus`
- **Description**: Finds pets by their status. This is useful for finding available pets for purchase, pending pets (those in the process of being sold), or sold pets.
- **Parameters**:
  - `status` (query): Status values to filter by
    - Available values: `available`, `pending`, `sold`
    - Default: `available`
- **Response**: 
  - 200: successful operation
  - Response body:
```json
[
  {
    "id": 10,
    "category": {
      "id": 1,
      "name": "Dogs"
    },
    "name": "Buddy",
    "photoUrls": [
      "https://example.com/dog1.jpg"
    ],
    "tags": [
      {
        "id": 1,
        "name": "friendly"
      }
    ],
    "status": "available"
  },
  {
    "id": 11,
    "category": {
      "id": 1,
      "name": "Dogs"
    },
    "name": "Max",
    "photoUrls": [
      "https://example.com/dog2.jpg"
    ],
    "tags": [
      {
        "id": 2,
        "name": "trained"
      }
    ],
    "status": "available"
  }
]
```
- **Relationships**:
  - Results can be used with `GET /pet/{petId}` to get more details about a specific pet
  - Available pets can be used with `GET /store/order/{orderId}` to check order status
- **Usage Context**: 
  - Primary method for browsing pets by availability status
  - Used in inventory management and customer-facing pet browsing functionality

### Find pets by tags
- **Endpoint**: `GET /pet/findByTags`
- **Description**: Finds pets based on their tags. Tags can represent characteristics like "friendly", "trained", or "vaccinated". This endpoint allows searching for pets with specific attributes.
- **Parameters**:
  - `tags` (query): Tags to filter by (can specify multiple)
- **Response**: 
  - 200: successful operation
  - Response body:
```json
[
  {
    "id": 10,
    "category": {
      "id": 1,
      "name": "Dogs"
    },
    "name": "Buddy",
    "photoUrls": [
      "https://example.com/dog1.jpg"
    ],
    "tags": [
      {
        "id": 1,
        "name": "friendly"
      }
    ],
    "status": "available"
  },
  {
    "id": 15,
    "category": {
      "id": 2,
      "name": "Cats"
    },
    "name": "Whiskers",
    "photoUrls": [
      "https://example.com/cat1.jpg"
    ],
    "tags": [
      {
        "id": 1,
        "name": "friendly"
      }
    ],
    "status": "pending"
  }
]
```
- **Relationships**:
  - Complements `GET /pet/findByStatus` by offering attribute-based filtering
  - Results can be used with `GET /pet/{petId}` to get specific pet details
- **Usage Context**: 
  - Used for advanced pet search functionality
  - Helps customers find pets with desired characteristics

### Find pet by ID
- **Endpoint**: `GET /pet/{petId}`
- **Description**: Returns detailed information about a single pet by its ID. This is the primary method to get comprehensive information about a specific pet.
- **Parameters**:
  - `petId` (path): ID of the pet to retrieve
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "id": 10,
  "category": {
    "id": 1,
    "name": "Dogs"
  },
  "name": "Buddy",
  "photoUrls": [
    "https://example.com/dog1.jpg"
  ],
  "tags": [
    {
      "id": 1,
      "name": "friendly"
    }
  ],
  "status": "available"
}
```
- **Relationships**:
  - Typically used after `GET /pet/findByStatus` or `GET /pet/findByTags` to get full details
  - Information from this endpoint can be used with `GET /store/inventory` to understand availability context
- **Usage Context**: 
  - Pet detail pages in applications
  - Verification before purchase or adoption
  - Administrative review of pet records

## Store API

### Returns pet inventories by status
- **Endpoint**: `GET /store/inventory`
- **Description**: Returns a map of status codes to quantities, providing an overview of the pet inventory by status. This endpoint gives a high-level view of stock levels across availability states.
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "available": 17,
  "pending": 3,
  "sold": 12
}
```
- **Relationships**:
  - Provides context for `GET /pet/findByStatus` results
  - Helps determine if inventory levels need adjustment
- **Usage Context**: 
  - Inventory management dashboard
  - Business analytics about pet availability
  - Determining whether more pets need to be added to inventory

### Find purchase order by ID
- **Endpoint**: `GET /store/order/{orderId}`
- **Description**: Returns information about a specific purchase order. This endpoint provides details about an order, including the pet ID, quantity, and shipment status.
- **Parameters**:
  - `orderId` (path): ID of the order to retrieve
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "id": 5,
  "petId": 10,
  "quantity": 1,
  "shipDate": "2023-06-10T17:32:28.000Z",
  "status": "placed",
  "complete": false
}
```
- **Relationships**:
  - Connected to `GET /pet/{petId}` to get details about the ordered pet
  - Can be referenced with `GET /store/inventory` to understand order impact on inventory
- **Usage Context**: 
  - Order tracking functionality
  - Order history for users
  - Administrative review of sales

## User API

### Get user by user name
- **Endpoint**: `GET /user/{username}`
- **Description**: Retrieves user information by username. This endpoint provides personal information and account details for a specific user.
- **Parameters**:
  - `username` (path): The username of the user to retrieve
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "id": 100,
  "username": "johndoe",
  "firstName": "John",
  "lastName": "Doe",
  "email": "<EMAIL>",
  "phone": "**********",
  "userStatus": 1
}
```
- **Relationships**:
  - User information is needed when viewing `GET /store/order/{orderId}` to connect orders to users
  - Can be cross-referenced with pet information from `GET /pet/{petId}` for ownership records
- **Usage Context**: 
  - User profile pages
  - Account management
  - Administrative user management

### Logs user into the system
- **Endpoint**: `GET /user/login`
- **Description**: Authenticates a user and creates a session. This endpoint validates user credentials and returns a session token for authenticated operations.
- **Parameters**:
  - `username` (query): The username for login
  - `password` (query): The password for login
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "code": 200,
  "type": "unknown",
  "message": "logged in user session:**********"
}
```
- **Relationships**:
  - Successful authentication required before using other endpoints that require user context
  - Establishes a session that ends with `GET /user/logout`
- **Usage Context**: 
  - User authentication flow
  - Secure access to personal information and orders
  - Session management

### Logs out current logged in user session
- **Endpoint**: `GET /user/logout`
- **Description**: Ends the current user session. This endpoint invalidates the user's authentication token and terminates the session.
- **Response**: 
  - 200: successful operation
  - Response body:
```json
{
  "code": 200,
  "type": "unknown",
  "message": "User logged out"
}
```
- **Relationships**:
  - Terminates the session established by `GET /user/login`
  - Typically used after completing user-specific operations
- **Usage Context**: 
  - User session termination
  - Security best practice
  - Session cleanup

## API Relationships and Workflows

### Pet Discovery and Information Workflow
1. **GET /store/inventory** → **GET /pet/findByStatus**
   - **Description**: Check overall inventory levels, then find available pets
   - **Data Flow**: Inventory counts → List of available pets

2. **GET /pet/findByStatus** → **GET /pet/findByTags**
   - **Description**: Find pets by availability, then filter further by desired attributes
   - **Data Flow**: Status-filtered pets → Tag-filtered pets

3. **GET /pet/findByTags** → **GET /pet/{petId}**
   - **Description**: Find pets with desired attributes, then get full details about a specific pet
   - **Data Flow**: Tag-filtered pets → Single pet details

### Order and Inventory Workflow
1. **GET /store/inventory** → **GET /store/order/{orderId}**
   - **Description**: Check inventory levels, then examine specific order details
   - **Data Flow**: Inventory counts → Order details

2. **GET /store/order/{orderId}** → **GET /pet/{petId}**
   - **Description**: Check order details, then get information about the ordered pet
   - **Data Flow**: Order information → Pet details

### User Authentication and Information Workflow
1. **GET /user/login** → **GET /user/{username}**
   - **Description**: Authenticate as a user, then retrieve full user profile information
   - **Data Flow**: Authentication → User details

2. **GET /user/{username}** → **GET /store/order/{orderId}**
   - **Description**: Check user details, then view their order information
   - **Data Flow**: User details → Order information

3. **GET /user/login** → **GET /pet/findByStatus** → **GET /pet/{petId}** → **GET /store/order/{orderId}** → **GET /user/logout**
   - **Description**: Complete user session flow from login to logout
   - **Data Flow**: Authentication → Pet browsing → Pet selection → Order checking → Session termination

## Petstore API Mapping

This section systematically maps the relationships between all Petstore GET APIs, exploring possible API combinations and the workflows they enable.

### Single API Operations

#### Pet GET APIs - Single Operations

1. **GET /pet/findByStatus**
   - **Operation**: Find pets by status
   - **Standalone Usage**: Query pets by availability
   - **Input Dependencies**: None (optional status parameter)
   - **Output Dependencies**: Returns pet IDs that can be used by other APIs

2. **GET /pet/findByTags**
   - **Operation**: Find pets by tags
   - **Standalone Usage**: Query pets by tag categories
   - **Input Dependencies**: None (optional tags parameter)
   - **Output Dependencies**: Returns pet IDs that can be used by other APIs

3. **GET /pet/{petId}**
   - **Operation**: Get pet by ID
   - **Standalone Usage**: Retrieve pet details
   - **Input Dependencies**: Requires pet ID
   - **Output Dependencies**: None (terminal operation)

#### Store GET APIs - Single Operations

4. **GET /store/inventory**
   - **Operation**: Get inventory counts
   - **Standalone Usage**: System inventory check
   - **Input Dependencies**: None
   - **Output Dependencies**: None (terminal operation)

5. **GET /store/order/{orderId}**
   - **Operation**: Find order by ID
   - **Standalone Usage**: Check order details
   - **Input Dependencies**: Requires order ID
   - **Output Dependencies**: None (terminal operation)

#### User GET APIs - Single Operations

6. **GET /user/{username}**
   - **Operation**: Get user by username
   - **Standalone Usage**: User profile lookup
   - **Input Dependencies**: Requires username
   - **Output Dependencies**: None (terminal operation)

7. **GET /user/login**
   - **Operation**: User login
   - **Standalone Usage**: Authentication
   - **Input Dependencies**: Requires username/password
   - **Output Dependencies**: Creates user session

8. **GET /user/logout**
   - **Operation**: User logout
   - **Standalone Usage**: End session
   - **Input Dependencies**: Active session
   - **Output Dependencies**: Terminates user session

### Two-API Combinations

#### Pet + Pet API Combinations

9. **GET /pet/findByStatus → GET /pet/{petId}**
   - **Workflow**: Pet Search and Detail Retrieval
   - **Description**: Find pets by status and then get details of a specific one
   - **Data Flow**: Pet list → Specific pet details

10. **GET /pet/findByTags → GET /pet/{petId}**
    - **Workflow**: Tag-Based Search and Detail Retrieval
    - **Description**: Find pets by tags and then get details of a specific one
    - **Data Flow**: Tagged pet list → Specific pet details

11. **GET /pet/findByStatus → GET /pet/findByTags**
    - **Workflow**: Status-Based Search with Tag Refinement
    - **Description**: Find pets by status and then filter by tags
    - **Data Flow**: Status-filtered list → Tag-filtered list

#### Pet + Store API Combinations

12. **GET /pet/findByStatus → GET /store/inventory**
    - **Workflow**: Available Pet Search and Inventory Check
    - **Description**: Find available pets and check inventory statistics
    - **Data Flow**: Available pets → Inventory verification

13. **GET /pet/{petId} → GET /store/order/{orderId}**
    - **Workflow**: Pet Verification and Order Check
    - **Description**: Verify pet details then check an order for that pet
    - **Data Flow**: Pet confirmation → Order details

14. **GET /store/inventory → GET /pet/findByStatus**
    - **Workflow**: Inventory Check and Pet Search
    - **Description**: Check inventory levels then search for available pets
    - **Data Flow**: Inventory assessment → Available pet search

15. **GET /store/order/{orderId} → GET /pet/{petId}**
    - **Workflow**: Order Details and Pet Verification
    - **Description**: Check order details and then verify the associated pet
    - **Data Flow**: Order information → Pet details

#### Pet + User API Combinations

16. **GET /user/{username} → GET /pet/findByStatus**
    - **Workflow**: User Verification and Pet Browsing
    - **Description**: Verify user credentials before showing available pets
    - **Data Flow**: User validation → Pet browsing

17. **GET /user/login → GET /pet/findByStatus**
    - **Workflow**: User Authentication and Pet Browsing
    - **Description**: User logs in and browses available pets
    - **Data Flow**: Authentication → Pet browsing

18. **GET /user/{username} → GET /pet/{petId}**
    - **Workflow**: User Verification and Pet Detail View
    - **Description**: Verify user credentials before showing pet details
    - **Data Flow**: User validation → Pet details

#### Store + Store API Combinations

19. **GET /store/inventory → GET /store/order/{orderId}**
    - **Workflow**: Inventory Check and Order Verification
    - **Description**: Check inventory before viewing specific order
    - **Data Flow**: Inventory confirmation → Order details

#### Store + User API Combinations

20. **GET /user/{username} → GET /store/order/{orderId}**
    - **Workflow**: User Verification and Order History Check
    - **Description**: Verify user credentials and check their order
    - **Data Flow**: User validation → Order history retrieval

21. **GET /user/{username} → GET /store/inventory**
    - **Workflow**: User Verification and Inventory Check
    - **Description**: Verify store administrator before showing inventory
    - **Data Flow**: Admin validation → Inventory access

#### User + User API Combinations

22. **GET /user/login → GET /user/{username}**
    - **Workflow**: Authentication and Profile Retrieval
    - **Description**: User logs in and checks their profile details
    - **Data Flow**: Authentication → Profile information

23. **GET /user/login → GET /user/logout**
    - **Workflow**: Complete Authentication Cycle
    - **Description**: User logs in and then logs out
    - **Data Flow**: Login → Logout

### Three-API Combinations

#### Pet Management Workflows

24. **GET /pet/findByStatus → GET /pet/findByTags → GET /pet/{petId}**
    - **Workflow**: Progressive Pet Search and Detail Retrieval
    - **Description**: Find pets by status, filter by tags, check specific pet details
    - **Data Flow**: Status search → Tag filter → Detailed information

25. **GET /pet/findByStatus → GET /pet/{petId} → GET /store/inventory**
    - **Workflow**: Pet Selection, Verification, and Inventory Context
    - **Description**: Find available pets, check details of specific pet, view inventory context
    - **Data Flow**: Pet search → Pet verification → Inventory context

#### Order and Inventory Workflows

26. **GET /store/inventory → GET /pet/findByStatus → GET /pet/{petId}**
    - **Workflow**: Inventory Assessment, Pet Search, and Selection
    - **Description**: Check inventory levels, find available pets, view specific pet details
    - **Data Flow**: Inventory check → Pet search → Pet selection

27. **GET /store/order/{orderId} → GET /pet/{petId} → GET /store/inventory**
    - **Workflow**: Order Verification, Pet Check, and Inventory Context
    - **Description**: Check order details, verify ordered pet, view inventory context
    - **Data Flow**: Order check → Pet verification → Inventory context

#### User and Order Workflows

28. **GET /user/login → GET /user/{username} → GET /store/order/{orderId}**
    - **Workflow**: Authentication, Profile Verification, and Order History
    - **Description**: Login, check user profile, view order history
    - **Data Flow**: Authentication → Profile check → Order history

29. **GET /user/login → GET /pet/findByStatus → GET /pet/{petId}**
    - **Workflow**: Authentication, Pet Browsing, and Selection
    - **Description**: Login, browse available pets, view specific pet details
    - **Data Flow**: Authentication → Pet browsing → Pet selection

30. **GET /user/{username} → GET /store/order/{orderId} → GET /pet/{petId}**
    - **Workflow**: User Verification, Order Check, and Pet Details
    - **Description**: Verify user, check their order, view details of ordered pet
    - **Data Flow**: User verification → Order check → Pet details

### Four-API Combinations

31. **GET /user/login → GET /pet/findByStatus → GET /pet/{petId} → GET /store/order/{orderId}**
    - **Workflow**: Authentication, Pet Selection, and Order Verification
    - **Description**: Login, browse pets, select specific pet, check order for that pet
    - **Data Flow**: Authentication → Pet browsing → Pet selection → Order verification

32. **GET /store/inventory → GET /pet/findByStatus → GET /pet/findByTags → GET /pet/{petId}**
    - **Workflow**: Complete Inventory and Pet Selection Process
    - **Description**: Check inventory, find available pets, filter by tags, view specific pet
    - **Data Flow**: Inventory check → Status filter → Tag filter → Pet details

33. **GET /user/{username} → GET /store/inventory → GET /pet/findByStatus → GET /pet/{petId}**
    - **Workflow**: User Verification, Inventory Check, and Pet Selection
    - **Description**: Verify user, check inventory, browse available pets, view specific pet
    - **Data Flow**: User verification → Inventory check → Pet browsing → Pet selection

### Five-API Combinations

34. **GET /user/login → GET /user/{username} → GET /store/inventory → GET /pet/findByStatus → GET /pet/{petId}**
    - **Workflow**: Complete User-to-Pet Selection Flow
    - **Description**: Login, verify profile, check inventory, browse pets, select specific pet
    - **Data Flow**: Authentication → Profile verification → Inventory check → Pet browsing → Pet selection

35. **GET /user/login → GET /pet/findByStatus → GET /pet/{petId} → GET /store/order/{orderId} → GET /user/logout**
    - **Workflow**: Complete User Session for Pet Selection and Order Verification
    - **Description**: Login, browse pets, select specific pet, check order, logout
    - **Data Flow**: Authentication → Pet browsing → Pet selection → Order verification → Session termination

### Data Relationships and Entity Connections

The Petstore API has several key entity relationships that are important to understand:

1. **Pet-Category Relationship**: Each pet belongs to exactly one category (one-to-many)
   - A category can have multiple pets
   - A pet belongs to only one category

2. **Pet-Tag Relationship**: Pets can have multiple tags (many-to-many)
   - A pet can have multiple tags
   - A tag can be applied to multiple pets

3. **Pet-Order Relationship**: Pets can be referenced in orders (one-to-many)
   - A pet can be in multiple orders (over time)
   - Each order line references exactly one pet

4. **User-Order Relationship**: Users can place multiple orders (one-to-many)
   - A user can place multiple orders
   - Each order is associated with one user

Understanding these relationships helps properly interpret and generate responses about the connections between different entities in the system. 