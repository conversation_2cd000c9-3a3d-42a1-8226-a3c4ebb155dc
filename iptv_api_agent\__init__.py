"""
IPTV Agent Package
-----------------

An intelligent agent for processing IPTV API documentation and answering questions
by automatically identifying the exact APIs to call and executing them without user intervention.
"""

from .iptv_agent import (
    iptv_agent,
    api_handler,
    list_files_in_documents_dir,
    process_all_files,
    process_file,
    create_vector_store_from_text,
    process_and_create_vector_store,
    search_similar_content,
    auto_process_documents
)

from .iptv_tools import (
    iptv_get_channels,
    iptv_get_channel,
    iptv_get_streams,
    iptv_get_categories,
    iptv_get_countries,
    iptv_query,
    IPTV_TOOLS
)

# Expose iptv_agent as agent for ADK compatibility
agent = iptv_agent

# Document Chat API mapping for MCP tools integration
mcp_APIDocumentChat_list_files_in_documents_dir = list_files_in_documents_dir
mcp_APIDocumentChat_process_all_files = process_all_files
mcp_APIDocumentChat_process_file = process_file
mcp_APIDocumentChat_create_vector_store_from_text = create_vector_store_from_text
mcp_APIDocumentChat_process_and_create_vector_store = process_and_create_vector_store
mcp_APIDocumentChat_search_similar_content = search_similar_content

# Auto-initialize on import
auto_process_documents() 