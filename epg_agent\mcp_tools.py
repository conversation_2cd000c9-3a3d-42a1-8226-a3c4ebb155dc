"""
MCP Tools for EPG API
These tools provide agent access to the EPG API using proper request and response models
"""

import os
import logging
import sys
from datetime import datetime
from typing import Optional, List, Dict, Any, Callable
from .models import (
    ChannelRequest, ChannelsResponse,
    FeedRequest, FeedsResponse,
    StreamRequest, StreamsResponse,
    CategoriesResponse, LanguagesResponse, CountriesResponse,
    SubdivisionsResponse, RegionsResponse, TimezonesResponse, BlocklistResponse,
    GuidesResponse,
    QueryRequest, QueryResponse
)
from .epg_client import EpgApiClient

# Set up logging
current_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(current_dir, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Create a timestamp for the log file
log_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(logs_dir, f"mcp_tools_{log_timestamp}.log")

# Configure logger
logger = logging.getLogger('epg_mcp_tools')
logger.setLevel(logging.DEBUG)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add the file handler to the logger
logger.addHandler(file_handler)

# Also add a console handler for immediate feedback
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

logger.info(f"MCP Tools logging initialized. Log file: {log_file}")

# Global EPG API client instance
_client = None

def get_client() -> EpgApiClient:
    """Get or create the global EPG API client instance"""
    global _client
    if _client is None:
        _client = EpgApiClient()
    return _client

# EPG API MCP Tools

def mcp_epg_get_channels(
    country: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get channels from the EPG API
    
    Args:
        country: Optional country code to filter by
        category: Optional category to filter by
        language: Optional language code to filter by
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        # Create request model
        request = None
        if country or category or language:
            request = ChannelRequest(
                country=country,
                category=category,
                language=language
            )
        
        # Make request
        client = get_client()
        response = client.get_channels(request)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_channels: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_channel(channel_id: str) -> Dict[str, Any]:
    """
    Get a specific channel by ID
    
    Args:
        channel_id: The channel ID
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_channel(channel_id)
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_channel: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_feeds(
    channel: Optional[str] = None,
    region: Optional[str] = None,
    country: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get feeds from the EPG API
    
    Args:
        channel: Optional channel ID to filter by
        region: Optional region code to filter by
        country: Optional country code to filter by
        
    Returns:
        FeedsResponse as dict
    """
    try:
        # Create request model
        request = None
        if channel or region or country:
            request = FeedRequest(
                channel=channel,
                region=region,
                country=country
            )
        
        # Make request
        client = get_client()
        response = client.get_feeds(request)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_feeds: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_feed(feed_id: str) -> Dict[str, Any]:
    """
    Get a specific feed by ID
    
    Args:
        feed_id: The feed ID
        
    Returns:
        FeedsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_feed(feed_id)
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_feed: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_streams(channel: Optional[str] = None, quality: Optional[str] = None) -> Dict[str, Any]:
    """
    Get streams from the EPG API
    
    Args:
        channel: Optional channel ID to filter by
        quality: Optional quality to filter by
        
    Returns:
        StreamsResponse as dict
    """
    try:
        # Create request model
        request = None
        if channel or quality:
            request = StreamRequest(
                channel=channel,
                quality=quality
            )
        
        # Make request
        client = get_client()
        response = client.get_streams(request)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_streams: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_categories() -> Dict[str, Any]:
    """
    Get categories from the EPG API
    
    Returns:
        CategoriesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_categories()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_categories: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_languages() -> Dict[str, Any]:
    """
    Get languages from the EPG API
    
    Returns:
        LanguagesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_languages()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_languages: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_countries() -> Dict[str, Any]:
    """
    Get countries from the EPG API
    
    Returns:
        CountriesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_countries()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_countries: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_subdivisions() -> Dict[str, Any]:
    """
    Get subdivisions from the EPG API
    
    Returns:
        SubdivisionsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_subdivisions()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_subdivisions: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_regions() -> Dict[str, Any]:
    """
    Get regions from the EPG API
    
    Returns:
        RegionsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_regions()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_regions: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_timezones() -> Dict[str, Any]:
    """
    Get timezones from the EPG API
    
    Returns:
        TimezonesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_timezones()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_timezones: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_blocklist() -> Dict[str, Any]:
    """
    Get blocklist from the EPG API
    
    Returns:
        BlocklistResponse as dict
    """
    try:
        client = get_client()
        response = client.get_blocklist()
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_blocklist: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_get_guides(channel: Optional[str] = None) -> Dict[str, Any]:
    """
    Get program guide information from the EPG API
    
    Args:
        channel: Optional channel ID to filter by
        
    Returns:
        GuidesResponse as dict
    """
    try:
        client = get_client()
        response = client.get_guides(channel)
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_get_guides: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def mcp_epg_query(query: str, max_results: int = 10) -> Dict[str, Any]:
    """
    Process a natural language query about EPG data
    
    Args:
        query: Natural language query
        max_results: Maximum number of results to return per endpoint
        
    Returns:
        QueryResponse as dict
    """
    try:
        # Create request model
        request = QueryRequest(
            query=query,
            max_results=max_results
        )
        
        # Make request
        client = get_client()
        response = client.query(request)
        
        # Return response as dict
        return response.dict()
    except Exception as e:
        logger.error(f"Error in mcp_epg_query: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "query": query,
            "data": []
        }

# List of all available MCP tools
EPG_MCP_TOOLS = [
    mcp_epg_get_channels,
    mcp_epg_get_channel,
    mcp_epg_get_feeds,
    mcp_epg_get_feed,
    mcp_epg_get_streams,
    mcp_epg_get_categories,
    mcp_epg_get_languages,
    mcp_epg_get_countries,
    mcp_epg_get_subdivisions,
    mcp_epg_get_regions,
    mcp_epg_get_timezones,
    mcp_epg_get_blocklist,
    mcp_epg_get_guides,
    mcp_epg_query
] 