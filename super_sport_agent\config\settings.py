"""
Configuration settings for the SuperSport Agent
"""

import os
import logging
from pathlib import Path

# Base project paths
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
DOCUMENTS_DIR = PROJECT_ROOT / "documents"
VECTOR_STORE_PATH = PROJECT_ROOT / "faiss_index"
LOGS_DIR = PROJECT_ROOT / "logs"

# Ensure directories exist
DOCUMENTS_DIR.mkdir(exist_ok=True)
VECTOR_STORE_PATH.parent.mkdir(exist_ok=True)
LOGS_DIR.mkdir(exist_ok=True)

# API settings
SUPER_SPORT_API_BASE_URL = os.getenv("SUPER_SPORT_API_BASE_URL", "https://supersport.com/apix/guide/v5.3")
MAX_API_RESULTS = 50

# LLM settings
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
MODEL_GEMINI_PRO = "gemini-pro"
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"  # Use this as a placeholder until 2.0 is available

# Vector store settings
EMBEDDING_MODEL = "models/embedding-001"
CHUNK_SIZE = 10000
CHUNK_OVERLAP = 1000

# Document processing settings
SUPPORTED_EXTENSIONS = {
    "pdf": ".pdf",
    "text": [".txt", ".text"],
    "markdown": ".md",
    "vtt": ".vtt"
}

# Logging settings
LOG_FILE = LOGS_DIR / "super_sport.log"
LOG_LEVEL = logging.DEBUG
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'

# App state
processed_documents = False 