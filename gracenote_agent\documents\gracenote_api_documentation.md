# Gracenote API Documentation

## Base URL
```
http://on-api.gracenote.com/v3
```

## Authentication
All API requests require an API key parameter:
```
api_key=qen44bsc25p995qgbemd2n7u
```

## Available Endpoints

### Sources API
**Endpoint**: `GET /Sources`

Returns a list of all channels/program services with detailed information.

**Parameters**:

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| updateId  | Number | Yes      | Baseline update ID (use 0 for initial request) |
| startDate | String | Yes      | Start date for the data range (YYYY-MM-DD) |
| endDate   | String | Yes      | End date for the data range (YYYY-MM-DD) |
| api_key   | String | Yes      | Your API authentication key |
| limit     | Number | No       | Maximum number of results to return |

**Example Request**:
```
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=2025-05-14&endDate=2025-05-24&api_key=qen44bsc25p995qgbemd2n7u&limit=1000
```

**Response Format**:
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<on xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://files.api.gracenote.com/xsd/on_update_sources_3.22.xsd" schemaVersion="3.22">
    <header>
        <content>On - Updates: Sources</content>
        <created>2025-05-15T05:55:09Z</created>
        <copyright>Copyright 2025 Gracenote, a Nielsen Company. All rights reserved.</copyright>
        <requestParameters>
            <requestParameter name="updateId">0</requestParameter>
            <requestParameter name="limit">1000</requestParameter>
        </requestParameters>
        <streamData>
            <maxUpdateId>85890760158</maxUpdateId>
        </streamData>
    </header>
    <sources>
        <prgSvcs>
            <prgSvc prgSvcId="143261" updateId="83049337939" updateDate="2024-10-31T07:49:23Z">
                <name>Kerala Vision Kerala</name>
                <reach>National/International</reach>
                <type>Satellite</type>
                <status statusId="6">Edited</status>
                <countriesOfCoverage>
                    <country>IND</country>
                </countriesOfCoverage>
                <videoQuality>
                    <signalType>Digital</signalType>
                    <videoType>SDTV</videoType>
                </videoQuality>
                <attribs>
                    <attrib>Digital</attrib>
                    <attrib>SDTV</attrib>
                </attribs>
                <timeZone>Indian Non-observing</timeZone>
                <ianaTimeZone>Asia/Kolkata</ianaTimeZone>
                <callSign>KERALAALA</callSign>
                <edLangs>
                    <edLang>en-IN</edLang>
                </edLangs>
                <bcastLangs>
                    <bcastLang>ml</bcastLang>
                </bcastLangs>
                <assets>
                    <asset assetId="s143261_lw_h15_aa" lastModified="2024-10-31T07:45:50Z" type="image/png" width="3200" height="2400" primary="true" category="Source Logo - white" ratio="4:3" >
                        <URI>assets/s143261_lw_h15_aa.png</URI>
                    </asset>
                    <!-- Additional assets omitted for brevity -->
                </assets>
            </prgSvc>
            <!-- Additional channels omitted for brevity -->
        </prgSvcs>
    </sources>
</on>
```

**Field Descriptions**:

Each `prgSvc` (program service/channel) contains the following properties:

- `prgSvcId`: A unique identifier for the channel/program service
- `updateId`: A unique identifier for this update of the channel information
- `updateDate`: Timestamp when this channel information was last updated
- `name`: The official name of the TV channel
- `reach`: Geographic reach of the channel (e.g., "National/International")
- `type`: Type of broadcasting (e.g., "Satellite", "Cable", "Terrestrial")
- `status`: Current status of the channel with a corresponding statusId
- `countriesOfCoverage`: List of countries where this channel is broadcast
- `videoQuality`: Contains information about the signal type and video format
  - `signalType`: Type of signal (e.g., "Digital")
  - `videoType`: Quality of video (e.g., "SDTV", "HDTV")
- `attribs`: List of channel attributes
- `timeZone`: Human-readable timezone of the channel's primary broadcast
- `ianaTimeZone`: Standard IANA timezone identifier
- `callSign`: Unique call sign or identifier for the channel
- `edLangs`: Editorial languages used for metadata
- `bcastLangs`: Broadcast languages used in the channel's content
- `assets`: List of channel logos and graphics in various formats
  - `assetId`: Unique identifier for the asset
  - `lastModified`: Timestamp when this asset was last updated
  - `type`: MIME type of the asset
  - `width`, `height`: Dimensions of the asset in pixels
  - `primary`: Boolean indicating if this is a primary asset
  - `category`: Type of asset (e.g., "Source Logo - white")
  - `ratio`: Aspect ratio of the asset
  - `URI`: Path to access the asset

### Schedules API
**Endpoint**: `GET /Schedules`

Returns the program schedule for a specific channel.

**Parameters**:

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| prgSvcId  | Number | Yes      | Program Service ID of the channel |
| startDate | String | Yes      | Start date for the schedule (YYYY-MM-DD) |
| endDate   | String | Yes      | End date for the schedule (YYYY-MM-DD) |
| api_key   | String | Yes      | Your API authentication key |

**Example Request**:
```
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=143261&startDate=2025-05-14&endDate=2025-05-24&api_key=qen44bsc25p995qgbemd2n7u
```

**Response Format**:
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<on xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://files.api.gracenote.com/xsd/on_update_schedules_3.22.xsd" schemaVersion="3.22">
    <header>
        <content>On - Updates: Schedules</content>
        <created>2025-05-15T05:55:52Z</created>
        <copyright>Copyright 2025 Gracenote, a Nielsen Company. All rights reserved.</copyright>
        <requestParameters>
            <requestParameter name="prgSvcId">143261</requestParameter>
            <requestParameter name="endDate">2025-05-24</requestParameter>
            <requestParameter name="startDate">2025-05-14</requestParameter>
        </requestParameters>
    </header>
    <schedules type="tv">
        <schedule prgSvcId="143261" date="2025-05-15" updateId="16536802679" updateDate="2025-05-08T03:01:05Z" >
            <event TMSId="SH042144040000" rootId="514472" time="00:30" dur="PT04H00M" > </event>
            <event TMSId="SH042144040000" rootId="514472" time="04:30" dur="PT04H00M" > </event>
            <event TMSId="SH042144040000" rootId="514472" time="08:30" dur="PT04H00M" > </event>
            <event TMSId="SH042144040000" rootId="514472" time="12:30" dur="PT04H00M" > </event>
            <event TMSId="SH042144040000" rootId="514472" time="16:30" dur="PT08H00M" > </event>
        </schedule>
        <!-- Additional days omitted for brevity -->
        <schedule prgSvcId="143261" date="2025-05-19" updateId="16583456298" updateDate="2025-05-12T03:16:06Z" >
            <event TMSId="SH051284270000" rootId="27350952" time="00:30" dur="PT00H30M" > </event>
            <event TMSId="SH051284170000" rootId="27350911" time="01:00" dur="PT00H30M" > </event>
            <!-- Additional events omitted for brevity -->
        </schedule>
        <!-- Additional days omitted for brevity -->
    </schedules>
</on>
```

**Field Descriptions**:

The response contains a collection of `schedule` elements, one for each day in the requested date range:

- `prgSvcId`: The channel ID for which schedule information is being provided
- `date`: The date for this schedule in YYYY-MM-DD format
- `updateId`: A unique identifier for this update of the schedule information
- `updateDate`: Timestamp when this schedule information was last updated

Each schedule contains a collection of `event` elements representing individual programs:

- `TMSId`: A unique identifier for the program event, used to fetch detailed program information
- `rootId`: An identifier that can be used to associate related programs or episodes
- `time`: The start time of the program in 24-hour format (HH:MM)
- `dur`: The duration of the program in ISO 8601 duration format (e.g., "PT04H00M" is 4 hours)

### Programs API
**Endpoint**: `GET /Programs`

Returns detailed information about specific programs.

**Parameters**:

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| tmsId     | String | Yes      | TMS ID(s) of the programs (comma-separated for multiple IDs) |
| api_key   | String | Yes      | Your API authentication key |

**Example Request**:
```
GET http://on-api.gracenote.com/v3/Programs?tmsId=SH051855920000&api_key=qen44bsc25p995qgbemd2n7u
```

**Response Format**:
```xml
<?xml version="1.0" encoding="UTF-8" ?>
<on xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://files.api.gracenote.com/xsd/on_update_programs_3.22.xsd" schemaVersion="3.22">
    <header>
        <content>On - Updates: Programs</content>
        <created>2025-05-15T05:59:48Z</created>
        <copyright>Copyright 2025 Gracenote, a Nielsen Company. All rights reserved.</copyright>
        <requestParameters>
            <requestParameter name="tmsId">SH051855920000</requestParameter>
        </requestParameters>
    </header>
    <programs>
        <program TMSId="SH051855920000" rootId="27704716" connectorId="SH051855920000" seriesId="27704716" updateId="89953908615" updateDate="2025-04-07T15:03:06Z" >
            <titles lang="en">
                <title size="120" type="full" subType="Main" lang="en">Putham Pudhu Kaalai</title>
                <title size="70" type="red" lang="en">Putham Pudhu Kaalai</title>
                <title size="40" type="red" lang="en">Putham Pudhu Kaalai</title>
                <title size="20" type="red" lang="en">Putham Pudhu Kaalai</title>
                <title size="10" type="red" lang="en">Putham</title>
            </titles>
            <descriptions lang="en-IN">
                <desc size="250" type="generic" lang="en-IN">The host presents segments on astrology, yoga asanas, health and cookery tips by experts, interview with a guest and motivational thoughts for the day.</desc>
                <desc size="100" type="generic" lang="en-IN">The host presents segments on astrology, yoga asanas and health tips for the day.</desc>
            </descriptions>
            <progType>Series</progType>
            <subType>Series</subType>
            <genres>
                <genre genreId="8">Documentary</genre>
            </genres>
            <origAirDate>2024-06-19</origAirDate>
            <colorCode>Color</colorCode>
            <assets>
                <asset assetId="p27704716_i_h9_aa" lastModified="2024-07-01T11:03:56Z" type="image/jpeg" width="1440" height="1080" primary="true" category="Iconic" ratio="4:3" tier="Series" >
                    <URI>assets/p27704716_i_h9_aa.jpg</URI>
                </asset>
                <!-- Additional assets omitted for brevity -->
            </assets>
            <sourceType>Network</sourceType>
            <originalNetwork prgSvcId="158322">SHATV</originalNetwork>
            <seriesPremiere>2024-06-19</seriesPremiere>
            <duration>60</duration>
        </program>
    </programs>
</on>
```

**Field Descriptions**:

Each `program` element contains detailed metadata about a specific program:

- `TMSId`: The unique identifier for this program
- `rootId`: An identifier linking related programs
- `connectorId`: An identifier for connecting to external systems
- `seriesId`: The identifier for the series this program belongs to
- `updateId`: A unique identifier for this update of the program information
- `updateDate`: Timestamp when this program information was last updated

The program data includes:

- `titles`: Various versions of the program title in different lengths and types
  - `size`: Character length constraint
  - `type`: Title type (e.g., "full", "red" for reduced)
  - `subType`: Additional title classification
  - `lang`: Language code for the title
- `descriptions`: Program descriptions of varying lengths
  - `size`: Character length constraint
  - `type`: Description type (e.g., "generic")
  - `lang`: Language code for the description
- `progType`: Program type (e.g., "Series", "Movie", "Sports")
- `subType`: Program subtype
- `genres`: List of genres with IDs
- `origAirDate`: Original air date of the program
- `colorCode`: Indicates if the program is in color or black and white
- `assets`: List of program images and graphics
  - Similar structure to channel assets, with additional `tier` attribute
- `sourceType`: The type of content source
- `originalNetwork`: The original network that produced/aired the content
- `seriesPremiere`: Date when the series first premiered
- `duration`: Length of the program in minutes

## Data Relationships

### Sources → Schedules → Programs

1. **Sources API** provides channel listings with `prgSvcId` as the unique identifier
2. **Schedules API** uses `prgSvcId` to fetch program lineup for a channel
3. Each schedule event contains a `TMSId` that references a specific program
4. **Programs API** uses `TMSId` to fetch detailed program metadata

This creates a hierarchical relationship:
- Sources (Channels) contain multiple Schedules (Days)
- Schedules contain multiple Events (Program Airings)
- Events reference Programs (Content)

### Asset Relationships

Both channels and programs have associated assets (images):
- Channel assets typically include logos in various formats and color schemes
- Program assets include promotional imagery, thumbnails, and posters
- Assets contain dimension information and URIs for retrieval

## Common API Combinations for User Queries

### Finding All Available Channels

To get a list of all channels with their details:

```
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=YOUR_API_KEY
```

### Finding Program Schedule for a Channel

To get the complete schedule for a specific channel for a date range:

```
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=YOUR_API_KEY
```

### Finding Detailed Program Information

To get comprehensive metadata about specific programs:

```
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2&api_key=YOUR_API_KEY
```

### Complete EPG Workflow

For a complete Electronic Program Guide implementation:

1. Get all channels from Sources API
2. For each channel of interest, get the schedule from Schedules API
3. Collect all unique TMSIds from the schedule events
4. Batch request program details from Programs API using the collected TMSIds
5. Link the program details back to their scheduled events

## Advanced User Questions and Filtering

### Channels by Language

To find channels broadcasting in a specific language, filter the Sources API response by examining the `bcastLangs` nodes:

```xml
<bcastLangs>
    <bcastLang>ml</bcastLang>
</bcastLangs>
```

### Channels by Country

To find channels from a specific country, filter the Sources API response by examining the `countriesOfCoverage` nodes:

```xml
<countriesOfCoverage>
    <country>IND</country>
</countriesOfCoverage>
```

### HD vs. SD Channels

To find HD channels, filter the Sources API response by examining the `videoQuality` node:

```xml
<videoQuality>
    <signalType>Digital</signalType>
    <videoType>HDTV</videoType>
</videoQuality>
```

### Programs by Genre

To find programs of a specific genre, filter the Programs API response by examining the `genres` node:

```xml
<genres>
    <genre genreId="8">Documentary</genre>
</genres>
```

### Programs by Date Range

To find programs airing within a specific date range, use the Schedules API with appropriate `startDate` and `endDate` parameters.

### Program Imagery

To find high-quality images for a program, examine the `assets` node in the Programs API response for assets with high resolution values:

```xml
<asset assetId="p27704716_i_h8_aa" lastModified="2024-07-01T11:03:56Z" type="image/jpeg" width="3840" height="2160" primary="true" category="Iconic" ratio="16:9" tier="Series" >
    <URI>assets/p27704716_i_h8_aa.jpg</URI>
</asset>
```

## Required Mandatory Fields Summary

### Sources API
- `updateId`: Baseline update ID (use 0 for initial request)
- `startDate`: Start date in YYYY-MM-DD format
- `endDate`: End date in YYYY-MM-DD format
- `api_key`: qen44bsc25p995qgbemd2n7u

### Schedules API
- `prgSvcId`: Channel ID from Sources API
- `startDate`: Start date in YYYY-MM-DD format
- `endDate`: End date in YYYY-MM-DD format
- `api_key`: qen44bsc25p995qgbemd2n7u

### Programs API
- `tmsId`: One or more program IDs from Schedules API (comma-separated)
- `api_key`: qen44bsc25p995qgbemd2n7u 

## Common User Questions and Required API Calls

Users typically don't know the API structure but will ask natural questions about TV listings. Here are common questions and the API calls needed to answer them:

### 1. "What channels are available?"

**Required API calls:**
```
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Parse the `prgSvcs` list from the Sources API response
- Extract the `name`, `callSign`, and other relevant details from each `prgSvc` element
- Present a formatted list of available channels to the user

### 2. "What's on Star Sports tonight?"

**Required API calls:**
```
// Step 1: Find the channel ID for Star Sports
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get the schedule for Star Sports using its prgSvcId
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=STAR_SPORTS_ID&startDate=TODAY&endDate=TODAY&api_key=qen44bsc25p995qgbemd2n7u

// Step 3: Get program details for tonight's programs
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- First, search the Sources API response for a channel with name containing "Star Sports"
- Extract the `prgSvcId` for that channel
- Use the ID to query the Schedules API for today's schedule
- Filter events to include only those between 6 PM and midnight (typical "tonight" period)
- Collect all `TMSId` values from those events
- Query the Programs API with those IDs to get detailed information
- Present a list of programs with times and descriptions

### 3. "When is the next cricket match on TV?"

**Required API calls:**
```
// Step 1: Get all channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for sports channels
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=SPORTS_CHANNEL_ID1&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=SPORTS_CHANNEL_ID2&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for all relevant sports channels

// Step 3: Get program details for scheduled events
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- First, identify likely sports channels from the Sources API
- For each sports channel, get their schedule for the next week
- Collect all TMSIds from scheduled events
- Get program details for all those events
- Filter programs with titles or descriptions containing "cricket" or related terms
- Sort by air date and time
- Present the next cricket match(es) with channel, date, time and description

### 4. "What movies are on this weekend?"

**Required API calls:**
```
// Step 1: Get all channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for relevant channels for upcoming weekend
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID1&startDate=UPCOMING_SATURDAY&endDate=UPCOMING_SUNDAY&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID2&startDate=UPCOMING_SATURDAY&endDate=UPCOMING_SUNDAY&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for all channels

// Step 3: Get program details for weekend events
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Get schedules for all channels for the upcoming weekend
- Collect all TMSIds from the scheduled events
- Get program details for all events
- Filter programs where `progType` equals "Movie"
- Sort by channel, date, and time
- Present a list of movies with air times, channels, and descriptions

### 5. "What time is the news on BBC?"

**Required API calls:**
```
// Step 1: Find the channel ID for BBC
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get the schedule for BBC
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=BBC_CHANNEL_ID&startDate=TODAY&endDate=TODAY&api_key=qen44bsc25p995qgbemd2n7u

// Step 3: Get program details for BBC's programs
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Search Sources API response for channels containing "BBC" in their name
- Get the schedule for the BBC channel(s)
- Get program details for all events in the schedule
- Filter programs with "news" in the title or description
- Present a list of news programs with their air times

### 6. "What Hindi channels are available?"

**Required API calls:**
```
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Parse the Sources API response
- Filter channels where `bcastLangs` contains "hi" (ISO code for Hindi)
- Present a list of Hindi-language channels

### 7. "Tell me about the show Putham Pudhu Kaalai"

**Required API calls:**
```
// Step 1: Find the program by searching through all channels and schedules (this is inefficient but might be necessary)
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: For each channel, search schedules for a program matching the name
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID1&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID2&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat until a matching program is found

// Step 3: Once a TMSId is found, get detailed information
GET http://on-api.gracenote.com/v3/Programs?tmsId=FOUND_PROGRAM_TMSID&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Search through schedules to find a program with a title matching "Putham Pudhu Kaalai"
- Once found, use the TMSId to get detailed program information
- Present program details including description, genre, original air date, etc.

### 8. "What's playing on channel 143261 right now?"

**Required API calls:**
```
// Get today's schedule for the specified channel
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=143261&startDate=TODAY&endDate=TODAY&api_key=qen44bsc25p995qgbemd2n7u

// Get program details for the current program
GET http://on-api.gracenote.com/v3/Programs?tmsId=CURRENT_PROGRAM_TMSID&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Get today's schedule for channel 143261
- Determine the current time
- Find the event that spans the current time
- Get the program details using its TMSId
- Present information about the currently airing program

### 9. "Show me all documentary programs tomorrow"

**Required API calls:**
```
// Step 1: Get all channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for all channels for tomorrow
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID1&startDate=TOMORROW&endDate=TOMORROW&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=CHANNEL_ID2&startDate=TOMORROW&endDate=TOMORROW&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for relevant channels

// Step 3: Get program details for tomorrow's events
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Get schedules for all channels for tomorrow
- Collect all TMSIds from scheduled events
- Get program details for all events
- Filter programs where `genres` contains a genre with `genreId` of "8" (Documentary)
- Sort by time and channel
- Present a list of documentary programs airing tomorrow

### 10. "When is the next episode of Putham Pudhu Kaalai?"

**Required API calls:**
```
// Step 1: Find the program's TMSId (similar to question 7)
// ... search through channels and schedules

// Step 2: Once a TMSId is found, get program details to confirm series information
GET http://on-api.gracenote.com/v3/Programs?tmsId=FOUND_PROGRAM_TMSID&api_key=qen44bsc25p995qgbemd2n7u

// Step 3: Search future schedules for the channel that airs this program
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=PROGRAM_CHANNEL_ID&startDate=TODAY&endDate=TODAY+14&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- First find a program matching "Putham Pudhu Kaalai"
- Get its details and note the channel that broadcasts it and its `seriesId`
- Get future schedules for that channel
- Search for events with TMSIds that have the same `rootId` or that match the program pattern
- Find the next occurrence after the current date/time
- Present information about when the next episode will air

### 11. "Which channels show sports in HD?"

**Required API calls:**
```
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Parse the Sources API response
- Filter channels where:
  - `videoQuality/videoType` equals "HDTV"
  - Channel name or description contains words like "Sports", "Cricket", "Football", etc.
- Present a list of HD sports channels

### 12. "What's the TV guide for Star Plus for the next 3 days?"

**Required API calls:**
```
// Step 1: Find the channel ID for Star Plus
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get the schedule for Star Plus for the next 3 days
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=STAR_PLUS_ID&startDate=TODAY&endDate=TODAY+2&api_key=qen44bsc25p995qgbemd2n7u

// Step 3: Get program details for scheduled programs
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Find the channel ID for Star Plus
- Get its schedule for today plus the next 2 days
- Get program details for all events
- Organize programs by day and time
- Present a comprehensive 3-day guide for Star Plus

### 13. "Are there any Malayalam movies this week?"

**Required API calls:**
```
// Step 1: Find Malayalam channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for Malayalam channels for the next week
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=MALAYALAM_CHANNEL_ID1&startDate=TODAY&endDate=TODAY+6&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=MALAYALAM_CHANNEL_ID2&startDate=TODAY&endDate=TODAY+6&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for all Malayalam channels

// Step 3: Get program details
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Identify Malayalam channels (where `bcastLangs` contains "ml")
- Get schedules for these channels for the next week
- Get program details for all events
- Filter for programs where:
  - `progType` equals "Movie"
  - Language is Malayalam or channel primarily broadcasts in Malayalam
- Present a list of Malayalam movies airing in the next week

### 14. "What children's programs are on today?"

**Required API calls:**
```
// Step 1: Get all channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for kids' channels and general entertainment channels
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=KIDS_CHANNEL_ID1&startDate=TODAY&endDate=TODAY&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=KIDS_CHANNEL_ID2&startDate=TODAY&endDate=TODAY&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for relevant channels

// Step 3: Get program details
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- Identify children's channels (based on names like "Disney", "Cartoon Network", "Nickelodeon", etc.)
- Get today's schedules for these channels
- Also include general entertainment channels
- Get program details for all events
- Filter programs where:
  - Genre includes "Children" or "Animation"
  - Title or description indicates children's content
- Present a list of children's programs organized by time and channel

### 15. "What programs have a high viewer rating?"

**Note**: This question assumes that program ratings are available in the program data. If not actually available, the system should explain that this information isn't provided in the API.

**Required API calls:**
```
// Step 1: Get popular/major channels
GET http://on-api.gracenote.com/v3/Sources?updateId=0&startDate=YYYY-MM-DD&endDate=YYYY-MM-DD&api_key=qen44bsc25p995qgbemd2n7u

// Step 2: Get schedules for popular channels
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=POPULAR_CHANNEL_ID1&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
GET http://on-api.gracenote.com/v3/Schedules?prgSvcId=POPULAR_CHANNEL_ID2&startDate=TODAY&endDate=TODAY+7&api_key=qen44bsc25p995qgbemd2n7u
// ... repeat for relevant channels

// Step 3: Get program details
GET http://on-api.gracenote.com/v3/Programs?tmsId=PROGRAM_ID1,PROGRAM_ID2,...&api_key=qen44bsc25p995qgbemd2n7u
```

**Processing logic:**
- If ratings are available in the program data, filter by that
- If not, use proxy indicators like:
  - Programs in prime time slots (7 PM - 11 PM)
  - Programs on major networks
  - Programs with "recommended" or "featured" flags (if available)
- Present a list of likely popular programs with their air times and channels 