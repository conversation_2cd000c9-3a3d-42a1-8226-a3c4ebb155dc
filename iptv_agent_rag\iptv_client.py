import os
import logging
import requests
from typing import List, Dict, Optional, Any, Type, Union, TypeVar, Generic
import urllib3
from datetime import datetime
import os.path
from .models import (
    ApiRequest, ApiResponse,
    ChannelRequest, ChannelsResponse, Channel,
    StreamRequest, StreamsResponse, Stream,
    CategoriesResponse, Category,
    CountriesResponse, Country,
    GuidesResponse, Guide,
    QueryRequest, QueryResponse, QueryResponseItem
)

# Import central logger
from .logger import get_logger
from .utils import limit_response_tokens

# Suppress SSL warnings - only use this in development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Get logger for this module
logger = get_logger('iptv_client')
logger.info("IPTV Client logging initialized")

T = TypeVar('T', bound=ApiResponse)

class IptvApiClient:
    """Client for interacting with the IPTV API"""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the IPTV API client
        
        Args:
            base_url: Base URL for the IPTV API. Defaults to the REMOTE_API_BASE_URL environment
                     variable or "https://iptv-org.github.io/api"
        """
        self.base_url = base_url or os.getenv("REMOTE_API_BASE_URL", "https://iptv-org.github.io/api")
        logger.info(f"Initialized IPTV API client with base URL: {self.base_url}")
    
    def _make_request(self, 
                     endpoint: str, 
                     params: Optional[Dict[str, Any]] = None, 
                     response_model: Type[T] = ApiResponse) -> T:
        """
        Make a request to the IPTV API
        
        Args:
            endpoint: The API endpoint to request (e.g., "channels.json")
            params: Optional query parameters
            response_model: Pydantic model to use for the response
            
        Returns:
            The API response parsed into the specified model
        """
        url = f"{self.base_url}/{endpoint}"
        logger.info(f"Making request to {url}")
        
        try:
            # Disable SSL verification - this is a workaround for SSL certificate issues
            # In production, you should fix the certificate issue instead of disabling verification
            response = requests.get(url, params=params, verify=False)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Response received from {url}: {data}")
            
            # For list responses, create the appropriate response object
            if isinstance(data, list):
                # Get the total number of items
                total_items = len(data)
                 
                # For large responses, limit the amount of data we return
                # if total_items > 100:
                #     data = data[:100]
                #     logger.info(f"Limited response to 100 items (total: {total_items})")
                
                # Special handling for guide data, which may have a different structure
                if endpoint == "guides.json":
                    try:
                        # Create the response object with valid items only
                        valid_items = []
                        for item in data:
                            # Ensure required fields exist
                            if 'id' not in item:
                                item['id'] = f"guide_{len(valid_items)}"
                            if 'channel' not in item or item['channel'] is None:
                                item['channel'] = "unknown"
                            if 'title' not in item:
                                item['title'] = "Untitled Program"
                            
                            try:
                                # Try to create a valid guide model
                                guide_model = Guide(**item)
                                valid_items.append(guide_model)
                            except Exception as e:
                                logger.warning(f"Skipping invalid guide item: {e}")
                                
                        return response_model(
                            status="success",
                            data=valid_items,
                            total_items=len(valid_items)
                        )
                    except Exception as e:
                        logger.error(f"Error processing guide data: {str(e)}")
                        return response_model(
                            status="error",
                            message=f"Error processing guide data: {str(e)}",
                            data=[],
                            total_items=0
                        )
                
                # For other endpoints, proceed normally
                try:
                    # Create the response object
                    return response_model(
                        status="success",
                        data=data,
                        total_items=total_items
                    )
                except Exception as e:
                    logger.error(f"Error creating response model: {str(e)}")
                    return response_model(
                        status="error", 
                        message=f"Error processing response: {str(e)}",
                        data=[]
                    )
            else:
                # For single-item responses
                try:
                    return response_model(
                        status="success",
                        data=[data]
                    )
                except Exception as e:
                    logger.error(f"Error creating response model: {str(e)}")
                    return response_model(
                        status="error", 
                        message=f"Error processing response: {str(e)}",
                        data=[]
                    )
        
        except requests.RequestException as e:
            logger.error(f"Request error: {str(e)}")
            return response_model(
                status="error",
                message=f"Request error: {str(e)}",
                data=[]
            )
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return response_model(
                status="error",
                message=f"Error parsing response: {str(e)}",
                data=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return response_model(
                status="error",
                message=f"Unexpected error: {str(e)}",
                data=[]
            )
    
    def get_channels(self, request: Optional[ChannelRequest] = None) -> ChannelsResponse:
        """
        Get channels from the IPTV API
        
        Args:
            request: Optional request parameters
            
        Returns:
            ChannelsResponse: The channels response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("channels.json", params, ChannelsResponse)
    
    def get_channel(self, channel_id: str) -> ChannelsResponse:
        """
        Get a specific channel by ID
        
        Args:
            channel_id: The channel ID
            
        Returns:
            ChannelsResponse: The channel response
        """
        all_channels = self.get_channels()
        if all_channels.status != "success":
            return all_channels
        
        # Find the specific channel
        matching_channels = [ch for ch in all_channels.data if ch.id == channel_id]
        
        if matching_channels:
            return ChannelsResponse(
                status="success",
                data=matching_channels,
                total_items=len(matching_channels)
            )
        else:
            return ChannelsResponse(
                status="error",
                message=f"Channel not found: {channel_id}",
                data=[],
                total_items=0
            )
    
    def get_streams(self, request: Optional[StreamRequest] = None) -> StreamsResponse:
        """
        Get streams from the IPTV API
        
        Args:
            request: Optional request parameters
            
        Returns:
            StreamsResponse: The streams response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("streams.json", params, StreamsResponse)
    
    def get_categories(self) -> CategoriesResponse:
        """
        Get categories from the IPTV API
        
        Returns:
            CategoriesResponse: The categories response
        """
        return self._make_request("categories.json", None, CategoriesResponse)
    
    def get_countries(self) -> CountriesResponse:
        """
        Get countries from the IPTV API
        
        Returns:
            CountriesResponse: The countries response
        """
        return self._make_request("countries.json", None, CountriesResponse)
    
    def get_guides(self, channel_id: Optional[str] = None) -> GuidesResponse:
        """
        Get program guides information from the IPTV API
        
        Args:
            channel_id: Optional channel ID to filter by
            
        Returns:
            GuidesResponse: The guides response
        """
        try:
            all_guides = self._make_request("guides.json", None, GuidesResponse)
            
            # If a channel ID is provided, filter the guides
            if channel_id and all_guides.status == "success":
                matching_guides = [g for g in all_guides.data if g.channel == channel_id]
                return GuidesResponse(
                    status="success",
                    data=matching_guides,
                    total_items=len(matching_guides)
                )
            return all_guides
        except Exception as e:
            logger.error(f"Error in get_guides: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error retrieving guides: {str(e)}",
                data=[],
                total_items=0
            )
    
    def query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query by making the appropriate API calls
        
        Args:
            request: The query request
            
        Returns:
            QueryResponse: The query response
        """
        query = request.query.lower()
        max_results = request.max_results
        results = []
        endpoints_queried = []
        
        # Simple keyword-based query processing
        if any(kw in query for kw in ["channel", "tv", "station", "network"]):
            endpoints_queried.append("channels")
            channels_resp = self.get_channels()
            if channels_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="channels",
                    data=channels_resp.data
                ))
                
        if any(kw in query for kw in ["stream", "watch", "link", "url"]):
            endpoints_queried.append("streams")
            streams_resp = self.get_streams()
            if streams_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="streams",
                    data=streams_resp.data
                ))
                
        if any(kw in query for kw in ["category", "type", "genre"]):
            endpoints_queried.append("categories")
            categories_resp = self.get_categories()
            if categories_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="categories",
                    data=categories_resp.data
                ))
                
        if any(kw in query for kw in ["country", "countries", "region", "location"]):
            endpoints_queried.append("countries")
            countries_resp = self.get_countries()
            if countries_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="countries",
                    data=countries_resp.data
                ))
        
        if any(kw in query for kw in ["guide", "program", "show", "schedule", "what's on", "whats on", "listing"]):
            endpoints_queried.append("guides")
            guides_resp = self.get_guides()
            if guides_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="guides",
                    data=guides_resp.data
                ))
                
        # If no specific queries were run, default to channels
        if not results:
            endpoints_queried.append("channels")
            channels_resp = self.get_channels()
            if channels_resp.status == "success":
                # Return all results without limiting
                results.append(QueryResponseItem(
                    source="channels",
                    data=channels_resp.data
                ))
        
        return QueryResponse(
            status="success",
            query=request.query,
            data=results,
            endpoints_queried=endpoints_queried
        ) 