# EPG Agent with Direct API Integration

This project provides an intelligent agent for answering questions about Electronic Program Guide (EPG) APIs using RAG (Retrieval-Augmented Generation) capabilities and direct API integration.

## Overview

The system consists of two main components:

1. **EPG Agent**: A RAG-powered agent that processes documents and answers user queries by directly accessing the remote EPG API.
2. **MCP Server** (Optional): A FastAPI server that provides API endpoints for EPG data with additional processing.

## Key Features

- Direct integration with the IPTV-org API (https://iptv-org.github.io/api)
- RAG capabilities using local documents for API documentation
- Natural language query processing for EPG data
- Support for all EPG endpoints: channels, feeds, streams, categories, countries, regions, timezones, etc.

## Setup

### Requirements

- Python 3.8+
- Required packages: fastapi, uvicorn, langchain, langchain_google_genai, PyPDF2, webvtt, FAISS, requests, and others (see below)

### Installation

1. Install the required packages:

```bash
pip install fastapi uvicorn langchain langchain_google_genai langchain_community PyPDF2 webvtt-py faiss-cpu google-generativeai requests
```

2. Set up environment variables:

```bash
export GOOGLE_API_KEY=your_google_api_key
export REMOTE_API_BASE_URL=https://iptv-org.github.io/api  # Change if you need a different API endpoint
```

### Starting the System

Run the demo script to test the agent's capabilities:

```bash
cd epg_agent
python demo.py
```

## Using the Agent

The agent provides several tools for working with EPG data:

### Document Processing Tools

- `list_files_in_documents_dir()`: List available document files
- `process_all_files()`: Process all documents in the directory
- `process_file(file_path)`: Process a specific document file
- `create_vector_store_from_text(text)`: Process text content into a vector store

### EPG API Tools

- `fetch_epg_api_data(endpoint, query_params)`: Directly fetch data from a specific EPG API endpoint
- `query_epg_data(query)`: Query EPG data using natural language

### Search Tools

- `search_similar_content(query, num_results)`: Search for relevant content in processed documents

## Example Queries

Once documents are processed, you can ask questions like:

- "What API endpoint should I use to get a list of all channels?"
- "Show me some BBC feeds" 
- "I want to find streams for CNN"
- "Tell me about the available channel categories"
- "What countries are supported in the EPG system?"

## API Endpoints

The EPG API provides the following endpoints:

- `/channels.json` - List all channels
- `/feeds.json` - List all feeds
- `/streams.json` - List all streams
- `/categories.json` - List all categories
- `/countries.json` - List all countries 
- `/regions.json` - List all regions
- `/timezones.json` - List all timezones
- `/blocklist.json` - List all blocked channels

## Architecture

```
epg_agent/
├── agent.py              # Main agent implementation with direct API integration
├── mcp_server.py         # Optional API server
├── demo.py               # Demo script
├── start.py              # Start script for both server and demo
├── __init__.py
├── data/                 # EPG data and documentation
│   └── api_documentation.md
├── documents/            # User-uploaded documents
└── faiss_index/          # Vector store for RAG functionality
```

## Customization

### Using a Different API Endpoint

To use a different API endpoint, set the `REMOTE_API_BASE_URL` environment variable.

### Extending the Agent

You can add more capabilities to the agent by adding tools to the `tools` list in `agent.py`.

## Troubleshooting

- If the MCP server fails to start, check that port 8000 is not already in use.
- If document processing fails, ensure the document files are not corrupted.
- If API requests fail, check that the MCP server is running and accessible. 