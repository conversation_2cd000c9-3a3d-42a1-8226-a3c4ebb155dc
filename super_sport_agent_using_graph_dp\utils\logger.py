"""
Logging utility for SuperSport Agent
"""

import sys
import logging
from ..config.settings import LOG_FILE, LOG_LEVEL, LOG_FORMAT

# Configure root logger formatter
formatter = logging.Formatter(LOG_FORMAT)

def get_logger(name):
    """
    Get a configured logger instance for the specified module.
    All loggers will write to the same central log file.
    
    Args:
        name (str): The name of the logger (typically __name__)
        
    Returns:
        logging.Logger: A configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Only add handlers if they don't exist already to prevent duplicate handlers
    if not logger.handlers:
        logger.setLevel(LOG_LEVEL)
        
        # Create file handler
        file_handler = logging.FileHandler(LOG_FILE)
        file_handler.setLevel(LOG_LEVEL)
        file_handler.setFormatter(formatter)
        
        # Create console handler for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add the handlers to the logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
    return logger 