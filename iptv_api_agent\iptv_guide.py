"""
IPTV Guide module for accessing and processing TV program guide data
"""

import os
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Any
from .models import Guide, GuidesResponse
from .iptv_client import IptvApiClient
from .logger import get_logger
from .utils import limit_response_tokens

# Get logger for this module
logger = get_logger('iptv_guide')
logger.info("IPTV Guide module initialized")

class IptvGuideManager:
    """
    Manager class for handling IPTV program guide data.
    Provides additional functionality beyond basic API access.
    """
    
    def __init__(self, client: Optional[IptvApiClient] = None):
        """Initialize the guide manager with an optional client"""
        self.client = client or IptvApiClient()
    
    def get_current_programs(self, channel_id: Optional[str] = None) -> GuidesResponse:
        """
        Get currently running programs
        
        Args:
            channel_id: Optional channel ID to filter by
            
        Returns:
            GuidesResponse with programs currently on air
        """
        try:
            # Get all guides or guides for a specific channel
            guides_resp = self.client.get_guides(channel_id)
            
            if guides_resp.status != "success":
                return guides_resp
            
            # Get current time
            now = datetime.now()
            
            # Filter for programs that are currently running
            current_programs = []
            for program in guides_resp.data:
                # Skip programs with missing times
                if not program.start or not program.stop:
                    continue
                    
                # Check if the program is currently running
                if program.start <= now <= program.stop:
                    current_programs.append(program)
            
            # Create response with only current programs
            return GuidesResponse(
                status="success",
                data=current_programs,
                total_items=len(current_programs)
            )
        except Exception as e:
            logger.error(f"Error getting current programs: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error getting current programs: {str(e)}",
                data=[],
                total_items=0
            )
    
    def get_upcoming_programs(self, 
                              channel_id: Optional[str] = None, 
                              hours_ahead: int = 24) -> GuidesResponse:
        """
        Get upcoming programs for the next N hours
        
        Args:
            channel_id: Optional channel ID to filter by
            hours_ahead: Number of hours to look ahead (default: 24)
            
        Returns:
            GuidesResponse with upcoming programs
        """
        try:
            # Get all guides or guides for a specific channel
            guides_resp = self.client.get_guides(channel_id)
            
            if guides_resp.status != "success":
                return guides_resp
            
            # Get current time and future time
            now = datetime.now()
            future = now + timedelta(hours=hours_ahead)
            
            # Filter for upcoming programs
            upcoming_programs = []
            for program in guides_resp.data:
                # Skip programs with missing times
                if not program.start or not program.stop:
                    continue
                    
                # Check if the program starts in the future but within our window
                if now <= program.start <= future:
                    upcoming_programs.append(program)
            
            # Sort by start time
            upcoming_programs.sort(key=lambda p: p.start)
            
            # Create response with only upcoming programs
            return GuidesResponse(
                status="success",
                data=upcoming_programs,
                total_items=len(upcoming_programs)
            )
        except Exception as e:
            logger.error(f"Error getting upcoming programs: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error getting upcoming programs: {str(e)}",
                data=[],
                total_items=0
            )
    
    def get_programs_by_category(self, 
                                category: str, 
                                channel_id: Optional[str] = None) -> GuidesResponse:
        """
        Get programs matching a specific category
        
        Args:
            category: Category to filter by
            channel_id: Optional channel ID to filter by
            
        Returns:
            GuidesResponse with programs in the specified category
        """
        try:
            # Get all guides or guides for a specific channel
            guides_resp = self.client.get_guides(channel_id)
            
            if guides_resp.status != "success":
                return guides_resp
            
            # Convert category to lowercase for case-insensitive comparison
            category_lower = category.lower()
            
            # Filter for programs in the specified category
            matching_programs = []
            for program in guides_resp.data:
                # Check if program has a matching category
                if program.category and category_lower in program.category.lower():
                    matching_programs.append(program)
            
            # Create response with only matching programs
            return GuidesResponse(
                status="success",
                data=matching_programs,
                total_items=len(matching_programs)
            )
        except Exception as e:
            logger.error(f"Error getting programs by category: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error getting programs by category: {str(e)}",
                data=[],
                total_items=0
            )
    
    def search_programs(self, query: str, channel_id: Optional[str] = None) -> GuidesResponse:
        """
        Search for programs matching a query string
        
        Args:
            query: Search query
            channel_id: Optional channel ID to filter by
            
        Returns:
            GuidesResponse with matching programs
        """
        try:
            # Get all guides or guides for a specific channel
            guides_resp = self.client.get_guides(channel_id)
            
            if guides_resp.status != "success":
                return guides_resp
            
            # Convert query to lowercase for case-insensitive comparison
            query_lower = query.lower()
            
            # Filter for programs matching the query
            matching_programs = []
            for program in guides_resp.data:
                # Check title and description for matches
                if ((program.title and query_lower in program.title.lower()) or
                    (program.description and query_lower in program.description.lower())):
                    matching_programs.append(program)
            
            # Create response with only matching programs
            return GuidesResponse(
                status="success",
                data=matching_programs,
                total_items=len(matching_programs)
            )
        except Exception as e:
            logger.error(f"Error searching programs: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error searching programs: {str(e)}",
                data=[],
                total_items=0
            )

# Helper functions for use in tools

def get_guide_manager() -> IptvGuideManager:
    """Get a guide manager instance"""
    return IptvGuideManager()

def format_guide_time(dt: Optional[datetime]) -> str:
    """Format a datetime for display in guide listings"""
    if not dt:
        return "Unknown time"
    return dt.strftime("%Y-%m-%d %H:%M")

def format_guide_entry(guide: Guide) -> str:
    """Format a guide entry for display"""
    result = f"{guide.title}"
    if guide.start and guide.stop:
        result += f" ({format_guide_time(guide.start)} - {format_guide_time(guide.stop)})"
    if guide.category:
        result += f" [{guide.category}]"
    if guide.description:
        desc = guide.description
        if len(desc) > 100:
            desc = desc[:97] + "..."
        result += f"\n  {desc}"
    return result 