"""
Knowledge Graph service for Neo4j operations
"""

import os
from pathlib import Path
from typing import List, Dict, Optional, Union, Any
from datetime import datetime
from langchain.text_splitter import RecursiveCharacterTextSplitter
import google.generativeai as genai
import logging
import json

from langchain_community.graphs import Neo4jGraph
from langchain.chains import Graph<PERSON>ypher<PERSON><PERSON>hai<PERSON>
from langchain_google_genai import Chat<PERSON><PERSON>gleGenerativeA<PERSON>
from langchain.prompts import PromptTemplate

from ..utils.logger import get_logger
from ..config.settings import (
    CHUNK_SIZE, 
    CHUNK_OVERLAP, 
    GOOGLE_API_KEY,
    MODEL_GEMINI_2_0_FLASH,
    NEO4J_URI,
    NEO4J_USERNAME,
    NEO4J_PASSWORD,
    NEO4J_DATABASE
)
from ..models.agent_models import KnowledgeGraphResult

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY is not set, knowledge graph operations will fail")

def serialize_neo4j_objects(obj: Any) -> Any:
    """
    Recursively convert Neo4j objects to serializable formats
    
    Args:
        obj: Object that may contain Neo4j datetime objects
        
    Returns:
        Serializable version of the object
    """
    # Handle Neo4j DateTime objects
    if hasattr(obj, '__class__') and 'neo4j.time' in str(obj.__class__):
        if hasattr(obj, 'to_native'):
            # Convert Neo4j DateTime to Python datetime, then to ISO string
            return obj.to_native().isoformat()
        else:
            # Fallback to string representation
            return str(obj)
    
    # Handle dictionaries
    elif isinstance(obj, dict):
        return {key: serialize_neo4j_objects(value) for key, value in obj.items()}
    
    # Handle lists
    elif isinstance(obj, list):
        return [serialize_neo4j_objects(item) for item in obj]
    
    # Handle tuples
    elif isinstance(obj, tuple):
        return tuple(serialize_neo4j_objects(item) for item in obj)
    
    # Return as-is for other types
    else:
        return obj

class KnowledgeGraphService:
    """Service for Neo4j knowledge graph operations"""
    
    def __init__(self):
        """Initialize the knowledge graph service"""
        self._graph = None
        logger.info("Initialized knowledge graph service")
    
    def get_text_chunks(self, text: str) -> List[str]:
        """
        Split text into chunks for processing
        
        Args:
            text: Text to split
            
        Returns:
            List of text chunks
        """
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE, 
            chunk_overlap=CHUNK_OVERLAP
        )
        return text_splitter.split_text(text)
    
    def get_neo4j_graph(self) -> Optional[Neo4jGraph]:
        """
        Get the Neo4j graph connection
        
        Returns:
            Neo4jGraph instance or None if not available
        """
        try:
            # If we already have a loaded graph connection
            if self._graph:
                return self._graph
            
            # Create new Neo4j graph connection
            self._graph = Neo4jGraph(
                url=NEO4J_URI,
                username=NEO4J_USERNAME, 
                password=NEO4J_PASSWORD,
                database=NEO4J_DATABASE
            )
            
            return self._graph
            
        except Exception as e:
            logger.error(f"Error connecting to Neo4j: {str(e)}")
            return None
    
    def create_knowledge_graph(self, content: str, source: str = "documents") -> KnowledgeGraphResult:
        """
        Create a knowledge graph from content
        
        Args:
            content: Text content to process
            source: Source identifier for the content
            
        Returns:
            KnowledgeGraphResult with status
        """
        try:
            # Get Neo4j graph connection
            graph = self.get_neo4j_graph()
            if not graph:
                raise ValueError("Neo4j connection not available")
            
            # Split text into chunks for more manageable processing
            chunks = self.get_text_chunks(content)
            if not chunks:
                return KnowledgeGraphResult(
                    status="error",
                    message="No text chunks created",
                    total_nodes=0
                )
            
            # Clear existing data (optional, comment if you want to append)
            clear_query = "MATCH (n) DETACH DELETE n"
            graph.query(clear_query)
            
            # Use Gemini to extract entities and relationships
            llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_2_0_FLASH)
            
            # Process each chunk and build the knowledge graph
            total_nodes = 0
            total_relationships = 0
            for i, chunk in enumerate(chunks):
                # Extract entities and relationships using LLM
                entity_prompt = """
                Extract API documentation entities and their relationships from the following text. 
                Focus on API endpoints, parameters, response structures, data formats, and usage examples.
                Return the result as valid JSON in the following format:
                {
                  "entities": [
                    {"type": "Endpoint", "name": "/tvguide", "properties": {"method": "GET", "base_url": "https://supersport.com/apix/guide/v5.3", "parameters": {"countryCode": {"required": true, "type": "String", "description": "Country code (e.g., 'za' for South Africa)"}, "channelOnly": {"required": false, "type": "Boolean", "description": "Filter to show only channels"}, "startDateTime": {"required": false, "type": "String", "description": "Start date and time for the guide (YYYY-MM-DD format)"}, "endDateTime": {"required": false, "type": "String", "description": "End date and time for the guide (YYYY-MM-DD format)"}, "liveOnly": {"required": false, "type": "Boolean", "description": "Filter to show only live events"}}}}},
                    {"type": "Parameter", "name": "countryCode", "properties": {"required": true, "type": "String", "description": "Country code (e.g., 'za' for South Africa)"}},
                    {"type": "Parameter", "name": "channelOnly", "properties": {"required": false, "type": "Boolean", "description": "Filter to show only channels"}},
                    {"type": "Parameter", "name": "startDateTime", "properties": {"required": false, "type": "String", "description": "Start date and time for the guide"}},
                    {"type": "Parameter", "name": "endDateTime", "properties": {"required": false, "type": "String", "description": "End date and time for the guide"}},
                    {"type": "Parameter", "name": "liveOnly", "properties": {"required": false, "type": "Boolean", "description": "Filter to show only live events"}},
                    {"type": "ResponseField", "name": "sport", "properties": {"type": "String", "description": "The primary sport category for the event"}},
                    {"type": "SportEvent", "name": "Rugby World Cup 2023", "properties": {"date": "2023-09-08", "isLive": true}},
                    {"type": "Channel", "name": "SuperSport School HD", "properties": {"channel_number": 216, "code": "35L"}},
                    {"type": "Sport", "name": "Football", "properties": {}},
                    {"type": "Example", "name": "What sports are on TV tonight", "properties": {"description": "Common user query example"}}
                  ],
                  "relationships": [
                    {"start": "/tvguide", "type": "REQUIRES_PARAMETER", "end": "countryCode"},
                    {"start": "/tvguide", "type": "ACCEPTS_PARAMETER", "end": "channelOnly"},
                    {"start": "/tvguide", "type": "ACCEPTS_PARAMETER", "end": "startDateTime"},
                    {"start": "/tvguide", "type": "ACCEPTS_PARAMETER", "end": "endDateTime"},
                    {"start": "/tvguide", "type": "ACCEPTS_PARAMETER", "end": "liveOnly"},
                    {"start": "/tvguide", "type": "RETURNS_FIELD", "end": "sport"},
                    {"start": "Rugby World Cup 2023", "type": "HAS_SPORT", "end": "Rugby"},
                    {"start": "SuperSport School HD", "type": "BROADCASTS", "end": "Rugby World Cup 2023"},
                    {"start": "What sports are on TV tonight", "type": "USES_ENDPOINT", "end": "/tvguide", "properties": {"parameters": "countryCode=za&startDateTime=TODAY&endDateTime=TODAY"}},
                    {"start": "Rugby", "type": "IS_SPORT_CATEGORY", "end": "sport"}
                  ]
                }
                
                Be sure to capture:
                1. API endpoints with their HTTP methods, base URLs and ALL query parameters
                2. Parameters with type, required status, and descriptions
                3. Response fields and their data types
                4. Sport events shown in examples
                5. TV channels mentioned
                6. Sport categories
                7. Example queries and how they map to API calls
                8. Relationships between all these entities
                
                TEXT
                """
                
                # Append the chunk to the prompt
                entity_prompt = entity_prompt + chunk
                
                try:
                    entity_response = llm.invoke(entity_prompt)
                    # Extract JSON from response
                    import re
                    json_match = re.search(r'```json\n([\s\S]+?)\n```', entity_response.content)
                    if json_match:
                        graph_data = json.loads(json_match.group(1))
                    else:
                        # Try to parse the entire response as JSON
                        graph_data = json.loads(entity_response.content)
                    
                    # Create entities in Neo4j
                    for entity in graph_data.get("entities", []):
                        entity_type = entity["type"]
                        entity_name = entity["name"]
                        properties = entity.get("properties", {})
                        
                        # Create Cypher query for entity
                        property_parts = []
                        for key, value in properties.items():
                            if isinstance(value, bool):
                                # Handle boolean values
                                property_parts.append(f'n.{key} = {str(value).lower()}')
                            elif isinstance(value, (int, float)):
                                # Handle numeric values directly
                                property_parts.append(f'n.{key} = {value}')
                            else:
                                # Escape quotes in string values
                                escaped_value = str(value).replace('"', '\\"').replace("'", "\\'")
                                property_parts.append(f'n.{key} = "{escaped_value}"')
                                
                        property_string = ", ".join(property_parts)
                        if property_string:
                            create_query = f'MERGE (n:{entity_type} {{name: "{entity_name.replace('"', '\\"')}"}}) SET {property_string}'
                        else:
                            create_query = f'MERGE (n:{entity_type} {{name: "{entity_name.replace('"', '\\"')}"}}) RETURN n'
                        
                        graph.query(create_query)
                        total_nodes += 1
                    
                    # Create relationships in Neo4j
                    for relationship in graph_data.get("relationships", []):
                        start_node = relationship["start"]
                        end_node = relationship["end"]
                        rel_type = relationship["type"]
                        properties = relationship.get("properties", {})
                        
                        # Create Cypher query for relationship
                        property_parts = []
                        for key, value in properties.items():
                            if isinstance(value, bool):
                                property_parts.append(f'r.{key} = {str(value).lower()}')
                            elif isinstance(value, (int, float)):
                                property_parts.append(f'r.{key} = {value}')
                            else:
                                escaped_value = str(value).replace('"', '\\"').replace("'", "\\'")
                                property_parts.append(f'r.{key} = "{escaped_value}"')
                                
                        property_string = ", ".join(property_parts)
                        if property_string:
                            create_rel_query = f'''
                            MATCH (a), (b)
                            WHERE a.name = "{start_node.replace('"', '\\"')}" AND b.name = "{end_node.replace('"', '\\"')}"
                            MERGE (a)-[r:{rel_type}]->(b)
                            SET {property_string}
                            '''
                        else:
                            create_rel_query = f'''
                            MATCH (a), (b)
                            WHERE a.name = "{start_node.replace('"', '\\"')}" AND b.name = "{end_node.replace('"', '\\"')}"
                            MERGE (a)-[r:{rel_type}]->(b)
                            '''
                        
                        graph.query(create_rel_query)
                        total_relationships += 1
                        
                except Exception as chunk_error:
                    logger.error(f"Error processing chunk {i}: {str(chunk_error)}")
                    continue
            
            # Add source metadata
            source_query = f'MERGE (n:Source {{name: "{source}", created_at: datetime()}})'
            graph.query(source_query)
            
            # Create result
            return KnowledgeGraphResult(
                status="success",
                message=f"Created knowledge graph with {total_nodes} nodes and {total_relationships} relationships",
                total_nodes=total_nodes,
                total_relationships=total_relationships
            )
            
        except Exception as e:
            logger.error(f"Error creating knowledge graph: {str(e)}")
            return KnowledgeGraphResult(
                status="error",
                message=f"Error creating knowledge graph: {str(e)}",
                total_nodes=0,
                total_relationships=0
            )
    
    def get_cypher_query_only(self, query: str) -> KnowledgeGraphResult:
        """
        Generate only a Cypher query from a natural language question without executing it
        
        Args:
            query: Natural language query
            
        Returns:
            KnowledgeGraphResult with the Cypher query
        """
        try:
            # Get Neo4j graph connection for schema information
            graph = self.get_neo4j_graph()
            if not graph:
                raise ValueError("Neo4j connection not available")
                
            # Retrieve the graph schema
            schema = graph.get_schema
            
            # Create the schema-aware cypher generation prompt
            template = """
            Task: Generate a Cypher statement to query the graph database.

            Instructions:
            Use only relationship types and properties provided in schema.
            Do not use other relationship types or properties that are not provided.

            schema:
            {schema}

            Note: Do not include explanations or apologies in your answers.
            Do not answer questions that ask anything other than creating Cypher statements.
            Do not include any text other than generated Cypher statements.

            Question: {question}"""

            cypher_generation_prompt = PromptTemplate(
                template=template,
                input_variables=["schema", "question"]
            )
            
            # Use LLM to generate only the Cypher query
            llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_2_0_FLASH)
            
            # Generate the Cypher query
            cypher_response = llm.invoke(cypher_generation_prompt.format(schema=schema, question=query))
            
            # Extract the Cypher query
            cypher_query = cypher_response.content.strip()
            
            # Log the generated query
            logger.info(f"Generated Cypher query: {cypher_query}")
            
            # Return KnowledgeGraphResult object
            return KnowledgeGraphResult(
                status="success",
                message="Cypher query generated successfully",
                query=query,
                cypher=cypher_query,
                answer="Cypher query generated but not executed"
            )
            
        except Exception as e:
            logger.error(f"Error generating Cypher query: {str(e)}")
            return KnowledgeGraphResult(
                status="error",
                message=f"Error generating Cypher query: {str(e)}",
                query=query,
                answer=f"I encountered an error while generating a Cypher query: {str(e)}"
            )
    
    def query_knowledge_graph(self, query: str) -> KnowledgeGraphResult:
        """
        Query the knowledge graph
        
        Args:
            query: Natural language query
            
        Returns:
            KnowledgeGraphResult with query results (with serializable objects)
        """
        try:
            # Get Neo4j graph connection
            graph = self.get_neo4j_graph()
            if not graph:
                raise ValueError("Neo4j connection not available")
                
            # Retrieve the graph schema
            schema = graph.get_schema
            
            # Create the schema-aware question-answering chain with a custom prompt
            template = """
            Task: Generate a Cypher statement to query the graph database.

            Instructions:
            Use only relationship types and properties provided in schema.
            Do not use other relationship types or properties that are not provided.

            schema:
            {schema}

            Note: Do not include explanations or apologies in your answers.
            Do not answer questions that ask anything other than creating Cypher statements.
            Do not include any text other than generated Cypher statements.

            Question: {question}"""

            cypher_generation_prompt = PromptTemplate(
                template=template,
                input_variables=["schema", "question"]
            )
            
            qa_prompt = PromptTemplate.from_template(
                """
                You are a SuperSport API documentation assistant. Use the following context from the knowledge graph to answer the question.
                
                Context: {context}
                
                Question: {question}
                
                Based on the API documentation, provide ONLY the appropriate API endpoint path that answers the question.
                Do not include any other details, parameters, or explanations - just return the endpoint path.
                
                Answer:
                """
            )
                
            # Create the question-answering chain
            llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_2_0_FLASH)
            chain = GraphCypherQAChain.from_llm(
                llm=llm,
                graph=graph,
                cypher_prompt=cypher_generation_prompt,
                qa_prompt=qa_prompt,
                verbose=True,
                allow_dangerous_requests=True
            )
            
            # Execute the query
            result = chain.invoke({"query": query})
            cypher_query = result.get("cypher", "")
            logger.info(f"Generated Cypher query: {cypher_query}")
            logger.info(f"Knowledge graph response: {result}")
            
            # Serialize the result to handle Neo4j objects
            serialized_result = serialize_neo4j_objects(result)
            
            # Return the result with serialized objects
            return KnowledgeGraphResult(
                status="success",
                message="Query executed successfully",
                query=query,
                answer=serialized_result["result"],
                cypher=serialized_result.get("cypher", ""),
                context=serialize_neo4j_objects(serialized_result.get("context", []))
            )
            
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {str(e)}")
            return KnowledgeGraphResult(
                status="error",
                message=f"Error querying knowledge graph: {str(e)}",
                query=query,
                answer="I encountered an error while trying to answer your question."
            ) 