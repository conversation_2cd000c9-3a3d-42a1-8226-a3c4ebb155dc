[project]
name = "adk-tutorial"
version = "0.1.0"
description = "Add your description here"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "faiss-cpu>=1.11.0",
    "google-adk>=0.4.0",
    "google-generativeai>=0.8.5",
    "langchain>=0.3.25",
    "langchain-community>=0.3.23",
    "langchain-google-genai>=2.0.10",
    "langchain-groq>=0.3.2",
    "langchain-neo4j>=0.4.0",
    "langgraph>=0.4.1",
    "litellm>=1.68.0",
    "mcp>=1.7.1",
    "mcp-use>=1.2.8",
    "neo4j>=5.28.1",
    "pypdf2>=3.0.1",
    "python-dotenv>=1.1.0",
    "pytz>=2025.2",
    "requests>=2.32.3",
    "webvtt-py>=0.5.1",
]
