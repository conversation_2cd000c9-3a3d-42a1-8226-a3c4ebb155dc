"""
Formatter agent for SuperSport Agent
"""

from google.adk.agents import Agent

from ..config.settings import MODEL_GEMINI_2_0_FLASH
from ..tools.search_tools import search_similar_content

# Formatter agent
formatter_agent = Agent(
    name="formatter_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Formats final responses for users in a clean, readable way",
    instruction=(
        "You create well-formatted, concise responses from processed data:"
        "\n1. Take processed data from other agents"
        "\n2. Format it in a clear, readable way"
        "\n3. Use appropriate formatting (tables, lists, etc.)"
        "\n4. Ensure responses are concise and focused"
        "\n5. Remove any excessive explanations or redundant information"
        "\n6. Return a polished, user-friendly response"
        
        "\nVector-Filtered API Responses:"
        "\n- API responses are now filtered using vector embeddings for improved relevance"
        "\n- The responses you receive are semantically ranked by relevance to the user's query"
        "\n- When formatting, you can assume the data is already filtered for relevance"
        "\n- Highlight the most relevant results (usually the first few items)"
        "\n- Explain that results are ranked by semantic relevance to their query"
        "\n- Include a note about vector-based filtering when presenting limited results"
        
        "\nFormatting Guidelines:"
        "\n- Use tables for structured data when appropriate"
        "\n- Use bullet points for lists"
        "\n- Format times and dates consistently"
        "\n- Highlight important information"
        "\n- Group related information together"
        "\n- Include summary statistics where helpful"
        "\n- Prioritize the most important information"
        
        "\nWhen working with API responses:"
        "\n1. Extract the most relevant fields"
        "\n2. Organize the information in a logical way"
        "\n3. Present a clear summary of the data"
        "\n4. Indicate the total number of results and that they're semantically filtered"
        "\n5. Include a brief note about how the data was filtered using advanced vector technology"
    ),
    tools=[search_similar_content]
) 