"""
SuperSport API Service
"""

import requests
import urllib3
from typing import List, Dict, Optional, Any, Type, Union, TypeVar, Generic
from datetime import datetime, timed<PERSON>ta

from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS

from ..utils.logger import get_logger
from ..config.settings import (
    SUPER_SPORT_API_BASE_URL,
    GOOGLE_API_KEY,
    MAX_API_RESULTS,
    EMBEDDING_MODEL
)
from ..models.api_models import (
    ApiRequest, ApiResponse,
    TVGuideRequest, TVGuideResponse, SportEvent,
    ChannelsResponse, Channel,
    LiveSportsResponse,
    UpcomingSportsResponse,
    SportCategoryResponse,
    QueryRequest, QueryResponse, QueryResponseItem
)
from ..services.knowledge_graph_service import KnowledgeGraphService

# Suppress SSL warnings - only use this in development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Get logger for this module
logger = get_logger(__name__)

T = TypeVar('T', bound=ApiResponse)

class SuperSportApiService:
    """Service for interacting with the SuperSport API"""
    
    def __init__(self, base_url: Optional[str] = None, api_key: Optional[str] = None):
        """
        Initialize the SuperSport API service
        
        Args:
            base_url: Base URL for the SuperSport API. Defaults to the configured value.
            api_key: API key for authentication. Defaults to the configured value.
        """
        self.base_url = base_url or SUPER_SPORT_API_BASE_URL
        self.api_key = api_key or GOOGLE_API_KEY
        logger.info(f"Initialized SuperSport API service with base URL: {self.base_url}")
    
    def _make_request(self, 
                     endpoint: str, 
                     params: Optional[Dict[str, Any]] = None, 
                     response_model: Type[T] = ApiResponse,
                     query: Optional[str] = None) -> T:
        """
        Make a request to the SuperSport API
        
        Args:
            endpoint: The API endpoint to request (e.g., "tvguide")
            params: Optional query parameters
            response_model: Pydantic model to use for the response
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            The API response parsed into the specified model
        """
        url = f"{self.base_url}/{endpoint}"
        
        # Construct URL with parameters
        if params:
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            url = f"{url}?{query_string}"
        
        logger.info(f"Making request to {url}")
        
        try:
            # Disable SSL verification - this is a workaround for SSL certificate issues
            # In production, you should fix the certificate issue instead of disabling verification
            response = requests.get(url, verify=False)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Response received from {url}: {len(data) if isinstance(data, list) else 'single item'}")
            
            # For list responses, create the appropriate response object
            if isinstance(data, list):
                # Get the total number of items
                total_items = len(data)
                logger.info(f"Total items in response: {total_items}")
                
                # Limit data size to prevent token overflow
                if len(data) > MAX_API_RESULTS:
                    logger.info(f"Limiting response to {MAX_API_RESULTS} items to prevent token overflow")
                    limited_data = data[:MAX_API_RESULTS]
                else:
                    limited_data = data
                
                # Create the initial response object
                try:
                    api_response = response_model(
                        status="success",
                        data=limited_data,
                        total_items=total_items
                    )
                    return api_response
                except Exception as e:
                    logger.error(f"Error creating response model from list data: {str(e)}")
                    
                    # Attempt with the first few items only in case it's a size issue
                    if len(limited_data) > 5:
                        logger.info("Trying with only the first 5 items")
                        try:
                            api_response = response_model(
                                status="success",
                                data=limited_data[:5],
                                total_items=total_items
                            )
                            return api_response
                        except Exception:
                            logger.error("Still failed with reduced items")
                    
                    # As a fallback, return an error response
                    return response_model(
                        status="error", 
                        message=f"Error processing response data: {str(e)}",
                        data=[]
                    )
            else:
                # For single-item responses
                try:
                    # Create response model
                    api_response = response_model(
                        status="success",
                        data=[data]
                    )
                    
                    return api_response
                except Exception as e:
                    logger.error(f"Error creating response model: {str(e)}")
                    return response_model(
                        status="error", 
                        message=f"Error processing response: {str(e)}",
                        data=[]
                    )
        
        except requests.RequestException as e:
            logger.error(f"Request error: {str(e)}")
            return response_model(
                status="error",
                message=f"Request error: {str(e)}",
                data=[]
            )
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return response_model(
                status="error",
                message=f"Error parsing response: {str(e)}",
                data=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return response_model(
                status="error",
                message=f"Unexpected error: {str(e)}",
                data=[]
            )
    
    def get_tv_guide(self, request: TVGuideRequest, query: Optional[str] = None) -> TVGuideResponse:
        """
        Get TV guide from the SuperSport API
        
        Args:
            request: TV guide request parameters
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            TVGuideResponse: The TV guide response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("tvguide", params, TVGuideResponse, query)
    
    def get_live_sports(self, country_code: str, date: Optional[str] = None, query: Optional[str] = None) -> LiveSportsResponse:
        """
        Get live sports events currently broadcasting
        
        Args:
            country_code: Country code (e.g., "za" for South Africa)
            date: Date to check for live events (format: YYYY-MM-DD)
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            LiveSportsResponse: The live sports response
        """
        # For live sports, we use the TV guide endpoint with live_only=True and today's date
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")
        
        request = TVGuideRequest(
            countryCode=country_code,
            startDateTime=date,
            endDateTime=date,
            liveOnly=True
        )
        
        # Use the TV guide endpoint but return a LiveSportsResponse
        params = request.dict(exclude_none=True)
        return self._make_request("tvguide", params, LiveSportsResponse, query)
    
    def get_upcoming_sports(self, country_code: str, start_date: Optional[str] = None, 
                          end_date: Optional[str] = None, days_ahead: int = 7, 
                          query: Optional[str] = None) -> UpcomingSportsResponse:
        """
        Get upcoming sports events for the next X days
        
        Args:
            country_code: Country code (e.g., "za" for South Africa)
            start_date: Starting date (format: YYYY-MM-DD)
            end_date: Ending date (format: YYYY-MM-DD)
            days_ahead: Number of days to look ahead (only used if end_date is not provided)
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            UpcomingSportsResponse: The upcoming sports response
        """
        # Calculate date range if not provided
        if start_date is None:
            start_date = datetime.now().strftime("%Y-%m-%d")
            
        if end_date is None:
            # Calculate end date based on start date + days_ahead
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = start_dt + timedelta(days=days_ahead)
            end_date = end_dt.strftime("%Y-%m-%d")
        
        # Create request with date range
        request = TVGuideRequest(
            countryCode=country_code,
            startDateTime=start_date,
            endDateTime=end_date,
            liveOnly=False
        )
        
        # Use the TV guide endpoint but return an UpcomingSportsResponse
        params = request.dict(exclude_none=True)
        return self._make_request("tvguide", params, UpcomingSportsResponse, query)
    
    def get_sport_categories(self, country_code: str, query: Optional[str] = None) -> SportCategoryResponse:
        """
        Get sport categories from the SuperSport API
        
        Args:
            country_code: Country code (e.g., "za" for South Africa)
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            SportCategoryResponse: The sport categories response
        """
        # For categories, we'll use a dedicated endpoint if available, or extract from TV guide otherwise
        params = {"countryCode": country_code}
        try:
            # Try dedicated categories endpoint first
            return self._make_request("categories", params, SportCategoryResponse, query)
        except Exception:
            # Fall back to extracting from TV guide if dedicated endpoint fails
            logger.info("Categories endpoint failed, extracting from TV guide")
            
            # Get TV guide data for today
            today = datetime.now().strftime("%Y-%m-%d")
            guide_request = TVGuideRequest(
                countryCode=country_code,
                startDateTime=today,
                endDateTime=today
            )
            
            guide_response = self.get_tv_guide(guide_request)
            
            if guide_response.status == "success" and guide_response.data:
                # Extract unique sport categories
                categories = set()
                for event in guide_response.data:
                    categories.add(event.sport)
                
                # Return as SportCategoryResponse
                return SportCategoryResponse(
                    status="success",
                    data=sorted(list(categories)),
                    total_items=len(categories)
                )
            else:
                # Return error if TV guide call failed
                return SportCategoryResponse(
                    status="error",
                    message="Failed to get sport categories",
                    data=[]
                )
    
    def get_channels(self, country_code: str, query: Optional[str] = None) -> ChannelsResponse:
        """
        Get channels from the SuperSport API
        
        Args:
            country_code: Country code (e.g., "za" for South Africa)
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            ChannelsResponse: The channels response
        """
        # For channels, we'll use the TV guide with channelOnly=True
        request = TVGuideRequest(
            countryCode=country_code,
            startDateTime=datetime.now().strftime("%Y-%m-%d"),
            endDateTime=datetime.now().strftime("%Y-%m-%d"),
            channelOnly=True
        )
        
        params = request.dict(exclude_none=True)
        return self._make_request("tvguide", params, ChannelsResponse, query)
    
    def search_programs(self, country_code: str, search_query: str) -> TVGuideResponse:
        """
        Search for programs matching the query
        
        Args:
            country_code: Country code (e.g., "za" for South Africa)
            search_query: Text to search for in program titles and descriptions
            
        Returns:
            TVGuideResponse: The TV guide response with matching programs
        """
        params = {
            "countryCode": country_code,
            "search": search_query
        }
        return self._make_request("tvguide", params, TVGuideResponse)
    
    def call_direct_url(self, url: str) -> Any:
        """
        Call a complete API URL directly
        
        Args:
            url: Complete URL including base URL, endpoint path, and query parameters
            
        Returns:
            The raw API response data
        """
        logger.info(f"Making direct request to {url}")
        
        try:
            # Disable SSL verification - this is a workaround for SSL certificate issues
            # In production, you should fix the certificate issue instead of disabling verification
            response = requests.get(url, verify=False)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Direct API response received: {len(data) if isinstance(data, list) else 'single item'}")
            
            # Return the raw data
            return data
            
        except requests.RequestException as e:
            logger.error(f"Direct request error: {str(e)}")
            return {
                "status": "error",
                "message": f"Direct request error: {str(e)}"
            }
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return {
                "status": "error",
                "message": f"Error parsing response: {str(e)}"
            }
        except Exception as e:
            logger.error(f"Unexpected error in direct API call: {str(e)}")
            return {
                "status": "error",
                "message": f"Unexpected error: {str(e)}"
            }
    
    def query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query against multiple endpoints
        
        Args:
            request: The query request
            
        Returns:
            QueryResponse with aggregated results
        """
        query = request.query
        max_results = request.max_results
        
        # Start with empty response
        response = QueryResponse(
            status="success",
            query=query,
            data=[],
            endpoints_queried=[]
        )
        
        # Default country code (could be extracted from query in a more advanced implementation)
        country_code = "za"
        
        try:
            # Query TV guide
            tv_guide = self.get_tv_guide(
                TVGuideRequest(
                    countryCode=country_code,
                    startDateTime=datetime.now().strftime("%Y-%m-%d"),
                    endDateTime=(datetime.now() + timedelta(days=1)).strftime("%Y-%m-%d")
                ),
                query=query
            )
            
            if tv_guide.status == "success" and tv_guide.data:
                response.data.append(
                    QueryResponseItem(
                        source="tvguide",
                        data=tv_guide.data[:max_results]
                    )
                )
                response.endpoints_queried.append("tvguide")
            
            # Query live sports
            live_sports = self.get_live_sports(country_code, query=query)
            
            if live_sports.status == "success" and live_sports.data:
                response.data.append(
                    QueryResponseItem(
                        source="live_sports",
                        data=live_sports.data[:max_results]
                    )
                )
                response.endpoints_queried.append("live_sports")
            
            return response
            
        except Exception as e:
            logger.error(f"Error processing query: {str(e)}")
            return QueryResponse(
                status="error",
                message=f"Error processing query: {str(e)}",
                query=query,
                data=[],
                endpoints_queried=[]
            )

    def _process_response(self, response_json: Any, endpoint: str, query: Optional[str] = None) -> ApiResponse:
        """
        Process API response into a structured format
        
        Args:
            response_json: The JSON response from the API
            endpoint: The endpoint that was called
            query: Optional query for filtering results
            
        Returns:
            ApiResponse with structured data
        """
        try:
            # Process based on endpoint
            if "tvguide" in endpoint:
                return self._process_tv_guide(response_json, query)
            elif "live" in endpoint:
                return self._process_live_sports(response_json, query)
            elif "upcoming" in endpoint:
                return self._process_upcoming_sports(response_json, query)
            elif "categories" in endpoint:
                return self._process_sport_categories(response_json, query)
            elif "channels" in endpoint:
                return self._process_channels(response_json, query)
            elif "search" in endpoint:
                return self._process_search_results(response_json, query)
            else:
                # Generic processing for unknown endpoints
                return self._process_generic_response(response_json, query)
        except Exception as e:
            logger.error(f"Error processing API response: {str(e)}")
            return ApiResponse(
                status="error",
                message=f"Error processing response: {str(e)}",
                endpoint=endpoint,
                query=query,
                data=None
            )
            
    def vectorize_api_response(self, response: ApiResponse, query: str) -> ApiResponse:
        """
        Filter API response results based on relevance to query using text-based filtering
        instead of vector embeddings, as we're now using Neo4j for API documentation.
        
        Args:
            response: Original API response
            query: The user query to find relevant information
            
        Returns:
            Modified ApiResponse with only the most relevant data
        """
        try:
            # Check if we have data to filter
            if response.status != "success" or not response.data:
                return response
            
            data = response.data
            
            # Apply text-based filtering
            filtered_data = []
            query_lower = query.lower()
            
            for item in data:
                # Convert item to string representation for text matching
                item_str = str(item).lower()
                
                # Check if query appears in the item string
                if query_lower in item_str:
                    filtered_data.append(item)
            
            # If we have too few results with exact matching, use partial matching
            if len(filtered_data) < 5 and len(data) > 0:
                # Split query into words for more flexible matching
                query_words = query_lower.split()
                
                # Add items that match any query word
                for item in data:
                    if item in filtered_data:
                        continue  # Skip items already included
                        
                    item_str = str(item).lower()
                    
                    # Check if any query word appears in the item string
                    if any(word in item_str for word in query_words if len(word) > 3):
                        filtered_data.append(item)
                        
                        # Stop once we have enough results
                        if len(filtered_data) >= 10:
                            break
            
            # If still no results, return original data with a limit
            if not filtered_data and data:
                filtered_data = data[:10]
            
            # Create new response with filtered results
            return ApiResponse(
                status="success",
                message=f"Retrieved {len(filtered_data)} most relevant results via text-based filtering",
                endpoint=response.endpoint,
                query=query,
                data=filtered_data,
                total_results=len(filtered_data),
                original_total=len(data)
            )
            
        except Exception as e:
            logger.error(f"Error filtering API response: {str(e)}")
            # Fall back to original response
            return response

    def _process_tv_guide(self, response_json: Dict[str, Any], query: Optional[str] = None) -> TVGuideResponse:
        """
        Process TV guide API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            TVGuideResponse with structured data
        """
        try:
            # Extract data from response
            channels = response_json.get("channels", [])
            data = []
            
            for channel in channels:
                channel_id = channel.get("id")
                channel_name = channel.get("name")
                channel_number = channel.get("number")
                
                # Get events for channel
                events = channel.get("events", [])
                for event in events:
                    sport_event = SportEvent(
                        id=event.get("id"),
                        title=event.get("title"),
                        description=event.get("description"),
                        start_time=event.get("startTime"),
                        end_time=event.get("endTime"),
                        channel_id=channel_id,
                        channel_name=channel_name,
                        channel_number=channel_number,
                        sport=event.get("sport"),
                        competition=event.get("competition"),
                        is_live=event.get("isLive", False)
                    )
                    data.append(sport_event)
            
            # Apply text-based filtering if query is provided
            if query:
                filtered_data = []
                query_lower = query.lower()
                
                for event in data:
                    # Check if query appears in any of these fields
                    searchable_text = (
                        f"{event.title} {event.description} {event.sport} "
                        f"{event.competition} {event.channel_name}"
                    ).lower()
                    
                    if query_lower in searchable_text:
                        filtered_data.append(event)
                
                data = filtered_data
            
            # Create response
            result = TVGuideResponse(
                status="success",
                message=f"Retrieved {len(data)} events",
                endpoint="tvguide",
                query=query,
                data=data,
                total_results=len(data)
            )
            
            # If we have a query and more than MAX_API_RESULTS, use vectorization
            if query and len(data) > MAX_API_RESULTS:
                return self.vectorize_api_response(result, query)
                
            # Otherwise fall back to simple limiting if too many results
            if len(data) > MAX_API_RESULTS:
                logger.info(f"Limiting response to {MAX_API_RESULTS} items to prevent token overflow")
                limited_data = data[:MAX_API_RESULTS]
                return TVGuideResponse(
                    status="success",
                    message=f"Retrieved {len(data)} events (showing {len(limited_data)})",
                    endpoint="tvguide",
                    query=query,
                    data=limited_data,
                    total_results=len(data)
                )
            
            return result
                
        except Exception as e:
            logger.error(f"Error processing TV guide response: {str(e)}")
            return TVGuideResponse(
                status="error",
                message=f"Error processing TV guide response: {str(e)}",
                endpoint="tvguide",
                query=query,
                data=[]
            )

    def _process_live_sports(self, response_json: Dict[str, Any], query: Optional[str] = None) -> LiveSportsResponse:
        """
        Process live sports API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            LiveSportsResponse with structured data
        """
        try:
            # Extract data from response
            sports = response_json.get("sports", [])
            data = []
            
            for sport in sports:
                sport_name = sport.get("name")
                events = sport.get("events", [])
                
                for event in events:
                    sport_event = SportEvent(
                        id=event.get("id"),
                        title=event.get("title"),
                        description=event.get("description", ""),
                        start_time=event.get("startTime"),
                        end_time=event.get("endTime"),
                        channel_id=event.get("channelId"),
                        channel_name=event.get("channelName"),
                        channel_number=event.get("channelNumber"),
                        sport=sport_name,
                        competition=event.get("competition", ""),
                        is_live=True
                    )
                    data.append(sport_event)
            
            # Apply text-based filtering if query is provided
            if query:
                filtered_data = []
                query_lower = query.lower()
                
                for event in data:
                    searchable_text = (
                        f"{event.title} {event.description} {event.sport} "
                        f"{event.competition} {event.channel_name}"
                    ).lower()
                    
                    if query_lower in searchable_text:
                        filtered_data.append(event)
                
                data = filtered_data
            
            # Create response
            result = LiveSportsResponse(
                status="success",
                message=f"Retrieved {len(data)} live events",
                endpoint="live",
                query=query,
                data=data,
                total_results=len(data)
            )
            
            # If we have a query and more than MAX_API_RESULTS, use vectorization
            if query and len(data) > MAX_API_RESULTS:
                return self.vectorize_api_response(result, query)
            
            # Otherwise fall back to simple limiting if too many results
            if len(data) > MAX_API_RESULTS:
                logger.info(f"Limiting response to {MAX_API_RESULTS} items to prevent token overflow")
                limited_data = data[:MAX_API_RESULTS]
                return LiveSportsResponse(
                    status="success",
                    message=f"Retrieved {len(data)} live events (showing {len(limited_data)})",
                    endpoint="live",
                    query=query,
                    data=limited_data,
                    total_results=len(data)
                )
            
            return result
                
        except Exception as e:
            logger.error(f"Error processing live sports response: {str(e)}")
            return LiveSportsResponse(
                status="error",
                message=f"Error processing live sports response: {str(e)}",
                endpoint="live",
                query=query,
                data=[]
            )

    def _process_upcoming_sports(self, response_json: Dict[str, Any], query: Optional[str] = None) -> UpcomingSportsResponse:
        """
        Process upcoming sports API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            UpcomingSportsResponse with structured data
        """
        try:
            # Extract data from response
            upcoming = response_json.get("upcoming", [])
            data = []
            
            for event in upcoming:
                sport_event = SportEvent(
                    id=event.get("id"),
                    title=event.get("title"),
                    description=event.get("description", ""),
                    start_time=event.get("startTime"),
                    end_time=event.get("endTime"),
                    channel_id=event.get("channelId"),
                    channel_name=event.get("channelName"),
                    channel_number=event.get("channelNumber"),
                    sport=event.get("sport", ""),
                    competition=event.get("competition", ""),
                    is_live=False
                )
                data.append(sport_event)
            
            # Apply text-based filtering if query is provided
            if query:
                filtered_data = []
                query_lower = query.lower()
                
                for event in data:
                    searchable_text = (
                        f"{event.title} {event.description} {event.sport} "
                        f"{event.competition} {event.channel_name}"
                    ).lower()
                    
                    if query_lower in searchable_text:
                        filtered_data.append(event)
                
                data = filtered_data
            
            # Create response
            result = UpcomingSportsResponse(
                status="success",
                message=f"Retrieved {len(data)} upcoming events",
                endpoint="upcoming",
                query=query,
                data=data,
                total_results=len(data)
            )
            
            # If we have a query and more than MAX_API_RESULTS, use vectorization
            if query and len(data) > MAX_API_RESULTS:
                return self.vectorize_api_response(result, query)
            
            # Otherwise fall back to simple limiting if too many results
            if len(data) > MAX_API_RESULTS:
                logger.info(f"Limiting response to {MAX_API_RESULTS} items to prevent token overflow")
                limited_data = data[:MAX_API_RESULTS]
                return UpcomingSportsResponse(
                    status="success",
                    message=f"Retrieved {len(data)} upcoming events (showing {len(limited_data)})",
                    endpoint="upcoming",
                    query=query,
                    data=limited_data,
                    total_results=len(data)
                )
            
            return result
                
        except Exception as e:
            logger.error(f"Error processing upcoming sports response: {str(e)}")
            return UpcomingSportsResponse(
                status="error",
                message=f"Error processing upcoming sports response: {str(e)}",
                endpoint="upcoming",
                query=query,
                data=[]
            )

    def _process_sport_categories(self, response_json: Dict[str, Any], query: Optional[str] = None) -> SportCategoryResponse:
        """
        Process sport categories API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            SportCategoryResponse with structured data
        """
        try:
            # Extract categories from response
            categories = response_json.get("categories", [])
            
            # Apply text-based filtering if query is provided
            if query:
                filtered_categories = []
                query_lower = query.lower()
                
                for category in categories:
                    category_name = category.get("name", "").lower()
                    if query_lower in category_name:
                        filtered_categories.append(category)
                
                categories = filtered_categories
            
            # Create response
            return SportCategoryResponse(
                status="success",
                message=f"Retrieved {len(categories)} sport categories",
                endpoint="categories",
                query=query,
                data=categories,
                total_results=len(categories)
            )
                
        except Exception as e:
            logger.error(f"Error processing sport categories response: {str(e)}")
            return SportCategoryResponse(
                status="error",
                message=f"Error processing sport categories response: {str(e)}",
                endpoint="categories",
                query=query,
                data=[]
            )

    def _process_channels(self, response_json: Dict[str, Any], query: Optional[str] = None) -> ChannelsResponse:
        """
        Process channels API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            ChannelsResponse with structured data
        """
        try:
            # Extract channels from response
            channels_data = response_json.get("channels", [])
            channels = []
            
            for channel_data in channels_data:
                channel = Channel(
                    id=channel_data.get("id"),
                    name=channel_data.get("name"),
                    number=channel_data.get("number"),
                    logo_url=channel_data.get("logoUrl")
                )
                channels.append(channel)
            
            # Apply text-based filtering if query is provided
            if query:
                filtered_channels = []
                query_lower = query.lower()
                
                for channel in channels:
                    if query_lower in channel.name.lower():
                        filtered_channels.append(channel)
                
                channels = filtered_channels
            
            # Create response
            result = ChannelsResponse(
                status="success",
                message=f"Retrieved {len(channels)} channels",
                endpoint="channels",
                query=query,
                data=channels,
                total_results=len(channels)
            )
            
            # If we have a query and more than MAX_API_RESULTS, use vectorization
            if query and len(channels) > MAX_API_RESULTS:
                return self.vectorize_api_response(result, query)
            
            return result
                
        except Exception as e:
            logger.error(f"Error processing channels response: {str(e)}")
            return ChannelsResponse(
                status="error",
                message=f"Error processing channels response: {str(e)}",
                endpoint="channels",
                query=query,
                data=[]
            )

    def _process_search_results(self, response_json: Dict[str, Any], query: Optional[str] = None) -> QueryResponse:
        """
        Process search results API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            QueryResponse with structured data
        """
        try:
            # Extract search results from response
            results = response_json.get("results", [])
            
            # Create response
            result = QueryResponse(
                status="success",
                message=f"Retrieved {len(results)} search results",
                endpoint="search",
                query=query,
                data=results,
                total_results=len(results)
            )
            
            # If we have more than MAX_API_RESULTS, use vectorization
            if len(results) > MAX_API_RESULTS:
                return self.vectorize_api_response(result, query or "")
            
            return result
                
        except Exception as e:
            logger.error(f"Error processing search results response: {str(e)}")
            return QueryResponse(
                status="error",
                message=f"Error processing search results response: {str(e)}",
                endpoint="search",
                query=query,
                data=[]
            )

    def _process_generic_response(self, response_json: Dict[str, Any], query: Optional[str] = None) -> ApiResponse:
        """
        Process generic API response
        
        Args:
            response_json: The JSON response from the API
            query: Optional query for filtering results
            
        Returns:
            ApiResponse with structured data
        """
        try:
            # Try to extract data or use the entire response
            data = response_json.get("data", response_json)
            
            # Create response
            return ApiResponse(
                status="success",
                message="Retrieved API response",
                endpoint="generic",
                query=query,
                data=data
            )
                
        except Exception as e:
            logger.error(f"Error processing generic response: {str(e)}")
            return ApiResponse(
                status="error",
                message=f"Error processing response: {str(e)}",
                endpoint="generic",
                query=query,
                data=None
            ) 