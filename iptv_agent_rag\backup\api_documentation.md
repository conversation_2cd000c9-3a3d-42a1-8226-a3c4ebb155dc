# IPTV.org API Documentation

## Base URL
```
https://iptv-org.github.io/api
```

## Available Endpoints

### Channels API
**Endpoint**: `GET /channels.json`

Returns a list of all TV channels with detailed information.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/channels.json
```

**Response Format**:
```json
[
  {
    "id": "AnhuiTV.cn",
    "name": "Anhui TV",
    "alt_names": ["安徽卫视"],
    "network": "Anhui",
    "owners": ["China Central Television"],
    "country": "CN",
    "subdivision": "CN-AH",
    "city": "Hefei",
    "categories": ["general"],
    "is_nsfw": false,
    "launched": "2016-07-28",
    "closed": "2020-05-31",
    "replaced_by": "CCTV1.cn",
    "website": "http://www.ahtv.cn/",
    "logo": "https://example.com/logo.png"
  }
]
```

**Field Descriptions**:

Each channel object contains the following properties:

The `id` field contains a unique string identifier for the channel. This is used across the API to reference this specific channel.

The `name` field is a string containing the full official name of the TV channel.

The `alt_names` field is an array of strings representing alternative names by which the channel might be known, including names in different languages.

The `network` field is either a string representing the name of the larger network operating the channel, or null if the channel is independent.

The `owners` field is an array of strings listing the companies or entities that own the channel.

The `country` field is a string containing the ISO 3166-1 alpha-2 country code (such as "US" or "CN") from which the broadcast is transmitted.

The `subdivision` field is either a string containing the ISO 3166-2 code for the specific province, state, or region from which the broadcast is transmitted, or null if not applicable.

The `city` field is either a string with the name of the city from which the broadcast is transmitted, or null if not specified.

The `categories` field is an array of strings representing the content categories to which this channel belongs, such as "news", "sports", or "general".

The `is_nsfw` field is a boolean value that indicates whether the channel broadcasts adult content (true) or not (false).

The `launched` field is either a string in the format "YYYY-MM-DD" representing the channel's launch date, or null if unknown.

The `closed` field is either a string in the format "YYYY-MM-DD" representing the date the channel stopped broadcasting, or null if the channel is still active.

The `replaced_by` field is either a string containing the ID of another channel that replaced this one, or null if not applicable.

The `website` field is either a string containing the URL of the channel's official website, or null if not available.

The `logo` field is a string containing the URL to the channel's logo image.

### Feeds API
**Endpoint**: `GET /feeds.json`

Returns a list of all available TV feeds.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/feeds.json
```

**Response Format**:
```json
[
  {
    "channel": "BBCOne.uk",
    "id": "EastMidlandsHD",
    "name": "East Midlands HD",
    "is_main": false,
    "broadcast_area": ["c/UK"],
    "timezones": ["Europe/London"],
    "languages": ["eng"],
    "format": "1080i"
  }
]
```

**Field Descriptions**:

Each feed object contains the following properties:

The `channel` field is a string containing the ID of the channel to which this feed belongs. This ID corresponds to the `id` field in the Channels API.

The `id` field is a string that uniquely identifies this specific feed within the channel.

The `name` field is a string containing the human-readable name of the feed, which often includes regional identifiers or quality descriptors.

The `is_main` field is a boolean value that indicates whether this is the main broadcast feed for the channel (true) or a regional or alternative feed (false).

The `broadcast_area` field is an array of strings representing the areas where this feed is available. Areas prefixed with "c/" refer to countries (e.g., "c/UK"), while those prefixed with "s/" refer to subdivisions.

The `timezones` field is an array of strings listing the IANA timezone identifiers (such as "Europe/London") where this feed operates.

The `languages` field is an array of strings containing the ISO 639-3 language codes (such as "eng" for English) used in this feed's broadcasts.

The `format` field is a string describing the video format of the feed, such as "1080i", "720p", or "576i".

### Streams API
**Endpoint**: `GET /streams.json`

Returns a list of available stream URLs for channels.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/streams.json
```

**Response Format**:
```json
[
  {
    "channel": "BBCOne.uk",
    "feed": "EastMidlandsHD",
    "url": "http://1111296894.rsc.cdn77.org/LS-ATL-54548-6/index.m3u8",
    "referrer": "http://example.com/",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "quality": "720p"
  }
]
```

**Field Descriptions**:

Each stream object contains the following properties:

The `channel` field is a string containing the channel ID for which this stream is available. This corresponds to the `id` field in the Channels API.

The `feed` field is a string identifying the specific feed of the channel that this stream represents. This may be null if not specified or if it's the main feed.

The `url` field is a string containing the actual stream URL that can be used to access the channel's content.

The `referrer` field is either a string containing a URL that should be set as the HTTP referrer when accessing the stream, or null if no specific referrer is required.

The `user_agent` field is either a string containing the User-Agent header value that should be used when requesting the stream, or null if not required.

The `quality` field is either a string describing the quality of the stream (such as "720p" or "1080p"), or null if the quality is unknown.

### Guides API
**Endpoint**: `GET /guides.json`

Returns a list of channel guide information.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/guides.json
```

**Response Format**:
```json
[
  {
    "channel": "BBCOne.uk",
    "feed": "EastMidlandsHD",
    "site": "sky.co.uk",
    "site_id": "bbcone",
    "site_name": "BBC One",
    "lang": "en"
  }
]
```

**Field Descriptions**:

Each guide object contains the following properties:

The `channel` field is a string containing the ID of the channel for which guide information is available. This corresponds to the `id` field in the Channels API.

The `feed` field is either a string identifying the specific feed of the channel for which this guide applies, or null if it applies to all feeds.

The `site` field is a string identifying the website or service that provides the program guide data.

The `site_id` field is a string containing the ID used by the site to identify this channel in its system.

The `site_name` field is a string containing the name of the channel as it appears on the site providing the guide data.

The `lang` field is a string containing the ISO 639-1 two-letter language code (such as "en" for English) in which the guide data is provided.

### Categories API
**Endpoint**: `GET /categories.json`

Returns a list of all channel categories.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/categories.json
```

**Response Format**:
```json
[
  {
    "id": "documentary",
    "name": "Documentary"
  }
]
```

**Field Descriptions**:

Each category object contains the following properties:

The `id` field is a string containing a unique identifier for the category. This ID is used in the `categories` array of channel objects.

The `name` field is a string containing the human-readable name of the category, such as "Documentary", "News", or "Sports".

### Languages API
**Endpoint**: `GET /languages.json`

Returns a list of all languages used in the EPG system.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/languages.json
```

**Response Format**:
```json
[
  {
    "name": "French",
    "code": "fra"
  }
]
```

**Field Descriptions**:

Each language object contains the following properties:

The `name` field is a string containing the English name of the language, such as "French" or "Spanish".

The `code` field is a string containing the ISO 639-3 three-letter language code, such as "fra" for French or "spa" for Spanish.

### Countries API
**Endpoint**: `GET /countries.json`

Returns a list of all countries in the EPG system.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/countries.json
```

**Response Format**:
```json
[
  {
    "name": "Canada",
    "code": "CA",
    "languages": ["eng", "fra"],
    "flag": "🇨🇦"
  }
]
```

**Field Descriptions**:

Each country object contains the following properties:

The `name` field is a string containing the English name of the country, such as "Canada" or "United States".

The `code` field is a string containing the ISO 3166-1 alpha-2 two-letter country code, such as "CA" for Canada or "US" for United States.

The `languages` field is an array of strings containing the ISO 639-3 three-letter codes for the official languages of the country.

The `flag` field is a string containing the emoji representing the country's flag.

### Subdivisions API
**Endpoint**: `GET /subdivisions.json`

Returns a list of subdivisions (states, provinces, etc.).

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/subdivisions.json
```

**Response Format**:
```json
[
  {
    "country": "CA",
    "name": "Ontario",
    "code": "CA-ON"
  }
]
```

**Field Descriptions**:

Each subdivision object contains the following properties:

The `country` field is a string containing the ISO 3166-1 alpha-2 two-letter country code of the parent country.

The `name` field is a string containing the English name of the subdivision, such as "Ontario" or "California".

The `code` field is a string containing the ISO 3166-2 code for the subdivision, which typically consists of the country code, a hyphen, and a subdivision identifier (e.g., "CA-ON" for Ontario, Canada).

### Regions API
**Endpoint**: `GET /regions.json`

Returns a list of geographic regions with their countries.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/regions.json
```

**Response Format**:
```json
[
  {
    "code": "MAGHREB",
    "name": "Maghreb",
    "countries": ["DZ", "LY", "MA", "MR", "TN"]
  }
]
```

**Field Descriptions**:

Each region object contains the following properties:

The `code` field is a string containing a unique identifier for the region, typically in uppercase letters.

The `name` field is a string containing the English name of the region, such as "Maghreb" or "Scandinavia".

The `countries` field is an array of strings containing the ISO 3166-1 alpha-2 two-letter country codes for all countries that belong to this region.

### Timezones API
**Endpoint**: `GET /timezones.json`

Returns a list of timezones used in the EPG system.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/timezones.json
```

**Response Format**:
```json
[
  {
    "id": "Europe/London",
    "utc_offset": "+00:00",
    "countries": ["UK", "GG", "IM", "JE"]
  }
]
```

**Field Descriptions**:

Each timezone object contains the following properties:

The `id` field is a string containing the IANA Time Zone Database identifier, such as "Europe/London" or "America/New_York".

The `utc_offset` field is a string representing the timezone's offset from UTC in the format "+HH:MM" or "-HH:MM".

The `countries` field is an array of strings containing the ISO 3166-1 alpha-2 two-letter country codes for countries that use this timezone.

### Blocklist API
**Endpoint**: `GET /blocklist.json`

Returns a list of blocked channels and the reasons.

**Parameters**: None

**Example Request**:
```
GET https://iptv-org.github.io/api/blocklist.json
```

**Response Format**:
```json
[
  {
    "channel": "AnimalPlanetEast.us",
    "reason": "dmca",
    "ref": "https://github.com/iptv-org/iptv/issues/1831"
  }
]
```

**Field Descriptions**:

Each blocklist entry contains the following properties:

The `channel` field is a string containing the ID of the blocked channel. This corresponds to the `id` field in the Channels API.

The `reason` field is a string describing why the channel is blocked, such as "dmca" for copyright violations or "geo" for geographic restrictions.

The `ref` field is a string containing a URL that references more information about the blocking reason, typically linking to an issue in the project's repository.

## Common API Combinations for User Queries

### Finding Channels by Country

To find all channels from a specific country (e.g., UK), you should first retrieve the complete list of channels from the Channels API. Once you have the full channel list, you can filter it to include only those channels where the `country` field equals "UK". This will give you all TV channels that broadcast from the United Kingdom.

```
GET https://iptv-org.github.io/api/channels.json
```

Then filter the results where the country field equals "UK".

### Finding Stream URLs for a Specific Channel

To find all available stream URLs for a specific channel like BBC One, follow these steps:

First, retrieve the channel ID from the Channels API by getting the full list of channels and finding the entry for BBC One, which would have an ID like "BBCOne.uk".

```
GET https://iptv-org.github.io/api/channels.json
```

Next, retrieve all available streams from the Streams API:

```
GET https://iptv-org.github.io/api/streams.json
```

Finally, filter the streams response to include only entries where the `channel` field equals "BBCOne.uk". This will give you all available stream URLs for BBC One, potentially including different feeds or quality levels.

### Finding Channels by Category

To find all channels of a specific category, such as Documentary channels:

First, retrieve the list of categories to confirm the correct category ID:

```
GET https://iptv-org.github.io/api/categories.json
```

This will confirm that the ID for Documentary is "documentary".

Next, retrieve all channels:

```
GET https://iptv-org.github.io/api/channels.json
```

Finally, filter the channels to include only those where the `categories` array includes "documentary". This will give you all documentary channels.

### Finding Channels by Language

To find all channels that broadcast in a specific language, such as French:

First, retrieve the list of languages to get the correct language code:

```
GET https://iptv-org.github.io/api/languages.json
```

This will show that the code for French is "fra".

Next, retrieve all feeds:

```
GET https://iptv-org.github.io/api/feeds.json
```

Finally, filter the feeds to include only those where the `languages` array includes "fra". This will give you all feeds that broadcast in French.

### Finding Regional Broadcasts

To find regional broadcasts for a specific country:

Retrieve all feeds:

```
GET https://iptv-org.github.io/api/feeds.json
```

Then filter the results to include only feeds where the `broadcast_area` array contains the country code with the "c/" prefix, such as "c/UK" for the United Kingdom. This will give you all feeds specifically broadcast in that country.

### Finding TV Guide Information for a Channel

To find TV guide information for a specific channel:

Retrieve all guide data:

```
GET https://iptv-org.github.io/api/guides.json
```

Then filter the results to include only entries where the `channel` field equals the channel ID you're interested in, such as "BBCOne.uk". This will provide information about which sites offer program guide data for that channel.

## Advanced API Combinations

### 1. Channels + Streams + Guides Combination
**Use Case**: Get complete channel information with available streams and program guide sources.

**Steps**:
1. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

2. Get all streams:
```
GET https://iptv-org.github.io/api/streams.json
```

3. Get all guides:
```
GET https://iptv-org.github.io/api/guides.json
```

4. For each channel of interest, join the data by matching the `id` field from channels with the `channel` field in both streams and guides.

**Example Result**:
```json
{
  "channel": {
    "id": "BBCOne.uk",
    "name": "BBC One",
    "country": "UK",
    "categories": ["general"],
    "logo": "https://example.com/bbcone.png"
  },
  "streams": [
    {
      "feed": "EastMidlandsHD",
      "url": "http://example.com/bbc1.m3u8",
      "quality": "720p"
    }
  ],
  "guides": [
    {
      "feed": "EastMidlandsHD",
      "site": "sky.co.uk",
      "site_id": "bbcone"
    }
  ]
}
```

### 2. Countries + Channels + Categories Combination
**Use Case**: Find channels from specific countries filtered by category.

**Steps**:
1. Get list of countries:
```
GET https://iptv-org.github.io/api/countries.json
```

2. Get list of categories:
```
GET https://iptv-org.github.io/api/categories.json
```

3. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

4. Filter channels where `country` matches desired country code AND `categories` array contains the desired category ID.

**Example**: Finding all sports channels from Canada:
```javascript
// Filter channels where country="CA" AND "sports" is in categories array
const sportsChannelsFromCanada = channels.filter(channel => 
  channel.country === "CA" && 
  channel.categories.includes("sports")
);
```

### 3. Regions + Countries + Channels Combination
**Use Case**: Find all channels from a geographic region.

**Steps**:
1. Get list of regions:
```
GET https://iptv-org.github.io/api/regions.json
```

2. From the regions response, select a region and note its `countries` array.

3. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

4. Filter channels where the `country` field matches any country code in the region's `countries` array.

**Example**: Finding all channels from the Maghreb region:
```javascript
// First, get the Maghreb region's country codes: ["DZ", "LY", "MA", "MR", "TN"]
const maghrebCountries = regions.find(region => region.code === "MAGHREB").countries;

// Then filter channels that belong to any of these countries
const maghrebChannels = channels.filter(channel => 
  maghrebCountries.includes(channel.country)
);
```

### 4. Languages + Feeds + Streams Combination
**Use Case**: Find streams of channels broadcasting in specific languages.

**Steps**:
1. Get list of languages:
```
GET https://iptv-org.github.io/api/languages.json
```

2. Get all feeds:
```
GET https://iptv-org.github.io/api/feeds.json
```

3. Get all streams:
```
GET https://iptv-org.github.io/api/streams.json
```

4. Filter feeds where the `languages` array includes the desired language code.
5. Join with streams data to get the actual stream URLs.

**Example**: Finding French language streams:
```javascript
// Find feed IDs where languages include "fra"
const frenchFeeds = feeds.filter(feed => 
  feed.languages.includes("fra")
);

// Get channel and feed IDs for matching
const frenchChannelFeedPairs = frenchFeeds.map(feed => ({
  channel: feed.channel,
  feed: feed.id
}));

// Find all streams matching these channel/feed combinations
const frenchStreams = streams.filter(stream => 
  frenchChannelFeedPairs.some(pair => 
    stream.channel === pair.channel && 
    (stream.feed === pair.feed || (stream.feed === null && pair.is_main))
  )
);
```

### 5. Timezones + Feeds + Channels Combination
**Use Case**: Find channels broadcasting in a specific timezone.

**Steps**:
1. Get list of timezones:
```
GET https://iptv-org.github.io/api/timezones.json
```

2. Get all feeds:
```
GET https://iptv-org.github.io/api/feeds.json
```

3. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

4. Filter feeds where the `timezones` array includes the desired timezone ID.
5. Join with channels data to get the channel details.

**Example**: Finding channels broadcasting in America/New_York timezone:
```javascript
// Filter feeds where timezones include "America/New_York"
const nyTimezoneFeeds = feeds.filter(feed => 
  feed.timezones.includes("America/New_York")
);

// Get unique channel IDs
const nyChannelIds = [...new Set(nyTimezoneFeeds.map(feed => feed.channel))];

// Find channel details
const nyChannels = channels.filter(channel => 
  nyChannelIds.includes(channel.id)
);
```

### 6. Subdivisions + Feeds + Streams Combination
**Use Case**: Find streams available in specific regional subdivisions.

**Steps**:
1. Get list of subdivisions:
```
GET https://iptv-org.github.io/api/subdivisions.json
```

2. Get all feeds:
```
GET https://iptv-org.github.io/api/feeds.json
```

3. Get all streams:
```
GET https://iptv-org.github.io/api/streams.json
```

4. For a subdivision of interest (e.g., "CA-ON" for Ontario, Canada), filter feeds where the `broadcast_area` array includes an entry of the format "s/CA-ON".
5. Join with streams data to get the stream URLs.

**Example**: Finding streams for Ontario, Canada:
```javascript
// Filter feeds broadcasting in Ontario
const ontarioFeeds = feeds.filter(feed => 
  feed.broadcast_area.some(area => area === "s/CA-ON")
);

// Get channel and feed IDs
const ontarioChannelFeeds = ontarioFeeds.map(feed => ({
  channel: feed.channel,
  feed: feed.id
}));

// Find matching streams
const ontarioStreams = streams.filter(stream => 
  ontarioChannelFeeds.some(pair => 
    stream.channel === pair.channel && 
    (stream.feed === pair.feed || stream.feed === null)
  )
);
```

### 7. Blocklist + Channels + Streams Combination
**Use Case**: Filter out blocked channels from results.

**Steps**:
1. Get blocklist:
```
GET https://iptv-org.github.io/api/blocklist.json
```

2. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

3. Get all streams:
```
GET https://iptv-org.github.io/api/streams.json
```

4. Create a list of blocked channel IDs from the blocklist.
5. Filter channels and streams to exclude any that match these IDs.

**Example**: Getting all available non-blocked channels and their streams:
```javascript
// Get all blocked channel IDs
const blockedChannelIds = blocklist.map(item => item.channel);

// Filter out blocked channels
const allowedChannels = channels.filter(channel => 
  !blockedChannelIds.includes(channel.id)
);

// Filter out streams for blocked channels
const allowedStreams = streams.filter(stream => 
  !blockedChannelIds.includes(stream.channel)
);
```

### 8. Categories + Languages + Streams Combination
**Use Case**: Find streams of channels in specific categories and languages.

**Steps**:
1. Get list of categories:
```
GET https://iptv-org.github.io/api/categories.json
```

2. Get all channels:
```
GET https://iptv-org.github.io/api/channels.json
```

3. Get all feeds:
```
GET https://iptv-org.github.io/api/feeds.json
```

4. Get all streams:
```
GET https://iptv-org.github.io/api/streams.json
```

5. Filter channels by desired category.
6. Filter feeds associated with these channels by desired language.
7. Get streams for the resulting channel/feed combinations.

**Example**: Finding streams for news channels in English:
```javascript
// Find news channels
const newsChannels = channels.filter(channel => 
  channel.categories.includes("news")
);

// Get their IDs
const newsChannelIds = newsChannels.map(channel => channel.id);

// Find feeds for these channels in English
const englishNewsFeeds = feeds.filter(feed => 
  newsChannelIds.includes(feed.channel) && 
  feed.languages.includes("eng")
);

// Get channel/feed pairs
const englishNewsPairs = englishNewsFeeds.map(feed => ({
  channel: feed.channel,
  feed: feed.id
}));

// Find matching streams
const englishNewsStreams = streams.filter(stream => 
  englishNewsPairs.some(pair => 
    stream.channel === pair.channel && 
    (stream.feed === pair.feed || stream.feed === null)
  )
);
```

## Reference
- [IPTV.org API GitHub Repository](https://github.com/iptv-org/api) 