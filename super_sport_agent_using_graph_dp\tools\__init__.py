"""
Tools for SuperSport Agent
"""

from .api_tools import (
    super_sport_get_tv_guide,
    super_sport_get_live_sports,
    super_sport_get_upcoming_sports,
    super_sport_get_sport_categories,
    super_sport_get_channels,
    super_sport_search_programs,
    super_sport_query,
    API_TOOLS
)

from .knowledge_graph_tools import (
    list_files_in_documents_dir,
    process_all_files,
    process_file,
    process_and_create_knowledge_graph,
    create_knowledge_graph_from_text,
    ask_question,
    KNOWLEDGE_GRAPH_TOOLS
)

# Collect all tools for easy access
ALL_TOOLS = API_TOOLS + KNOWLEDGE_GRAPH_TOOLS

__all__ = [
    # API tools
    "super_sport_get_tv_guide",
    "super_sport_get_live_sports",
    "super_sport_get_upcoming_sports",
    "super_sport_get_sport_categories",
    "super_sport_get_channels",
    "super_sport_search_programs",
    "super_sport_query",
    "API_TOOLS",
    
    # Knowledge Graph tools
    "list_files_in_documents_dir",
    "process_all_files",
    "process_file",
    "process_and_create_knowledge_graph",
    "create_knowledge_graph_from_text",
    "ask_question",
    "KNOWLEDGE_GRAPH_TOOLS",
    
    # All tools
    "ALL_TOOLS"
] 