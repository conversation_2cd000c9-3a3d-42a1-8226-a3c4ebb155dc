# SuperSport Agent with API Documentation Processing

This project provides an intelligent agent for processing and answering questions about SuperSport API documentation by identifying the exact API endpoints to call based on documentation context.

## Overview

The system consists of two main components in a hierarchical agent structure:

1. **Root Agent**: Processes user queries based on API documentation
2. **API Identifier Subagent**: Identifies the exact API endpoint to call from the API documentation

## Key Features

- Documentation-driven API identification
- Hierarchical agent architecture using Google's ADK (Agent Development Kit)
- Root agent delegates API identification to a specialized subagent
- RAG capabilities using local documents for API documentation
- Exact API endpoint identification from documentation

## Setup

### Requirements

- Python 3.8+
- Required packages: langchain, langchain_google_genai, PyPDF2, webvtt, FAISS, requests, google-generativeai, google-adk

### Installation

1. Install the required packages:

```bash
pip install langchain langchain_google_genai langchain_community PyPDF2 webvtt-py faiss-cpu google-generativeai requests pydantic google-adk
```

2. Set up environment variables:

```bash
export GOOGLE_API_KEY=your_google_api_key
```

### Starting the System

Run the web interface to test the agent's capabilities:

```bash
cd super_sport_agent
# Web interface command here
```

## Agent Architecture

The system uses a hierarchical agent architecture:

### Root Agent

The main agent that:
1. Processes user queries
2. Retrieves relevant API documentation
3. Delegates to the API Identifier subagent
4. Executes the identified API call
5. Presents results to the user

### API Identifier Subagent

A specialized subagent that:
1. Analyzes API documentation context
2. Identifies the exact API endpoint to call
3. Extracts endpoint URL and parameters
4. Explains which API to use and why

## Using the Agent

The agent provides several tools for working with SuperSport data:

### Document Processing Tools

- `list_files_in_documents_dir()`: List available document files
- `process_all_files()`: Process all documents in the directory
- `process_file(file_path)`: Process a specific document file
- `create_vector_store_from_text(text)`: Process text content into a vector store

### API Tools

- `super_sport_get_tv_guide()`: Get TV guide data from the SuperSport API
- `super_sport_get_live_sports()`: Get currently broadcasting live sports events
- `super_sport_get_upcoming_sports()`: Get upcoming sports events
- `super_sport_get_past_matches()`: Get historical sports matches from past dates
- `super_sport_get_sport_categories()`: Get sport categories available in the API
- `super_sport_get_channels()`: Get all channels from the SuperSport API
- `super_sport_search_programs()`: Search for programs matching a query
- `super_sport_query()`: Query SuperSport data using natural language

### Search and Q&A Tools

- `search_similar_content(query, num_results)`: Search for relevant content in processed documents
- `ask_question(question)`: Ask a question about the processed documents

## Advanced Date Handling

The agent supports natural language date formats in all API queries:

### Future Date Formats
- `today` - Current date
- `tomorrow` - Next day
- `today+N` - N days from now (e.g., "today+3" for 3 days ahead)

### Past Date Formats
- `yesterday` - Previous day
- `today-N` - N days before today (e.g., "today-5" for 5 days ago)
- `N days ago` - N days before today (e.g., "3 days ago" for 3 days in the past)
- `last week` - 7 days ago
- `last month` - 30 days ago (approximate)
- `last year` - 365 days ago (approximate)

### Natural Calendar Dates
- Month-day formats like "May 10" or "June 15"
- Day-month formats like "10 May" or "15 June"

These date formats are automatically converted to the proper YYYY-MM-DD format required by the API.

## Example Queries

Once documents are processed, you can ask questions like:

- "What football matches are on TV tonight?"
- "When is the next rugby match?"
- "What's on SuperSport School HD channel today?"
- "Which channels are showing live football?"
- "What school hockey matches are on this weekend?"
- "What football matches were on last week?"
- "Show me rugby matches from last month"
- "What happened on May 10th on SuperSport channels?"
- "What were the results of football matches from yesterday?"

## Adding Documents

Place your API documentation files in the `documents` directory:

- PDF files (*.pdf)
- Text files (*.txt, *.md)
- VTT subtitle files (*.vtt)

The agent will automatically process these files and make them available for querying.

## Architecture

```
super_sport_agent/
├── super_sport_agent.py      # Main agent implementation
├── super_sport_client.py     # SuperSport API client
├── super_sport_tools.py      # API tools for the agent
├── models.py                 # Data models
├── __init__.py               # Package initialization
├── documents/                # API documentation files
└── faiss_index/              # Vector store for RAG functionality
```

## Troubleshooting

- If document processing fails, ensure the document files are not corrupted.
- If API requests fail, check your internet connection and the API status.
- If the agent is not responding, check that the GOOGLE_API_KEY environment variable is set correctly.
- If you're not getting good answers, try adding more detailed API documentation to the documents directory. 