"""
SuperSport Agent Package

This package provides an intelligent agent for processing and answering questions
about SuperSport API documentation by identifying the API endpoints to call.
"""

# Import version info
__version__ = "1.0.0"

# Import agents
from .agents import (
    super_sport_agent,
    api_agent,
    document_agent,
    formatter_agent,
    agent
)

# Import tools
from .tools import (
    # API tools
    super_sport_get_tv_guide,
    super_sport_get_live_sports,
    super_sport_get_upcoming_sports,
    super_sport_get_sport_categories,
    super_sport_get_channels,
    super_sport_search_programs,
    super_sport_query,
    
    # Knowledge Graph tools
    list_files_in_documents_dir,
    process_all_files,
    process_file,
    process_and_create_knowledge_graph,
    create_knowledge_graph_from_text,
    ask_question
)

# Import services
from .services import (
    SuperSportApiService,
    DocumentService,
    KnowledgeGraphService
)

# Import models
from .models import (
    # API models
    ApiRequest,
    TVGuideRequest, 
    QueryRequest,
    ApiResponse,
    Channel,
    SportEvent,
    TVGuideResponse,
    LiveSportsResponse,
    UpcomingSportsResponse,
    SportCategoryResponse,
    ChannelsResponse,
    QueryResponseItem,
    QueryResponse,
    
    # Agent models
    DocumentProcessingResult,
    KnowledgeGraphResult,
    QuestionAnswerResult
)

# Import utilities
from .utils import get_logger

# Exportable package elements
__all__ = [
    # Agents
    "super_sport_agent",
    "api_agent",
    "document_agent",
    "formatter_agent",
    "agent",
    
    # API tools
    "super_sport_get_tv_guide",
    "super_sport_get_live_sports",
    "super_sport_get_upcoming_sports",
    "super_sport_get_sport_categories",
    "super_sport_get_channels",
    "super_sport_search_programs",
    "super_sport_query",
    
    # Knowledge Graph tools
    "list_files_in_documents_dir",
    "process_all_files",
    "process_file",
    "process_and_create_knowledge_graph",
    "create_knowledge_graph_from_text",
    "ask_question",
    
    # Services
    "SuperSportApiService",
    "DocumentService",
    "KnowledgeGraphService",
    
    # API models
    "ApiRequest",
    "TVGuideRequest",
    "QueryRequest",
    "ApiResponse",
    "Channel",
    "SportEvent",
    "TVGuideResponse",
    "LiveSportsResponse",
    "UpcomingSportsResponse",
    "SportCategoryResponse",
    "ChannelsResponse",
    "QueryResponseItem",
    "QueryResponse",
    
    # Agent models
    "DocumentProcessingResult",
    "KnowledgeGraphResult",
    "QuestionAnswerResult",
    
    # Utilities
    "get_logger"
] 