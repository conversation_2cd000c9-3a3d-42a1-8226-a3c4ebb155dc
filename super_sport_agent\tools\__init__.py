"""
Tools for SuperSport Agent
"""

from .api_tools import (
    super_sport_get_tv_guide,
    super_sport_get_live_sports,
    super_sport_get_upcoming_sports,
    super_sport_get_sport_categories,
    super_sport_get_channels,
    super_sport_search_programs,
    super_sport_query,
    API_TOOLS
)

from .document_tools import (
    list_files_in_documents_dir,
    process_all_files,
    process_file,
    process_and_create_vector_store,
    DOCUMENT_TOOLS
)

from .search_tools import (
    create_vector_store_from_text,
    search_similar_content,
    ask_question,
    execute_local_api_call,
    SEARCH_TOOLS
)

# Collect all tools for easy access
ALL_TOOLS = API_TOOLS + DOCUMENT_TOOLS + SEARCH_TOOLS

__all__ = [
    # API tools
    "super_sport_get_tv_guide",
    "super_sport_get_live_sports",
    "super_sport_get_upcoming_sports",
    "super_sport_get_sport_categories",
    "super_sport_get_channels",
    "super_sport_search_programs",
    "super_sport_query",
    "API_TOOLS",
    
    # Document tools
    "list_files_in_documents_dir",
    "process_all_files",
    "process_file",
    "process_and_create_vector_store",
    "DOCUMENT_TOOLS",
    
    # Search tools
    "create_vector_store_from_text",
    "search_similar_content",
    "ask_question",
    "execute_local_api_call",
    "SEARCH_TOOLS",
    
    # All tools
    "ALL_TOOLS"
] 