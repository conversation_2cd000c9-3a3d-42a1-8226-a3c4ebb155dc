# Sample User Queries for EPG System

Below are examples of natural language queries that users might ask about TV channels, programs, and related information, along with how these queries map to the EPG API.

## Channel Information Queries

### "What channels show sports programs?"
This query is asking for channels that belong to the sports category. To answer this:
1. Use the Channels API to get all channels
2. Filter the results for channels where the `categories` array includes "sports"

### "Are there any Chinese TV channels available?"
This query is asking for channels from a specific country. To answer this:
1. Use the Channels API to get all channels
2. Filter the results for channels where the `country` field equals "CN"

### "What's the official website for BBC One?"
This query is asking for specific information about a channel. To answer this:
1. Use the Channels API to get all channels
2. Find the channel with a name matching "BBC One" or an ID like "BBCOne.uk"
3. Return the value of the `website` field

### "When was Sky News launched?"
This query asks for the launch date of a specific channel. To answer this:
1. Use the Channels API to get all channels
2. Find the channel with a name matching "Sky News"
3. Return the value of the `launched` field

## Stream Availability Queries

### "How can I watch CNN?"
This query is asking for stream links for a specific channel. To answer this:
1. Use the Channels API to identify the channel ID for CNN
2. Use the Streams API to find all streams with that channel ID
3. Return the stream URLs along with any required referrer or user agent information

### "What's the highest quality stream available for Discovery Channel?"
This query asks for the best quality stream for a specific channel. To answer this:
1. Use the Channels API to identify the channel ID for Discovery Channel
2. Use the Streams API to find all streams with that channel ID
3. Compare the `quality` fields and return the stream with the highest quality

### "Are there any Spanish language channels I can watch?"
This query is asking for channels in a specific language. To answer this:
1. Use the Languages API to find the code for Spanish ("spa")
2. Use the Feeds API to find feeds where the `languages` array includes "spa"
3. Use the Channels API to get the names of those channels

## Regional Broadcasting Queries

### "What local channels are available in Ontario, Canada?"
This query asks for channels specific to a region. To answer this:
1. Use the Subdivisions API to find the code for Ontario, Canada ("CA-ON")
2. Use the Feeds API to find feeds where the `broadcast_area` includes "s/CA-ON"
3. Use the Channels API to get the names of those channels

### "What channels are available in my timezone (Europe/Berlin)?"
This query asks for channels available in a specific timezone. To answer this:
1. Use the Timezones API to verify the timezone information
2. Use the Feeds API to find feeds where the `timezones` array includes "Europe/Berlin"
3. Use the Channels API to get the names of those channels

## Program Guide Queries

### "Where can I find the TV schedule for National Geographic?"
This query asks for program guide information for a specific channel. To answer this:
1. Use the Channels API to identify the channel ID for National Geographic
2. Use the Guides API to find entries where the `channel` field matches that ID
3. Return the site information that provides program data for that channel

### "What's playing on HBO right now?"
This is a more complex query that requires:
1. Using the Channels API to identify the channel ID for HBO
2. Using the Guides API to find where to retrieve program data
3. Making an additional call to the program data provider (outside the scope of this API)
4. Using the user's current time to determine what's playing

## Content Category Queries

### "What documentary channels are available?"
This query asks for channels of a specific category. To answer this:
1. Use the Categories API to confirm the ID for documentary category
2. Use the Channels API to find channels where `categories` includes "documentary"

### "Are there any kids channels that broadcast in French?"
This is a multi-condition query that requires:
1. Use the Categories API to find the ID for kids/children's category
2. Use the Languages API to find the code for French ("fra")
3. Use the Channels API to find channels where `categories` includes "kids"
4. Use the Feeds API to filter for feeds that have those channel IDs and include "fra" in their `languages` array

## Technical Queries

### "Why can't I access Animal Planet anymore?"
This query might relate to blocked channels. To answer this:
1. Use the Blocklist API to check if "AnimalPlanet" or similar IDs appear
2. Return the reason for blocking and any reference information

### "What HD channels are available?"
This query is asking for high-definition channels. To answer this:
1. Use the Feeds API to find feeds where the `format` field includes "1080" or "720p"
2. Use the Channels API to get the names of those channels

## Complex Multi-Step Queries

### "What English language news channels are available in 1080p quality?"
This complex query requires multiple API calls and filters:
1. Use the Categories API to confirm the ID for news category
2. Use the Languages API to find the code for English ("eng")
3. Use the Channels API to find channels where `categories` includes "news"
4. Use the Feeds API to filter for feeds of those channels that have "eng" in their `languages` array and a `format` of "1080i" or "1080p"
5. Use the Streams API to find high-quality streams for these channels

### "I'm in Canada and interested in sports channels with French commentary. What are my options?"
This multi-faceted query requires:
1. Use the Countries API to confirm the code for Canada ("CA")
2. Use the Categories API to confirm the ID for sports category
3. Use the Languages API to find the code for French ("fra")
4. Use the Feeds API to find feeds where `broadcast_area` includes "c/CA" and `languages` includes "fra"
5. Use the Channels API to filter for channels with those feed IDs that include "sports" in their `categories` array 