#!/usr/bin/env python3

import os
import sys
import json
from .iptv_agent import iptv_agent, api_handler, list_files_in_documents_dir, process_all_files, search_similar_content
from .logger import get_logger

# Get logger for this module
logger = get_logger('iptv_agent_demo')

def print_header(text):
    """Print a formatted header"""
    border = "=" * (len(text) + 4)
    print(f"\n{border}")
    print(f"| {text} |")
    print(f"{border}\n")
    logger.info(f"Demo section: {text}")

def process_docs_demo():
    """Process API documentation"""
    print_header("IPTV Agent - Document Processing")
    
    # List available documents
    print("Checking available documents...")
    files = list_files_in_documents_dir()
    
    pdf_files = files.get("pdf_files", [])
    vtt_files = files.get("vtt_files", [])
    text_files = files.get("text_files", [])
    
    print(f"Found {len(pdf_files)} PDF files, {len(vtt_files)} VTT files, and {len(text_files)} text files")
    
    if pdf_files or vtt_files or text_files:
        print("\nProcessing all documents...")
        result = process_all_files()
        print(f"Result: {result.get('status')} - {result.get('message')}")
    else:
        print("\nNo documents found. Please add API documentation to the documents directory.")
        # Create a sample documentation file if none exists
        docs_dir = files.get("documents_dir")
        if docs_dir:
            sample_file_path = os.path.join(docs_dir, "sample_api_docs.md")
            if not os.path.exists(sample_file_path):
                print("\nCreating a sample API documentation file...")
                sample_content = """# IPTV API Documentation

## Base URL
```
https://iptv-org.github.io/api
```

## Available Endpoints

### Channels API
**Endpoint**: `GET /channels.json`

Returns a list of all TV channels with detailed information.

**Example Request**:
```
GET https://iptv-org.github.io/api/channels.json
```

**Parameters**:
- country: Filter by country code (optional)
- category: Filter by category (optional)

### Streams API
**Endpoint**: `GET /streams.json`

Returns a list of available stream URLs for channels.

**Example Request**:
```
GET https://iptv-org.github.io/api/streams.json
```

**Parameters**:
- channel: Filter by channel ID (optional)
- quality: Filter by stream quality (optional)

### Categories API
**Endpoint**: `GET /categories.json`

Returns a list of all channel categories.

**Example Request**:
```
GET https://iptv-org.github.io/api/categories.json
```

### Countries API
**Endpoint**: `GET /countries.json`

Returns a list of all countries with channels.

**Example Request**:
```
GET https://iptv-org.github.io/api/countries.json
```
"""
                try:
                    with open(sample_file_path, 'w') as f:
                        f.write(sample_content)
                    print(f"Created sample file at {sample_file_path}")
                    # Process the new file
                    print("\nProcessing the sample documentation...")
                    result = process_all_files()
                    print(f"Result: {result.get('status')} - {result.get('message')}")
                except Exception as e:
                    print(f"Error creating sample file: {str(e)}")

def api_handler_demo():
    """Demo the API handler subagent capabilities"""
    print_header("IPTV Agent - API Handler Demo")
    
    queries = [
        "How do I get a list of channels?",
        "I want to see available streams",
        "What categories are available in the system?",
        "Can you tell me about countries that have channels?"
    ]
    
    for query in queries:
        print(f"\nUser Query: \"{query}\"")
        
        # First search for relevant documentation
        search_result = search_similar_content(query, num_results=1)
        if search_result.get("status") != "success":
            print("Error: Could not find relevant documentation.")
            continue
            
        documentation = search_result.get("context", ["No documentation found"])[0]
        print(f"\nFound relevant documentation:\n{'-' * 40}\n{documentation[:200]}...\n{'-' * 40}")
        
        # Now let the API handler analyze and execute the API call
        try:
            # Prepare input for the subagent
            subagent_input = {
                "question": query,
                "documentation": documentation
            }
            
            # Call the API handler subagent
            print("\nAPI Handler Analysis and Execution:")
            response = api_handler.invoke(subagent_input)
            
            if response:
                print(f"\n{response}")
            else:
                print("No response from API handler.")
        except Exception as e:
            print(f"Error in API handler: {str(e)}")

def root_agent_demo():
    """Demo the root agent capabilities"""
    print_header("IPTV Agent - Root Agent Demo")
    
    queries = [
        "What API should I use to get a list of all channels?",
        "Which endpoint provides stream URLs?",
        "How can I get information about categories?",
        "What's the API for getting country information?"
    ]
    
    for query in queries:
        print(f"\nUser query: \"{query}\"")
        print("\nRoot agent response:")
        try:
            # Use the root agent with subagent delegation
            response = iptv_agent.invoke({"question": query})
            if response:
                print(response)
            else:
                print("No response received")
        except Exception as e:
            print(f"Error invoking agent: {str(e)}")

def interactive_demo():
    """Interactive demo mode"""
    print_header("IPTV Agent - Interactive Demo")
    
    print("Type your questions about the IPTV API below. Type 'exit' to quit.")
    print("The agent will process API documentation to find the exact API to use.")
    
    while True:
        try:
            query = input("\nYour question: ")
            
            if query.lower() in ['exit', 'quit', 'q']:
                print("\nExiting interactive demo. Goodbye!")
                logger.info("Exiting interactive demo")
                break
                
            logger.info(f"User query: {query}")
            print("\nRoot agent response:")
            response = iptv_agent.invoke({"question": query})
            if response:
                print(response)
                logger.info("Response provided to user")
            else:
                print("No response received")
                logger.warning("No response received from agent")
                
        except KeyboardInterrupt:
            print("\n\nExiting interactive demo. Goodbye!")
            logger.info("Interactive demo interrupted by user")
            break
        except Exception as e:
            print(f"Error: {str(e)}")
            logger.error(f"Error in interactive demo: {str(e)}", exc_info=True)

def main():
    """Main entry point"""
    print("\nIPTV Agent Demo\n")
    print("This demo showcases the capabilities of the IPTV Agent with documentation-based API identification")
    logger.info("IPTV Agent Demo started")
    
    # Process documents first
    process_docs_demo()
    
    # Demo menu
    while True:
        print("\n" + "=" * 40)
        print("IPTV Agent Demo Menu")
        print("=" * 40)
        print("1. API Handler Demo")
        print("2. Root Agent Demo")
        print("3. Interactive Agent Demo")
        print("4. Process Documents")
        print("0. Exit")
        
        choice = input("\nEnter your choice (0-4): ")
        logger.info(f"User selected menu option: {choice}")
        
        if choice == "1":
            api_handler_demo()
        elif choice == "2":
            root_agent_demo()
        elif choice == "3":
            interactive_demo()
        elif choice == "4":
            process_docs_demo()
        elif choice == "0":
            print("\nExiting IPTV Agent Demo. Goodbye!")
            logger.info("IPTV Agent Demo exited")
            break
        else:
            print("\nInvalid choice. Please try again.")
            logger.warning(f"Invalid menu choice: {choice}")

if __name__ == "__main__":
    main() 