"""
Document processing tools for SuperSport Agent
"""

from typing import Dict, Any, Optional, List, Callable, Union
from pathlib import Path

from ..utils.logger import get_logger
from ..services.document_service import DocumentService
from ..services.vector_service import VectorService

# Get logger for this module
logger = get_logger(__name__)

# Singleton services
_document_service = None
_vector_service = None

def get_document_service() -> DocumentService:
    """Get or create the singleton document service"""
    global _document_service
    if _document_service is None:
        _document_service = DocumentService()
    return _document_service

def get_vector_service() -> VectorService:
    """Get or create the singleton vector service"""
    global _vector_service
    if _vector_service is None:
        _vector_service = VectorService()
    return _vector_service

def list_files_in_documents_dir() -> Dict[str, Any]:
    """
    List all available files in the project's documents directory.
    Returns a dictionary with lists of PDF, VTT, and text files.
    """
    try:
        document_service = get_document_service()
        return document_service.list_files()
    except Exception as e:
        logger.error(f"Error in list_files_in_documents_dir: {str(e)}")
        return {
            "status": "error",
            "message": f"Error listing files: {str(e)}"
        }

def process_file(file_path: str) -> Dict[str, Any]:
    """
    Process a file (PDF, VTT, text) from a given file path.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Dictionary with status and extracted text
    """
    try:
        document_service = get_document_service()
        result = document_service.process_file(file_path)
        
        # Convert to dict for returning to the agent
        return result.dict()
    except Exception as e:
        logger.error(f"Error in process_file: {str(e)}")
        return {
            "status": "error",
            "message": f"Error processing file: {str(e)}",
            "file_count": 0
        }

def process_all_files() -> Dict[str, Any]:
    """
    Process all PDF, VTT, and text files in the documents directory automatically.
    Returns a summary of the processing results.
    """
    try:
        logger.info("Starting process_all_files in document_tools...")
        document_service = get_document_service()
        logger.info("Calling document_service.process_all_files()...")
        result = document_service.process_all_files()
        
        logger.info(f"Document processing result status: {result.status}, processed files: {len(result.processed_files) if hasattr(result, 'processed_files') else 0}")
        if hasattr(result, 'content'):
            logger.info(f"Content length: {len(result.content) if result.content else 0} characters")
        else:
            logger.warning("No content attribute in result")
        
        # If processing is successful and we have content, create vector store
        if result.status == "success" and hasattr(result, 'content') and result.content:
            logger.info("Document processing successful with content. Creating vector store...")
            vector_service = get_vector_service()
            vector_result = vector_service.create_from_text(result.content)
            
            logger.info(f"Vector store creation result: {vector_result.status}")
            
            # Add vector store creation result to the response
            result_dict = result.dict()
            result_dict["vector_store"] = {
                "status": vector_result.status,
                "message": vector_result.message if hasattr(vector_result, 'message') else "Vector store created"
            }
            logger.info("Returning combined document and vector store result")
            return result_dict
        
        # If no content or error, just return the document processing result
        if result.status != "success":
            logger.warning(f"Document processing was not successful: {result.message}")
        elif not hasattr(result, 'content') or not result.content:
            logger.warning("No content available to create vector store")
        
        logger.info("Returning document processing result only")
        return result.dict()
    except Exception as e:
        import traceback
        stack_trace = traceback.format_exc()
        logger.error(f"Error in process_all_files: {str(e)}\n{stack_trace}")
        return {
            "status": "error",
            "message": f"Error processing files: {str(e)}",
            "file_count": 0
        }

def process_and_create_vector_store(file_path: str) -> Dict[str, Any]:
    """
    Process a file and create a vector store from its content in one step.
    
    Args:
        file_path: Path to the file to process
        
    Returns:
        Dictionary with processing and vector store creation status
    """
    try:
        # First process the file
        document_service = get_document_service()
        process_result = document_service.process_file(file_path)
        
        # Check if processing was successful
        if process_result.status != "success" or not hasattr(process_result, 'content') or not process_result.content:
            return process_result.dict()
        
        # Create vector store from the processed content
        vector_service = get_vector_service()
        vector_result = vector_service.create_from_text(process_result.content)
        
        # Combine results
        result = process_result.dict()
        result["vector_store"] = {
            "status": vector_result.status,
            "message": vector_result.message if hasattr(vector_result, 'message') else "Vector store created"
        }
        
        return result
    except Exception as e:
        logger.error(f"Error in process_and_create_vector_store: {str(e)}")
        return {
            "status": "error",
            "message": f"Error processing file and creating vector store: {str(e)}",
            "file_count": 0
        }

# List of document tools for easy reference
DOCUMENT_TOOLS: List[Callable] = [
    list_files_in_documents_dir,
    process_file,
    process_all_files,
    process_and_create_vector_store
] 