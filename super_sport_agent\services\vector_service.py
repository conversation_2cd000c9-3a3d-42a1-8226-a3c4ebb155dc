"""
Vector store service for embeddings and semantic search
"""

import os
from pathlib import Path
from typing import List, Dict, Optional, Union, Any
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
from langchain_community.vectorstores import FAISS
import google.generativeai as genai

from ..utils.logger import get_logger
from ..config.settings import (
    VECTOR_STORE_PATH, 
    CHUNK_SIZE, 
    CHUNK_OVERLAP, 
    EMBEDDING_MODEL,
    GOOGLE_API_KEY
)
from ..models.agent_models import VectorSearchResult

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY is not set, vector operations will fail")

class VectorService:
    """Service for vector store operations"""
    
    def __init__(self, vector_store_path: Optional[Union[str, Path]] = None):
        """
        Initialize the vector service
        
        Args:
            vector_store_path: Path to the vector store. Defaults to the configured value.
        """
        self.vector_store_path = Path(vector_store_path) if vector_store_path else Path(VECTOR_STORE_PATH)
        self._vector_store = None
        logger.info(f"Initialized vector service with path: {self.vector_store_path}")
    
    def get_text_chunks(self, text: str) -> List[str]:
        """
        Split text into chunks for processing
        
        Args:
            text: Text to split
            
        Returns:
            List of text chunks
        """
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=CHUNK_SIZE, 
            chunk_overlap=CHUNK_OVERLAP
        )
        return text_splitter.split_text(text)
    
    def create_vector_store(self, text_chunks: List[str]) -> str:
        """
        Create a vector store from text chunks
        
        Args:
            text_chunks: List of text chunks to embed
            
        Returns:
            Status message
        """
        try:
            if not GOOGLE_API_KEY:
                raise ValueError("GOOGLE_API_KEY is not set")
                
            embeddings = GoogleGenerativeAIEmbeddings(model=EMBEDDING_MODEL)
            self._vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
            self._vector_store.save_local(str(self.vector_store_path))
            
            return "Vector store created successfully"
        except Exception as e:
            error_msg = f"Error creating vector store: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    def create_from_text(self, text: str) -> VectorSearchResult:
        """
        Process text and create vector store in one step
        
        Args:
            text: Text content to process
            
        Returns:
            VectorSearchResult with status
        """
        try:
            # Split text into chunks
            chunks = self.get_text_chunks(text)
            
            if not chunks:
                return VectorSearchResult(
                    status="error",
                    message="No text chunks created",
                    query="",
                    total_results=0
                )
            
            # Create vector store
            result = self.create_vector_store(chunks)
            
            if result.startswith("Error"):
                return VectorSearchResult(
                    status="error",
                    message=result,
                    query="",
                    total_results=0
                )
            
            return VectorSearchResult(
                status="success",
                message=f"Created vector store with {len(chunks)} chunks",
                query="",
                total_results=len(chunks)
            )
            
        except Exception as e:
            logger.error(f"Error creating vector store from text: {str(e)}")
            return VectorSearchResult(
                status="error",
                message=f"Error creating vector store from text: {str(e)}",
                query="",
                total_results=0
            )
    
    def get_vector_store(self) -> Optional[FAISS]:
        """
        Get the current vector store, loading it if necessary
        
        Returns:
            FAISS vector store or None if not available
        """
        try:
            # If we already have a loaded vector store
            if self._vector_store:
                return self._vector_store
            
            # Check if vector store exists
            if not os.path.exists(self.vector_store_path):
                logger.warning(f"Vector store not found at {self.vector_store_path}")
                return None
            
            # Load existing vector store
            embeddings = GoogleGenerativeAIEmbeddings(model=EMBEDDING_MODEL)
            self._vector_store = FAISS.load_local(str(self.vector_store_path), embeddings)
            
            return self._vector_store
            
        except Exception as e:
            logger.error(f"Error loading vector store: {str(e)}")
            return None
    
    def search_similar_content(self, query: str, num_results: int = 3) -> VectorSearchResult:
        """
        Search for content similar to the query
        
        Args:
            query: Search query
            num_results: Number of results to return
            
        Returns:
            VectorSearchResult with search results
        """
        try:
            # Get vector store
            vector_store = self.get_vector_store()
            
            if not vector_store:
                return VectorSearchResult(
                    status="error",
                    message="Vector store not available. Process documents first.",
                    query=query,
                    total_results=0
                )
            
            # Perform similarity search
            search_results = vector_store.similarity_search_with_score(query, k=num_results)
            
            # Extract results
            contexts = []
            scores = []
            
            for doc, score in search_results:
                contexts.append(doc.page_content)
                scores.append(score)
            
            return VectorSearchResult(
                status="success",
                query=query,
                context=contexts,
                score=scores,
                total_results=len(contexts)
            )
            
        except Exception as e:
            logger.error(f"Error searching vector store: {str(e)}")
            return VectorSearchResult(
                status="error",
                message=f"Error searching vector store: {str(e)}",
                query=query,
                total_results=0
            ) 