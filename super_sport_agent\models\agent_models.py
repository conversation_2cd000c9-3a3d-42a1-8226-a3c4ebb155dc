"""
Agent-specific models for SuperSport Agent
"""

from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field

class DocumentProcessingResult(BaseModel):
    """Result of document processing operations"""
    status: str = Field(..., description="Status of the operation (success/error/warning)")
    message: str = Field(..., description="Message describing the result")
    processed_files: Optional[List[str]] = Field(default_factory=list, description="List of processed files")
    file_count: Optional[int] = Field(0, description="Total number of files processed")
    content: Optional[str] = Field(None, description="Extracted content from processed files")
    
class VectorSearchResult(BaseModel):
    """Result of vector store search operations"""
    status: str = Field(..., description="Status of the search (success/error)")
    message: Optional[str] = Field(None, description="Message describing the result")
    query: str = Field(..., description="The search query")
    context: Optional[List[str]] = Field(default_factory=list, description="Retrieved context from vector store")
    score: Optional[List[float]] = Field(default_factory=list, description="Similarity scores")
    total_results: Optional[int] = Field(0, description="Total number of results found")

class QuestionAnswerResult(BaseModel):
    """Result of question answering operations"""
    status: str = Field(..., description="Status of the operation (success/error)")
    question: str = Field(..., description="Original question")
    answer: str = Field(..., description="Answer to the question")
    api_endpoints: Optional[List[Dict[str, str]]] = Field(default_factory=list, description="Identified API endpoints")
    context: Optional[List[str]] = Field(default_factory=list, description="Context used to generate the answer")
    confidence: Optional[float] = Field(None, description="Confidence score of the answer") 