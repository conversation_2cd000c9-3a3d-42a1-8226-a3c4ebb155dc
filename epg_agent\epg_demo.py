#!/usr/bin/env python3
"""
EPG API Tools Demo - Shows how to use the EPG API tools with models
"""

import os
import logging
import argparse
from pprint import pprint
import json
from typing import Dict, Any

# Import MCP tools and models
from .models import ChannelRequest, QueryRequest
from .epg_client import EpgApiClient
from .mcp_tools import (
    mcp_epg_get_channels, 
    mcp_epg_get_categories,
    mcp_epg_query
)
from .agent import root_agent, process_file

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger('epg_demo')

# Formatter for pretty output
def format_response(response: Dict[str, Any]) -> str:
    """Format a response for display"""
    try:
        # Format with indentation for readability
        return json.dumps(response, indent=2)
    except Exception:
        # Fall back to simple printing
        return str(response)

def initialize_agent():
    """Initialize the agent with API documentation"""
    logger.info("Processing API documentation for the agent...")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    api_doc_path = os.path.join(current_dir, "data", "api_documentation.md")
    
    if not os.path.exists(api_doc_path):
        logger.error(f"API documentation not found at {api_doc_path}")
        logger.info("Agent will still work, but without RAG capabilities.")
        return False
    
    result = process_file(api_doc_path)
    if result.get("status") != "success":
        logger.error(f"Failed to process API documentation: {result.get('message')}")
        return False
    
    logger.info("API documentation successfully processed.")
    return True

def demo_direct_api_client():
    """Demonstrate the direct API client usage"""
    logger.info("\n" + "="*50)
    logger.info("DEMO: Direct EPG API client usage")
    logger.info("="*50)
    
    # Create an API client
    client = EpgApiClient()
    logger.info(f"Created client for API: {client.base_url}")
    
    # Get channels
    logger.info("\nFetching channels...")
    channels_response = client.get_channels()
    logger.info(f"Status: {channels_response.status}")
    logger.info(f"Total channels: {channels_response.total_items}")
    if channels_response.data:
        logger.info("First channel:")
        logger.info(f"  - ID: {channels_response.data[0].id}")
        logger.info(f"  - Name: {channels_response.data[0].name}")
        logger.info(f"  - Categories: {channels_response.data[0].categories}")

    # Get categories
    logger.info("\nFetching categories...")
    categories_response = client.get_categories()
    logger.info(f"Status: {categories_response.status}")
    logger.info(f"Categories: {[cat.name for cat in categories_response.data[:5]]}")

    # Try a query
    logger.info("\nExecuting a query...")
    query_request = QueryRequest(query="What news channels are available?", max_results=5)
    query_response = client.query(query_request)
    logger.info(f"Status: {query_response.status}")
    logger.info(f"Endpoints queried: {query_response.endpoints_queried}")
    logger.info(f"Number of data items: {len(query_response.data)}")
    if query_response.data:
        logger.info(f"First data source: {query_response.data[0].source}")
        logger.info(f"Data count: {len(query_response.data[0].data)}")

def demo_mcp_tools():
    """Demonstrate the MCP tools usage"""
    logger.info("\n" + "="*50)
    logger.info("DEMO: MCP Tools Usage")
    logger.info("="*50)
    
    # Get channels with MCP tool
    logger.info("\nUsing mcp_epg_get_channels tool:")
    channels_result = mcp_epg_get_channels(category="news")
    logger.info(f"Status: {channels_result['status']}")
    logger.info(f"Total channels: {channels_result['total_items']}")
    logger.info(f"First news channel: {channels_result['data'][0]['name'] if channels_result['data'] else 'None'}")
    
    # Get categories with MCP tool
    logger.info("\nUsing mcp_epg_get_categories tool:")
    categories_result = mcp_epg_get_categories()
    logger.info(f"Status: {categories_result['status']}")
    logger.info(f"Number of categories: {categories_result['total_items']}")
    logger.info(f"Categories: {[cat['name'] for cat in categories_result['data'][:5] if cat.get('name')]}")
    
    # Query with MCP tool
    logger.info("\nUsing mcp_epg_query tool:")
    query_result = mcp_epg_query("Find sports channels")
    logger.info(f"Status: {query_result['status']}")
    logger.info(f"Endpoints queried: {query_result['endpoints_queried']}")
    if query_result['data']:
        logger.info(f"Sources: {[item['source'] for item in query_result['data']]}")

def demo_agent():
    """Demonstrate using the agent with the EPG API tools"""
    logger.info("\n" + "="*50)
    logger.info("DEMO: Agent with EPG API Tools")
    logger.info("="*50)
    
    demo_queries = [
        "What API endpoint should I use to get a list of all channels?",
        "Show me some BBC channels",
        "What sports categories are available?",
        "Find news channels in the US", 
        "How can I get stream URLs?"
    ]
    
    for query in demo_queries:
        logger.info(f"\nQuery: {query}")
        try:
            response = root_agent.run(query)
            logger.info(f"Response: {response}")
        except Exception as e:
            logger.error(f"Error running agent: {str(e)}")

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="EPG API Tools Demo")
    parser.add_argument("--all", action="store_true", help="Run all demos")
    parser.add_argument("--client", action="store_true", help="Run direct API client demo")
    parser.add_argument("--tools", action="store_true", help="Run MCP tools demo")
    parser.add_argument("--agent", action="store_true", help="Run agent demo")
    args = parser.parse_args()
    
    # If no specific demo is requested, run all
    run_all = args.all or not (args.client or args.tools or args.agent)
    
    # Initialize the agent
    agent_initialized = initialize_agent()
    if not agent_initialized and (run_all or args.agent):
        logger.warning("Agent not fully initialized. Demo may not work as expected.")
    
    # Run the demos
    if run_all or args.client:
        demo_direct_api_client()
    
    if run_all or args.tools:
        demo_mcp_tools()
    
    if run_all or args.agent:
        demo_agent()
    
    logger.info("\nDemo completed.")

if __name__ == "__main__":
    main() 