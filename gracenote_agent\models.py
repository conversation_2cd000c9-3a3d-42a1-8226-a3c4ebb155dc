from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import date, datetime
from enum import Enum

# Models for IPTV API Requests
class ApiRequest(BaseModel):
    """Base model for API requests"""
    pass

class ChannelRequest(ApiRequest):
    """Request parameters for channel API"""
    country: Optional[str] = Field(None, description="Filter by country code")
    category: Optional[str] = Field(None, description="Filter by category")
    language: Optional[str] = Field(None, description="Filter by language code")

class StreamRequest(ApiRequest):
    """Request parameters for stream API"""
    channel: Optional[str] = Field(None, description="Filter by channel ID")
    quality: Optional[str] = Field(None, description="Filter by stream quality")

class QueryRequest(ApiRequest):
    """Natural language query request"""
    query: str = Field(..., description="The natural language query to process")
    max_results: Optional[int] = Field(10, description="Maximum number of results to return")

# Models for IPTV API Responses
class ApiResponse(BaseModel):
    """Base model for API responses"""
    status: str = Field(..., description="Status of the API response (success/error)")
    message: Optional[str] = Field(None, description="Response message, particularly for errors")

class Channel(BaseModel):
    """Channel model based on the IPTV API schema"""
    id: str
    name: str
    alt_names: Optional[List[str]] = []
    network: Optional[str] = None
    country: Optional[str] = None
    categories: Optional[List[str]] = []
    is_nsfw: Optional[bool] = False
    logo: Optional[str] = None

class Stream(BaseModel):
    """Stream model based on the IPTV API schema"""
    channel: Optional[str] = None
    url: str
    quality: Optional[str] = None
    is_nsfw: Optional[bool] = False

class Category(BaseModel):
    """Category model based on the IPTV API schema"""
    id: str
    name: str

class Country(BaseModel):
    """Country model based on the IPTV API schema"""
    name: str
    code: str
    flag: str

class Guide(BaseModel):
    """Program guide model based on the IPTV API schema"""
    id: Optional[str] = None
    channel: Optional[str] = None
    title: Optional[str] = None
    description: Optional[str] = None
    start: Optional[datetime] = None
    stop: Optional[datetime] = None
    category: Optional[str] = None
    season: Optional[int] = None
    episode: Optional[int] = None
    language: Optional[str] = None
    country: Optional[str] = None

# Response models for specific endpoints
class ChannelsResponse(ApiResponse):
    """Response model for channels endpoint"""
    data: Optional[List[Channel]] = []
    total_items: Optional[int] = 0

class StreamsResponse(ApiResponse):
    """Response model for streams endpoint"""
    data: Optional[List[Stream]] = []
    total_items: Optional[int] = 0

class CategoriesResponse(ApiResponse):
    """Response model for categories endpoint"""
    data: Optional[List[Category]] = []
    total_items: Optional[int] = 0

class CountriesResponse(ApiResponse):
    """Response model for countries endpoint"""
    data: Optional[List[Country]] = []
    total_items: Optional[int] = 0

class GuidesResponse(ApiResponse):
    """Response model for program guides endpoint"""
    data: Optional[List[Guide]] = []
    total_items: Optional[int] = 0

class QueryResponseItem(BaseModel):
    """Item in query response"""
    source: str = Field(..., description="Source endpoint of the data")
    data: Any = Field(..., description="The actual data")

class QueryResponse(ApiResponse):
    """Response model for natural language queries"""
    query: str = Field(..., description="The original query")
    data: List[QueryResponseItem] = []
    endpoints_queried: List[str] = []
    context: Optional[List[str]] = [] 