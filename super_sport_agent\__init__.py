"""
SuperSport Agent Package

This package provides an intelligent agent for processing and answering questions
about SuperSport API documentation by identifying the API endpoints to call.
"""

# Import version info
__version__ = "1.0.0"

# Import agents
from .agents import (
    agent
)

# Import services
from .services import (
    VectorService
)

# Import models
from .models import (
    # Agent models
    DocumentProcessingResult,
    VectorSearchResult,
    QuestionAnswerResult
)

# Import utilities
from .utils import get_logger

# Exportable package elements
__all__ = [
    # Agents
    "agent",
    
    # Services
    "VectorService",
    
    # Agent models
    "DocumentProcessingResult",
    "VectorSearchResult",
    "QuestionAnswerResult",
    
    # Utilities
    "get_logger"
] 