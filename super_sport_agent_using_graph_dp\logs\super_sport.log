2025-05-20 18:27:30,128 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next football match?
2025-05-20 18:27:30,129 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:27:34,354 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Generated Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'
RETURN e.path
ORDER BY score(e.description, 'football match next') DESC
LIMIT 1
```
2025-05-20 18:27:34,356 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'
RETURN e.path
ORDER BY score(e.description, 'football match next') DESC
LIMIT 1
```
2025-05-20 18:27:34,358 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:27:35,725 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error executing Cypher query: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY score(e.description, 'football match next') DESC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}
neo4j.exceptions.GqlError: {gql_status: 42I06} {gql_status_description: error: syntax error or access rule violation - invalid input. Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY score(e.description, 'football match next') DESC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {message: 42I06: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY score(e.description, 'football match next') DESC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {diagnostic_record: {'_classification': 'CLIENT_ERROR', '_position': {'column': 1, 'offset': 0, 'line': 1}, 'OPERATION': '', 'OPERATION_CODE': '0', 'CURRENT_SCHEMA': '/'}} {raw_classification: CLIENT_ERROR}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 277, in execute_cypher_query
    results = graph.query(cypher_query)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\langchain_community\graphs\neo4j_graph.py", line 467, in query
    data, _, _ = self._driver.execute_query(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 970, in execute_query
    return session._run_transaction(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 583, in _run_transaction
    result = transaction_function(tx, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_work\query.py", line 144, in wrapped
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 1306, in _work
    res = tx.run(query, parameters)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\transaction.py", line 206, in run
    result._tx_ready_run(query, parameters)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 177, in _tx_ready_run
    self._run(query, parameters, None, None, None, None, None, None)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 864, in fetch_message
    res = self._process_message(tag, fields)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 1208, in _process_message
    response.on_failure(summary_metadata or {})
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 254, in on_failure
    raise self._hydrate_error(metadata)
neo4j.exceptions.CypherSyntaxError: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY score(e.description, 'football match next') DESC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}

2025-05-20 18:27:35,728 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error in ask_question: 'dict' object has no attribute 'dict'
Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 197, in ask_question
    return result.dict()
           ^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'dict'

2025-05-20 18:27:38,737 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next football match?
2025-05-20 18:27:38,738 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:27:42,979 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Generated Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')
RETURN e.path
ORDER BY
  CASE
    WHEN e.path CONTAINS 'fixtures' THEN 0
    WHEN e.description CONTAINS 'next' THEN 1
    WHEN e.description CONTAINS 'upcoming' THEN 2
    ELSE 3
  END
LIMIT 1
```
2025-05-20 18:27:42,980 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')
RETURN e.path
ORDER BY
  CASE
    WHEN e.path CONTAINS 'fixtures' THEN 0
    WHEN e.description CONTAINS 'next' THEN 1
    WHEN e.description CONTAINS 'upcoming' THEN 2
    ELSE 3
  END
LIMIT 1
```
2025-05-20 18:27:42,981 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:27:44,381 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error executing Cypher query: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')\nRETURN e.path\nORDER BY\n  CASE\n    WHEN e.path CONTAINS 'fixtures' THEN 0\n    WHEN e.description CONTAINS 'next' THEN 1\n    WHEN e.description CONTAINS 'upcoming' THEN 2\n    ELSE 3\n  END\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}
neo4j.exceptions.GqlError: {gql_status: 42I06} {gql_status_description: error: syntax error or access rule violation - invalid input. Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')\nRETURN e.path\nORDER BY\n  CASE\n    WHEN e.path CONTAINS 'fixtures' THEN 0\n    WHEN e.description CONTAINS 'next' THEN 1\n    WHEN e.description CONTAINS 'upcoming' THEN 2\n    ELSE 3\n  END\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {message: 42I06: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')\nRETURN e.path\nORDER BY\n  CASE\n    WHEN e.path CONTAINS 'fixtures' THEN 0\n    WHEN e.description CONTAINS 'next' THEN 1\n    WHEN e.description CONTAINS 'upcoming' THEN 2\n    ELSE 3\n  END\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {diagnostic_record: {'_classification': 'CLIENT_ERROR', '_position': {'column': 1, 'offset': 0, 'line': 1}, 'OPERATION': '', 'OPERATION_CODE': '0', 'CURRENT_SCHEMA': '/'}} {raw_classification: CLIENT_ERROR}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 277, in execute_cypher_query
    results = graph.query(cypher_query)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\langchain_community\graphs\neo4j_graph.py", line 467, in query
    data, _, _ = self._driver.execute_query(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 970, in execute_query
    return session._run_transaction(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 583, in _run_transaction
    result = transaction_function(tx, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_work\query.py", line 144, in wrapped
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 1306, in _work
    res = tx.run(query, parameters)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\transaction.py", line 206, in run
    result._tx_ready_run(query, parameters)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 177, in _tx_ready_run
    self._run(query, parameters, None, None, None, None, None, None)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 864, in fetch_message
    res = self._process_message(tag, fields)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 1208, in _process_message
    response.on_failure(summary_metadata or {})
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 254, in on_failure
    raise self._hydrate_error(metadata)
neo4j.exceptions.CypherSyntaxError: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND (e.description CONTAINS 'next' OR e.description CONTAINS 'upcoming' OR e.path CONTAINS 'fixtures')\nRETURN e.path\nORDER BY\n  CASE\n    WHEN e.path CONTAINS 'fixtures' THEN 0\n    WHEN e.description CONTAINS 'next' THEN 1\n    WHEN e.description CONTAINS 'upcoming' THEN 2\n    ELSE 3\n  END\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}

2025-05-20 18:27:44,384 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error in ask_question: 'dict' object has no attribute 'dict'
Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 197, in ask_question
    return result.dict()
           ^^^^^^^^^^^
AttributeError: 'dict' object has no attribute 'dict'

2025-05-20 18:30:33,484 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next football match?
2025-05-20 18:30:33,485 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:30:37,248 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Generated Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'
RETURN e.path
ORDER BY size(e.path) ASC
LIMIT 1
```
2025-05-20 18:30:37,250 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'
RETURN e.path
ORDER BY size(e.path) ASC
LIMIT 1
```
2025-05-20 18:30:37,251 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:30:38,662 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error executing Cypher query: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}
neo4j.exceptions.GqlError: {gql_status: 42I06} {gql_status_description: error: syntax error or access rule violation - invalid input. Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {message: 42I06: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {diagnostic_record: {'_classification': 'CLIENT_ERROR', '_position': {'column': 1, 'offset': 0, 'line': 1}, 'OPERATION': '', 'OPERATION_CODE': '0', 'CURRENT_SCHEMA': '/'}} {raw_classification: CLIENT_ERROR}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 279, in execute_cypher_query
    results = graph.query(cypher_query)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\langchain_community\graphs\neo4j_graph.py", line 467, in query
    data, _, _ = self._driver.execute_query(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 970, in execute_query
    return session._run_transaction(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 583, in _run_transaction
    result = transaction_function(tx, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_work\query.py", line 144, in wrapped
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 1306, in _work
    res = tx.run(query, parameters)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\transaction.py", line 206, in run
    result._tx_ready_run(query, parameters)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 177, in _tx_ready_run
    self._run(query, parameters, None, None, None, None, None, None)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 864, in fetch_message
    res = self._process_message(tag, fields)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 1208, in _process_message
    response.on_failure(summary_metadata or {})
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 254, in on_failure
    raise self._hydrate_error(metadata)
neo4j.exceptions.CypherSyntaxError: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'match' AND e.description CONTAINS 'next'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}

2025-05-20 18:30:40,936 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: Find the API endpoint for upcoming football matches.
2025-05-20 18:30:40,937 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:30:43,834 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Generated Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'
RETURN e.path
ORDER BY size(e.path) ASC
LIMIT 1
```
2025-05-20 18:30:43,835 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: ```cypher
MATCH (e:Endpoint)
WHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'
RETURN e.path
ORDER BY size(e.path) ASC
LIMIT 1
```
2025-05-20 18:30:43,835 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 18:30:45,220 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - ERROR - Error executing Cypher query: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}
neo4j.exceptions.GqlError: {gql_status: 42I06} {gql_status_description: error: syntax error or access rule violation - invalid input. Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {message: 42I06: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```', expected: 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH'.} {diagnostic_record: {'_classification': 'CLIENT_ERROR', '_position': {'column': 1, 'offset': 0, 'line': 1}, 'OPERATION': '', 'OPERATION_CODE': '0', 'CURRENT_SCHEMA': '/'}} {raw_classification: CLIENT_ERROR}

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\tools\knowledge_graph_tools.py", line 279, in execute_cypher_query
    results = graph.query(cypher_query)
              ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\langchain_community\graphs\neo4j_graph.py", line 467, in query
    data, _, _ = self._driver.execute_query(
                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 970, in execute_query
    return session._run_transaction(
           ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\session.py", line 583, in _run_transaction
    result = transaction_function(tx, *args, **kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_work\query.py", line 144, in wrapped
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\driver.py", line 1306, in _work
    res = tx.run(query, parameters)
          ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\transaction.py", line 206, in run
    result._tx_ready_run(query, parameters)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 177, in _tx_ready_run
    self._run(query, parameters, None, None, None, None, None, None)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 236, in _run
    self._attach()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\work\result.py", line 430, in _attach
    self._connection.fetch_message()
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 184, in inner
    func(*args, **kwargs)
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt.py", line 864, in fetch_message
    res = self._process_message(tag, fields)
          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_bolt5.py", line 1208, in _process_message
    response.on_failure(summary_metadata or {})
  File "D:\Project\TATA_MCVR\Dev\ADK_Tutorial\.venv\Lib\site-packages\neo4j\_sync\io\_common.py", line 254, in on_failure
    raise self._hydrate_error(metadata)
neo4j.exceptions.CypherSyntaxError: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input '```cypher\nMATCH (e:Endpoint)\nWHERE e.description CONTAINS 'football' AND e.description CONTAINS 'upcoming' AND e.description CONTAINS 'matches'\nRETURN e.path\nORDER BY size(e.path) ASC\nLIMIT 1\n```': expected 'FOREACH', 'ALTER', 'ORDER BY', 'CALL', 'USING PERIODIC COMMIT', 'CREATE', 'LOAD CSV', 'START DATABASE', 'STOP DATABASE', 'DEALLOCATE', 'DELETE', 'DENY', 'DETACH', 'DROP', 'DRYRUN', 'FINISH', 'GRANT', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REALLOCATE', 'REMOVE', 'RENAME', 'RETURN', 'REVOKE', 'ENABLE SERVER', 'SET', 'SHOW', 'SKIP', 'TERMINATE', 'UNWIND', 'USE' or 'WITH' (line 1, column 1 (offset: 0))
"```cypher"
 ^}

2025-05-20 18:30:50,904 - super_sport_agent_using_graph_dp.tools.api_tools - INFO - Getting upcoming sports from 2025-05-20 to 2025-05-27
2025-05-20 18:30:50,907 - super_sport_agent_using_graph_dp.services.api_service - INFO - Initialized SuperSport API service with base URL: https://supersport.com/apix/guide/v5.3
2025-05-20 18:30:50,909 - super_sport_agent_using_graph_dp.services.api_service - INFO - Making request to https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=2025-05-20&endDateTime=2025-05-27&channelOnly=False&liveOnly=False
2025-05-20 18:30:53,488 - super_sport_agent_using_graph_dp.services.api_service - INFO - Response received from https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=2025-05-20&endDateTime=2025-05-27&channelOnly=False&liveOnly=False: 4018
2025-05-20 18:30:53,489 - super_sport_agent_using_graph_dp.services.api_service - INFO - Total items in response: 4018
2025-05-20 18:30:53,489 - super_sport_agent_using_graph_dp.services.api_service - INFO - Limiting response to 50 items to prevent token overflow
2025-05-20 18:37:52,252 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next rugby match?
2025-05-20 18:37:52,253 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Using direct Cypher query: MATCH (n:Endpoint) RETURN n LIMIT 1;
2025-05-20 18:37:52,255 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: MATCH (n:Endpoint) RETURN n LIMIT 1;
2025-05-20 18:37:52,257 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 19:27:21,273 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 19:27:21,275 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Starting process_all_files in knowledge_graph_tools...
2025-05-20 19:27:21,277 - super_sport_agent_using_graph_dp.services.document_service - INFO - Initialized document service with directory: D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\documents
2025-05-20 19:27:21,278 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Calling document_service.process_all_files()...
2025-05-20 19:27:21,279 - super_sport_agent_using_graph_dp.services.document_service - INFO - Starting to process all files in the documents directory
2025-05-20 19:27:21,281 - super_sport_agent_using_graph_dp.services.document_service - INFO - Found 0 PDF files, 0 VTT files, 0 text files, and 1 markdown files
2025-05-20 19:27:21,282 - super_sport_agent_using_graph_dp.services.document_service - INFO - Processing PDF files...
2025-05-20 19:27:21,283 - super_sport_agent_using_graph_dp.services.document_service - INFO - Processing VTT files...
2025-05-20 19:27:21,284 - super_sport_agent_using_graph_dp.services.document_service - INFO - Processing text files...
2025-05-20 19:27:21,285 - super_sport_agent_using_graph_dp.services.document_service - INFO - Processing markdown files...
2025-05-20 19:27:21,286 - super_sport_agent_using_graph_dp.services.document_service - DEBUG - Processing markdown file: D:\Project\TATA_MCVR\Dev\ADK_Tutorial\super_sport_agent_using_graph_dp\documents\supersport_api_documentation.md
2025-05-20 19:27:21,288 - super_sport_agent_using_graph_dp.services.document_service - DEBUG - Successfully extracted 11511 characters from markdown: supersport_api_documentation.md
2025-05-20 19:27:21,288 - super_sport_agent_using_graph_dp.services.document_service - INFO - Finished processing files. Total processed: 1, Total text length: 11513
2025-05-20 19:27:21,288 - super_sport_agent_using_graph_dp.services.document_service - INFO - Successfully processed 1 files with content length 11513
2025-05-20 19:27:21,290 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Document processing result status: success, processed files: 1
2025-05-20 19:27:21,290 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Content length: 11513 characters
2025-05-20 19:27:21,291 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Document processing successful with content. Creating SuperSport API knowledge graph...
2025-05-20 19:27:51,369 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Knowledge graph creation result: success
2025-05-20 19:27:51,370 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Returning combined document and SuperSport API knowledge graph result
2025-05-20 19:46:23,606 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next football match?
2025-05-20 19:46:23,607 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Using Cypher query: 
        MATCH (n:Query)-[r:USES_ENDPOINT]->(m:Endpoint)
        WITH m.base_url AS baseUrl, m.name as endpoint, r.parameters AS params
        RETURN baseUrl + endpoint + '?' + params AS completeUrl LIMIT 1
        
2025-05-20 19:46:23,608 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: 
        MATCH (n:Query)-[r:USES_ENDPOINT]->(m:Endpoint)
        WITH m.base_url AS baseUrl, m.name as endpoint, r.parameters AS params
        RETURN baseUrl + endpoint + '?' + params AS completeUrl LIMIT 1
        
2025-05-20 19:46:23,609 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-20 19:53:14,865 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Processing SuperSport API question: When is the next football match?
2025-05-20 19:53:14,867 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Using Cypher query: 
        MATCH (n:Query)-[r:USES_ENDPOINT]->(m:Endpoint)
        WITH m.base_url AS baseUrl, m.name as endpoint, r.parameters AS params
        RETURN baseUrl + endpoint + '?' + params AS completeUrl LIMIT 1
        
2025-05-20 19:53:14,869 - super_sport_agent_using_graph_dp.tools.knowledge_graph_tools - INFO - Executing Cypher query: 
        MATCH (n:Query)-[r:USES_ENDPOINT]->(m:Endpoint)
        WITH m.base_url AS baseUrl, m.name as endpoint, r.parameters AS params
        RETURN baseUrl + endpoint + '?' + params AS completeUrl LIMIT 1
        
2025-05-20 19:53:14,870 - super_sport_agent_using_graph_dp.services.knowledge_graph_service - INFO - Initialized knowledge graph service
