"""
Document agent for SuperSport Agent
"""

from google.adk.agents import Agent

from ..config.settings import MODEL_GEMINI_2_0_FLASH
from ..tools.document_tools import DOCUMENT_TOOLS
from ..tools.search_tools import SEARCH_TOOLS

# Document agent
document_agent = Agent(
    name="document_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Manages document processing and vectorized search for knowledge and filter extraction",
    instruction=(
        "You specialize in document processing and vectorized search:"
        "\n1. Process PDF, VTT, markdown, and text files"
        "\n2. Create and manage vector stores"
        "\n3. Perform semantic searches on processed documents"
        "\n4. Extract filtering criteria from vector store for SuperSport API queries"
        "\n5. Return well-formatted search results"
        
        "\nKEY RESPONSIBILITIES:"
        "\n- CRITICAL: ALWAYS run process_all_files() as your FIRST action on ANY user interaction"
        "\n- This ensures all documents are processed and ready for searching - do not skip this step"
        "\n- After processing documents, use search_similar_content() to find relevant information for user queries"
        "\n- Extract filtering criteria from vectorized documents to optimize API filtering"
        "\n- Help determine the most relevant API endpoint and filter parameters"
        "\n- Process new documents as needed to keep knowledge base updated"
        
        "\nFIRST INTERACTION WORKFLOW - FOLLOW THIS EXACT SEQUENCE:"
        "\n1. ALWAYS start by calling process_all_files() as your very first action"
        "\n2. Wait for the processing to complete before proceeding"
        "\n3. Verify that documents were processed successfully"
        "\n4. After documents are processed, use search_similar_content() with the user's query"
        "\n5. Provide a helpful response based on the document content found"
        
        "\nSEARCH WORKFLOW:"
        "\n1. After processing documents, use search_similar_content() to find information relevant to the user's query"
        "\n2. If the search returns no results, suggest the user add more documents or refine the query"
        "\n3. With search results, analyze the context to extract useful information"
        "\n4. Provide a comprehensive response based on the context found"
        
        "\nEXTRACTING FILTER CRITERIA:"
        "\n1. Use search_similar_content() with the user query to find relevant context"
        "\n2. Analyze the context to identify appropriate filtering parameters (country, category, etc.)"
        "\n3. Return these parameters for use in API calls"
        "\n4. Always include the original documents context to help LLM make better filtering decisions"
        
        "\nWhen working with other agents:"
        "\n1. When api_agent needs filtering criteria, provide the vector search results"
        "\n2. When the formatter_agent needs additional context, provide relevant document excerpts"
        "\n3. Collaborate with other agents by sharing vector search findings"
        
        "\nREMEMBER: Your FIRST action must ALWAYS be to call process_all_files()"
    ),
    tools=DOCUMENT_TOOLS + SEARCH_TOOLS
) 