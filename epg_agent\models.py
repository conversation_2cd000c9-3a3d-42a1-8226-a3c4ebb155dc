from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import date
from enum import Enum

# Models for EPG API Requests

class ApiRequest(BaseModel):
    """Base model for API requests"""
    pass

class ChannelRequest(ApiRequest):
    """Request parameters for channel API"""
    country: Optional[str] = Field(None, description="Filter by country code")
    category: Optional[str] = Field(None, description="Filter by category")
    language: Optional[str] = Field(None, description="Filter by language code")

class FeedRequest(ApiRequest):
    """Request parameters for feed API"""
    channel: Optional[str] = Field(None, description="Filter by channel ID")
    region: Optional[str] = Field(None, description="Filter by region code")
    country: Optional[str] = Field(None, description="Filter by country code")

class StreamRequest(ApiRequest):
    """Request parameters for stream API"""
    channel: Optional[str] = Field(None, description="Filter by channel ID")
    quality: Optional[str] = Field(None, description="Filter by stream quality")

class QueryRequest(ApiRequest):
    """Natural language query request"""
    query: str = Field(..., description="The natural language query to process")
    max_results: Optional[int] = Field(10, description="Maximum number of results to return")

# Models for EPG API Responses

class ApiResponse(BaseModel):
    """Base model for API responses"""
    status: str = Field(..., description="Status of the API response (success/error)")
    message: Optional[str] = Field(None, description="Response message, particularly for errors")

class Channel(BaseModel):
    """Channel model based on the EPG API schema"""
    id: str
    name: str
    alt_names: Optional[List[str]] = []
    network: Optional[str] = None
    owners: Optional[List[str]] = []
    country: Optional[str] = None
    subdivision: Optional[str] = None
    city: Optional[str] = None
    categories: Optional[List[str]] = []
    is_nsfw: Optional[bool] = False
    launched: Optional[str] = None
    closed: Optional[str] = None
    replaced_by: Optional[str] = None
    website: Optional[str] = None
    logo: Optional[str] = None

class Feed(BaseModel):
    """Feed model based on the EPG API schema"""
    channel: str
    id: str
    name: str
    is_main: Optional[bool] = False
    broadcast_area: Optional[List[str]] = []
    timezones: Optional[List[str]] = []
    languages: Optional[List[str]] = []
    format: Optional[str] = None

class Stream(BaseModel):
    """Stream model based on the EPG API schema"""
    channel: Optional[str] = None
    feed: Optional[str] = None
    url: str
    referrer: Optional[str] = None
    user_agent: Optional[str] = None
    quality: Optional[str] = None

class Category(BaseModel):
    """Category model based on the EPG API schema"""
    id: str
    name: str

class Language(BaseModel):
    """Language model based on the EPG API schema"""
    name: str
    code: str

class Country(BaseModel):
    """Country model based on the EPG API schema"""
    name: str
    code: str
    languages: List[str]
    flag: str

class Subdivision(BaseModel):
    """Subdivision model based on the EPG API schema"""
    country: str
    name: str
    code: str

class Region(BaseModel):
    """Region model based on the EPG API schema"""
    code: str
    name: str
    countries: List[str]

class Timezone(BaseModel):
    """Timezone model based on the EPG API schema"""
    id: str
    utc_offset: str
    countries: List[str]

class BlockItem(BaseModel):
    """Blocklist item model based on the EPG API schema"""
    channel: str
    reason: str
    ref: Optional[str] = None

class Guide(BaseModel):
    """Guide model based on the EPG API schema"""
    channel: Optional[str] = None
    feed: Optional[str] = None
    site: str
    site_id: str
    site_name: str
    lang: str

# Response models for specific endpoints

class ChannelsResponse(ApiResponse):
    """Response model for channels endpoint"""
    data: Optional[List[Channel]] = []
    total_items: Optional[int] = 0

class FeedsResponse(ApiResponse):
    """Response model for feeds endpoint"""
    data: Optional[List[Feed]] = []
    total_items: Optional[int] = 0

class StreamsResponse(ApiResponse):
    """Response model for streams endpoint"""
    data: Optional[List[Stream]] = []
    total_items: Optional[int] = 0

class CategoriesResponse(ApiResponse):
    """Response model for categories endpoint"""
    data: Optional[List[Category]] = []
    total_items: Optional[int] = 0

class LanguagesResponse(ApiResponse):
    """Response model for languages endpoint"""
    data: Optional[List[Language]] = []
    total_items: Optional[int] = 0

class CountriesResponse(ApiResponse):
    """Response model for countries endpoint"""
    data: Optional[List[Country]] = []
    total_items: Optional[int] = 0

class SubdivisionsResponse(ApiResponse):
    """Response model for subdivisions endpoint"""
    data: Optional[List[Subdivision]] = []
    total_items: Optional[int] = 0

class RegionsResponse(ApiResponse):
    """Response model for regions endpoint"""
    data: Optional[List[Region]] = []
    total_items: Optional[int] = 0

class TimezonesResponse(ApiResponse):
    """Response model for timezones endpoint"""
    data: Optional[List[Timezone]] = []
    total_items: Optional[int] = 0

class BlocklistResponse(ApiResponse):
    """Response model for blocklist endpoint"""
    data: Optional[List[BlockItem]] = []
    total_items: Optional[int] = 0

class GuidesResponse(ApiResponse):
    """Response model for guides endpoint"""
    data: Optional[List[Guide]] = []
    total_items: Optional[int] = 0

class QueryResponseItem(BaseModel):
    """Item in query response"""
    source: str = Field(..., description="Source endpoint of the data")
    data: Any = Field(..., description="The actual data")

class QueryResponse(ApiResponse):
    """Response model for natural language queries"""
    query: str = Field(..., description="The original query")
    data: List[QueryResponseItem] = []
    endpoints_queried: List[str] = []
    context: Optional[List[str]] = [] 