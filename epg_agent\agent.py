import os
import sys
import logging
import traceback
from PyPDF2 import PdfReader
import webvtt
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai
from langchain_community.vectorstores import FAISS
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm
from datetime import datetime, timezone
import pytz

# Import MCP tools
from .mcp_tools import (
    mcp_epg_get_channels,
    mcp_epg_get_channel,
    mcp_epg_get_feeds,
    mcp_epg_get_feed,
    mcp_epg_get_streams,
    mcp_epg_get_categories,
    mcp_epg_get_languages,
    mcp_epg_get_countries,
    mcp_epg_get_subdivisions,
    mcp_epg_get_regions,
    mcp_epg_get_timezones,
    mcp_epg_get_blocklist,
    mcp_epg_get_guides,
    mcp_epg_query,
    EPG_MCP_TOOLS
)

# Set up logging
current_project_path = os.path.abspath(os.path.dirname(__file__))
logs_dir = os.path.join(current_project_path, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Create a timestamp for the log file
log_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(logs_dir, f"epg_agent_{log_timestamp}.log")

# Configure logger
logger = logging.getLogger('epg_agent')
logger.setLevel(logging.DEBUG)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create formatter and add it to the handlers
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add a console handler for immediate feedback
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)

# Add the handlers to the logger
logger.addHandler(file_handler)
logger.addHandler(console_handler)

logger.info(f"EPG Agent logging initialized. Log file: {log_file}")

# Load environment variables
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable is not set")
else:
    genai.configure(api_key=GOOGLE_API_KEY)

# Project paths
current_project_path = os.path.abspath(os.path.dirname(__file__))
documents_dir = os.path.join(current_project_path, "documents")
vector_store_path = os.path.join(current_project_path, "faiss_index")
os.makedirs(documents_dir, exist_ok=True)

# Flag to indicate if documents have been processed
processed_documents = False

def list_files_in_documents_dir() -> dict:
    try:
        files = os.listdir(documents_dir)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        vtt_files = [f for f in files if f.lower().endswith('.vtt')]
        text_files = [f for f in files if f.lower().endswith(('.txt', '.md', '.text'))]
        return {
            "documents_dir": documents_dir,
            "pdf_files": pdf_files,
            "vtt_files": vtt_files,
            "text_files": text_files
        }
    except Exception as e:
        return {"error": str(e), "documents_dir": documents_dir}

def get_pdf_text(pdf_path):
    text = ""
    try:
        pdf_reader = PdfReader(pdf_path)
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        return f"Error processing PDF: {str(e)}"

def get_text_file_content(text_path):
    try:
        with open(text_path, 'r', encoding='utf-8') as file:
            text = file.read()
        return text
    except UnicodeDecodeError:
        try:
            with open(text_path, 'r', encoding='latin-1') as file:
                text = file.read()
            return text
        except Exception as e:
            return f"Error processing text file: {str(e)}"
    except Exception as e:
        return f"Error processing text file: {str(e)}"

def get_vtt_text(vtt_path):
    text = ""
    try:
        for caption in webvtt.read(vtt_path):
            text += f"{caption.text}\n"
        return text
    except Exception as e:
        return f"Error processing VTT: {str(e)}"

def get_text_chunks(text):
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
    return text_splitter.split_text(text)

def create_vector_store(text_chunks):
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
    os.makedirs(os.path.dirname(vector_store_path), exist_ok=True)
    vector_store.save_local(vector_store_path)
    return "Vector store created successfully"

def process_all_files() -> dict:
    global processed_documents
    try:
        files = list_files_in_documents_dir()
        pdf_files = files.get("pdf_files", [])
        vtt_files = files.get("vtt_files", [])
        text_files = files.get("text_files", [])
        all_text = ""
        processed_count = 0
        for pdf_file in pdf_files:
            file_path = os.path.join(documents_dir, pdf_file)
            text = get_pdf_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for vtt_file in vtt_files:
            file_path = os.path.join(documents_dir, vtt_file)
            text = get_vtt_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for text_file in text_files:
            file_path = os.path.join(documents_dir, text_file)
            text = get_text_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        if all_text:
            chunks = get_text_chunks(all_text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": f"Processed {processed_count} files and created vector store"}
        else:
            return {"status": "warning", "message": "No text was extracted from any files"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def process_file(file_path: str) -> dict:
    global processed_documents
    try:
        text = ""
        if file_path.lower().endswith('.pdf'):
            text = get_pdf_text(file_path)
        elif file_path.lower().endswith('.vtt'):
            text = get_vtt_text(file_path)
        elif file_path.lower().endswith(('.txt', '.md', '.text')):
            text = get_text_file_content(file_path)
        else:
            return {"status": "error", "message": "Unsupported file type."}
        if text and not text.startswith("Error"):
            chunks = get_text_chunks(text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": "File processed and vector store created"}
        else:
            return {"status": "error", "message": text}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def create_vector_store_from_text(text: str) -> dict:
    global processed_documents
    try:
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        return {"status": "success", "message": "Text processed and vector store created"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def process_and_create_vector_store(file_path: str) -> dict:
    result = process_file(file_path)
    if result.get("status") == "success":
        return {"status": "success", "message": "File processed and vector store created successfully"}
    else:
        return result

def search_similar_content(query: str, num_results: int = 3) -> dict:
    """
    Search for similar content in the vector store based on the query
    """
    global processed_documents
    try:
        if not processed_documents:
            return {"status": "error", "context": [], "message": "No documents have been processed yet. Please process documents first."}
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        docs = vector_store.similarity_search(query, k=num_results)
        return {"status": "success", "context": [doc.page_content for doc in docs]}
    except Exception as e:
        return {"status": "error", "context": [], "message": f"Error: {str(e)}"}

def analyze_query(question: str) -> dict:
    """
    Analyze a user query to determine which APIs need to be called
    
    Args:
        question: The user's natural language question
        
    Returns:
        A dictionary with the query analysis and recommended API calls
    """
    try:
        question_lower = question.lower()
        
        # Initialize results
        analysis = {
            "original_query": question,
            "query_type": "unknown",
            "entities": [],
            "recommended_apis": [],
            "parameters": {}
        }
        
        # Detect query type
        if any(term in question_lower for term in ["channel", "station", "network", "tv"]):
            analysis["query_type"] = "channel_related"
            analysis["recommended_apis"].append("channels")
            
        if any(term in question_lower for term in ["feed", "broadcast", "airing", "showing"]):
            analysis["query_type"] = "feed_related"
            analysis["recommended_apis"].append("feeds")
            
        if any(term in question_lower for term in ["stream", "watch", "view", "url", "link"]):
            analysis["query_type"] = "stream_related"
            analysis["recommended_apis"].append("streams")
            
        if any(term in question_lower for term in ["category", "type", "genre", "kind"]):
            analysis["query_type"] = "category_related"
            analysis["recommended_apis"].append("categories")
            
        if any(term in question_lower for term in ["country", "nation", "region", "area", "location"]):
            analysis["query_type"] = "location_related"
            analysis["recommended_apis"].append("countries")
            analysis["recommended_apis"].append("regions")
            
        if any(term in question_lower for term in ["language", "languages"]):
            analysis["query_type"] = "language_related"
            analysis["recommended_apis"].append("languages")
            
        # Detect specific topics
        topics = {
            "news": ["news", "current events", "headlines"],
            "sports": ["sport", "football", "soccer", "basketball", "baseball", "tennis", "game"],
            "entertainment": ["movie", "film", "show", "series", "comedy", "drama"],
            "music": ["music", "concert", "song"],
            "kids": ["kid", "child", "cartoon", "animation"],
            "documentary": ["documentary", "educational", "learning"],
            "cooking": ["cook", "food", "cuisine", "chef", "recipe"]
        }
        
        for topic, keywords in topics.items():
            if any(keyword in question_lower for keyword in keywords):
                analysis["entities"].append({"type": "topic", "value": topic})
                # Add topic as potential category parameter
                if topic in ["news", "sports", "music", "kids"]:
                    analysis["parameters"]["category"] = topic
        
        # Detect well-known channel names that might be mentioned in the question
        common_channels = [
            {"name": "HBO", "id": "HBO.us"},
            {"name": "CNN", "id": "CNN.us"},
            {"name": "BBC", "id": "BBCOne.uk"},
            {"name": "Discovery", "id": "DiscoveryChannel.us"},
            {"name": "National Geographic", "id": "NationalGeographic.us"},
            {"name": "ESPN", "id": "ESPN.us"},
            {"name": "FOX", "id": "FOX.us"},
            {"name": "NBC", "id": "NBC.us"},
            {"name": "CBS", "id": "CBS.us"},
            {"name": "ABC", "id": "ABC.us"},
            {"name": "Disney", "id": "DisneyChannel.us"},
            {"name": "Sky", "id": "SkyOne.uk"}
        ]
        
        # Check if any of the common channels are mentioned in the question
        for channel in common_channels:
            if channel["name"].lower() in question_lower:
                analysis["entities"].append({"type": "channel", "value": channel["name"]})
                analysis["parameters"]["channel"] = channel["id"]
                break
                
        # Handle potential time/schedule questions
        if any(term in question_lower for term in ["when", "time", "schedule", "showing", "broadcast", "air", "playing", "now", "currently"]):
            analysis["query_type"] = "schedule_related"
            analysis["recommended_apis"].extend(["channels", "feeds", "guides"])
            
            # For "playing now" type questions, prioritize the guides API
            if "now" in question_lower or "currently" in question_lower or "right now" in question_lower:
                # Add guides to the beginning of the list for priority
                if "guides" in analysis["recommended_apis"]:
                    analysis["recommended_apis"].remove("guides")
                analysis["recommended_apis"].insert(0, "guides")
            
        # If no specific APIs determined yet, recommend general approach
        if not analysis["recommended_apis"]:
            analysis["recommended_apis"] = ["channels", "categories"]
            
        # Always add query as a fallback
        analysis["recommended_apis"].append("query")
        
        return {
            "status": "success", 
            "analysis": analysis
        }
    except Exception as e:
        logger.error(f"Error in analyze_query: {str(e)}")
        return {
            "status": "error",
            "message": f"Error analyzing query: {str(e)}"
        }

def execute_multi_api_query(question: str, max_results: int = 5) -> dict:
    """
    Execute a query across multiple EPG APIs based on query analysis
    
    Args:
        question: The user's natural language question
        max_results: Maximum number of results to return per API
        
    Returns:
        Combined results from multiple API calls
    """
    try:
        # First analyze the query
        analysis_result = analyze_query(question)
        if analysis_result.get("status") != "success":
            return analysis_result
            
        analysis = analysis_result.get("analysis", {})
        api_calls = analysis.get("recommended_apis", [])
        parameters = analysis.get("parameters", {})
        
        # Initialize results
        results = {
            "status": "success",
            "query": question,
            "api_calls": api_calls,
            "combined_data": [],
            "metadata": {
                "analysis": analysis
            }
        }
        
        # Execute each recommended API call
        for api in api_calls:
            api_data = None
            
            if api == "channels":
                # Get channels, applying any detected parameters
                category = parameters.get("category")
                country = parameters.get("country")
                language = parameters.get("language")
                
                channel_data = mcp_epg_get_channels(
                    category=category,
                    country=country,
                    language=language
                )
                
                if channel_data.get("status") == "success":
                    # Limit results
                    limited_data = channel_data.get("data", [])[:max_results]
                    api_data = {
                        "api": "channels",
                        "data": limited_data,
                        "total_available": channel_data.get("total_items", 0)
                    }
                    
            elif api == "feeds":
                # Get feeds, applying any detected parameters
                channel = parameters.get("channel")
                region = parameters.get("region")
                country = parameters.get("country")
                
                feed_data = mcp_epg_get_feeds(
                    channel=channel,
                    region=region,
                    country=country
                )
                
                if feed_data.get("status") == "success":
                    # Limit results
                    limited_data = feed_data.get("data", [])[:max_results]
                    api_data = {
                        "api": "feeds",
                        "data": limited_data,
                        "total_available": feed_data.get("total_items", 0)
                    }
                    
            elif api == "streams":
                # Get streams, applying any detected parameters
                channel = parameters.get("channel")
                quality = parameters.get("quality")
                
                stream_data = mcp_epg_get_streams(
                    channel=channel,
                    quality=quality
                )
                
                if stream_data.get("status") == "success":
                    # Limit results
                    limited_data = stream_data.get("data", [])[:max_results]
                    api_data = {
                        "api": "streams",
                        "data": limited_data,
                        "total_available": stream_data.get("total_items", 0)
                    }
                    
            elif api == "categories":
                category_data = mcp_epg_get_categories()
                
                if category_data.get("status") == "success":
                    api_data = {
                        "api": "categories",
                        "data": category_data.get("data", []),
                        "total_available": category_data.get("total_items", 0)
                    }
                    
            elif api == "countries":
                country_data = mcp_epg_get_countries()
                
                if country_data.get("status") == "success":
                    api_data = {
                        "api": "countries",
                        "data": country_data.get("data", []),
                        "total_available": country_data.get("total_items", 0)
                    }
                    
            elif api == "regions":
                region_data = mcp_epg_get_regions()
                
                if region_data.get("status") == "success":
                    api_data = {
                        "api": "regions",
                        "data": region_data.get("data", []),
                        "total_available": region_data.get("total_items", 0)
                    }
                    
            elif api == "languages":
                language_data = mcp_epg_get_languages()
                
                if language_data.get("status") == "success":
                    api_data = {
                        "api": "languages",
                        "data": language_data.get("data", []),
                        "total_available": language_data.get("total_items", 0)
                    }
            
            elif api == "guides":
                # Get guide information for a specific channel if mentioned
                channel = parameters.get("channel")
                
                # If the query is about what's playing now, we need to handle this specially
                if analysis.get("query_type") == "schedule_related" and "now" in question.lower():
                    # First, try to extract channel name from the question
                    channel_name = None
                    for entity in analysis.get("entities", []):
                        if entity.get("type") == "channel":
                            channel_name = entity.get("value")
                            break
                    
                    # If no channel entity was found, try to extract from the question directly
                    if not channel_name:
                        # Look for channel names in the question
                        channel_name_candidates = []
                        # Get all channels
                        all_channels = mcp_epg_get_channels()
                        if all_channels.get("status") == "success":
                            for ch in all_channels.get("data", []):
                                if ch.get("name") and ch.get("name").lower() in question.lower():
                                    channel_name_candidates.append({
                                        "id": ch.get("id"),
                                        "name": ch.get("name")
                                    })
                            
                            # If we found exactly one channel, use it
                            if len(channel_name_candidates) == 1:
                                channel = channel_name_candidates[0]["id"]
                                parameters["channel"] = channel
                    
                # Get the guides information for this channel
                guides_data = mcp_epg_get_guides(channel)
                
                if guides_data.get("status") == "success":
                    # For real-time schedule queries, simulate a response with available guide information
                    if analysis.get("query_type") == "schedule_related" and "now" in question.lower():
                        # Get current time in UTC
                        current_time = datetime.now(timezone.utc)
                        
                        # Create a simulated programming response since we can't make external API calls
                        simulated_data = []
                        for guide in guides_data.get("data", []):
                            # For each guide entry, create a simulated program
                            channel_id = guide.get("channel")
                            channel_info = None
                            
                            # Get the channel information to include in the response
                            if channel_id:
                                channel_data = mcp_epg_get_channel(channel_id)
                                if channel_data.get("status") == "success" and channel_data.get("data"):
                                    channel_info = channel_data.get("data")[0]
                            
                            if channel_info:
                                # Create a simulated current program
                                simulated_data.append({
                                    "channel_id": channel_id,
                                    "channel_name": channel_info.get("name"),
                                    "program_info": {
                                        "title": "Current Programming",
                                        "description": f"Programming information is available through {guide.get('site')}",
                                        "start_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                                        "guide_source": guide.get("site"),
                                        "guide_url": f"https://{guide.get('site')}/tv-guide/{guide.get('site_id')}",
                                        "note": "This is simulated program data. For real-time program information, please visit the channel's website or guide site."
                                    }
                                })
                        
                        api_data = {
                            "api": "guides",
                            "data": simulated_data,
                            "total_available": len(simulated_data),
                            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
                            "note": "This is generated program information based on guide sources. For accurate schedule data, check the guide source websites."
                        }
                    else:
                        # Regular guide data response (not asking about current programming)
                        api_data = {
                            "api": "guides",
                            "data": guides_data.get("data", []),
                            "total_available": guides_data.get("total_items", 0)
                        }
                    
            elif api == "query":
                # Use the general query API as a fallback
                query_data = mcp_epg_query(question, max_results=max_results)
                
                if query_data.get("status") == "success":
                    api_data = {
                        "api": "query",
                        "data": query_data.get("data", []),
                        "endpoints_queried": query_data.get("endpoints_queried", [])
                    }
            
            # Add API data to results if available
            if api_data:
                results["combined_data"].append(api_data)
        
        return results
    except Exception as e:
        logger.error(f"Error in execute_multi_api_query: {str(e)}")
        traceback.print_exc()
        return {
            "status": "error",
            "message": f"Error executing multi-API query: {str(e)}"
        }

def ask_question(question: str) -> dict:
    """
    Process a natural language query about EPG data using the enhanced multi-API approach
    """
    try:
        # Use the new multi-API execution function
        return execute_multi_api_query(question, max_results=10)
    except Exception as e:
        logger.error(f"Error in ask_question: {str(e)}")
        traceback.print_exc()
        return {"status": "error", "message": f"Error processing question: {str(e)}"}

# Define a utility function to simplify program information retrieval 
def get_current_program_info(channel_id: str) -> dict:
    """
    Get simplified information about what's currently playing on a channel
    
    Args:
        channel_id: The ID of the channel to check
        
    Returns:
        Dictionary with program information
    """
    # Get the current time in UTC
    current_time = datetime.now(timezone.utc)
    
    # First get channel information
    channel_data = mcp_epg_get_channel(channel_id)
    if channel_data.get("status") != "success" or not channel_data.get("data"):
        return {
            "status": "error",
            "message": f"Channel not found: {channel_id}",
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        }
    
    channel_info = channel_data.get("data")[0]
    
    # Get guide information for this channel
    guides_data = mcp_epg_get_guides(channel_id)
    if guides_data.get("status") != "success" or not guides_data.get("data"):
        return {
            "status": "error",
            "message": f"No program guide information available for {channel_info.get('name')}",
            "channel": channel_info,
            "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC")
        }
    
    # Create a simulated program info response
    guide = guides_data.get("data")[0]
    
    return {
        "status": "success",
        "channel": channel_info,
        "current_program": {
            "title": "Current Programming",
            "description": f"Programming information is available through {guide.get('site')}",
            "start_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
            "guide_source": guide.get("site"),
            "guide_url": f"https://{guide.get('site')}/tv-guide/{guide.get('site_id')}",
        },
        "current_time": current_time.strftime("%Y-%m-%d %H:%M:%S UTC"),
        "note": "For accurate real-time program information, please visit the channel's website."
    }

# --- Define Model Constants for easier use ---
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"
MODEL_GPT_4O = "openai/gpt-4o"
MODEL_CLAUDE_SONNET = "anthropic/claude-3-sonnet-20240229"
MODEL_GROQ_LLAMA_3_8B_8192 = "groq/llama-3.1-8b-instant"

print("\nEnvironment configured.")

# Create query analyzer subagent
query_analyzer = Agent(
    name="query_analyzer",
    model="gemini-2.0-flash",
    description="Subagent that analyzes user queries to determine which EPG APIs to call",
    instruction=(
        "You are a query analyzer for EPG (Electronic Program Guide) data. Your job is to analyze user queries "
        "and determine which EPG APIs should be called to best answer their question.\n\n"
        "For each query, you should:\n"
        "1. Identify the main intent of the query (e.g., finding channels, getting program information)\n"
        "2. Extract relevant entities (channel names, categories, countries, etc.)\n"
        "3. Determine which APIs should be called (channels, feeds, streams, etc.)\n"
        "4. Suggest appropriate parameters for those API calls\n\n"
        "Your analysis will be used by the main agent to make multiple API calls and combine the results."
    ),
    tools=[analyze_query]
)

# Define the agent with all tools
root_agent = Agent(
    name="epg_agent",
    # model=LiteLlm(model=MODEL_GROQ_LLAMA_3_8B_8192),
    model="gemini-2.0-flash",
    # model="gemini-1.5-pro",
    description=(
        "Agent to answer user questions by retrieving relevant API documentation, analyzing queries, "
        "and executing multiple API calls to the EPG API. The agent uses strongly-typed models for EPG data "
        "and combines results from multiple APIs to provide comprehensive answers."
    ),
    instruction=(
        "You are an intelligent EPG (Electronic Program Guide) assistant specialized in helping users interact with television channel APIs."
        " When a user asks a question like 'How can I find all available news channels?' or 'What feeds are available for BBC?':"
        "\n\n1. Understand what API functionality they're looking for using query analysis"
        "\n2. Use the search_similar_content tool to find relevant API documentation in the processed documents"
        "\n3. Use the execute_multi_api_query function to make multiple API calls based on the query analysis"
        "\n4. Present the combined results in a clear, user-friendly way, explaining what the data means"
        "\n\nFor complex queries like 'When is the football game showing?', you should:"
        "\n1. Analyze the query to understand it requires schedule and content information"
        "\n2. Call multiple APIs (channels, feeds, categories) to gather relevant data"
        "\n3. Look for sports/football channels and their associated feeds"
        "\n4. Present specific information about football content if available"
        "\n5. If exact schedule information isn't available, explain what was found and suggest alternatives"
        "\n\nFor data queries, always try to make multiple relevant API calls to provide the most comprehensive answer."
        "\n\nIf the user's query is ambiguous, ask clarifying questions to better understand their needs."
        "\n\nAlways explain API responses in simple terms, highlighting the most relevant information first."
        "\n\nIf a user uploads documents, use the process_file or process_all_files tool to handle them, then confirm the documents are ready to use."
    ),
    tools=[
        # RAG tools
        list_files_in_documents_dir,
        process_all_files,
        process_file,
        create_vector_store_from_text,
        process_and_create_vector_store,
        search_similar_content,
        
        # Query analysis tools
        analyze_query,
        execute_multi_api_query,
        
        # EPG API tools - add all MCP tools
        *EPG_MCP_TOOLS,
        
        # Legacy tool for compatibility
        ask_question
    ],
) 