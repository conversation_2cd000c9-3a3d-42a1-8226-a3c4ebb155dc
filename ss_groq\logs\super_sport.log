2025-05-21 15:45:51,023 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:53:33,713 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:53:35,708 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next football match?
2025-05-21 15:53:38,891 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: In order to use this chain, you must acknowledge that it can make dangerous requests by setting `allow_dangerous_requests` to `True`.You must narrowly scope the permissions of the database connection to only include necessary permissions. Failure to do so may result in data corruption or loss or reading sensitive data if such data is present in the database.Only use this chain if you understand the risks and have taken the necessary precautions. See https://python.langchain.com/docs/security for more information.
2025-05-21 15:55:56,540 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 15:55:58,334 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are channels shows sports programs?
2025-05-21 15:56:03,789 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel)<-[:BROADCAST_ON]-(e:Event)-[:BELONGS_TO]->(s:Sport)
RETURN c, e, s

2025-05-21 15:56:37,848 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next live matches?
2025-05-21 15:56:40,016 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)
WHERE e.isLive = true
RETURN e.title, e.start
ORDER BY e.start
LIMIT 1

2025-05-21 15:57:44,260 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Show all the completed programs
2025-05-21 15:57:47,049 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)
WHERE e.isLive = false
RETURN e

2025-05-21 16:00:59,256 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Any Rugby matches today?
2025-05-21 16:01:01,329 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE e.sport = "Rugby" AND e.start CONTAINS date()
RETURN e

2025-05-21 16:01:31,025 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: Are the any Rugby matches?
2025-05-21 16:01:33,557 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Rugby"
RETURN e

2025-05-21 20:53:46,187 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-21 20:53:49,014 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: When are the next upcoming football matches?
2025-05-21 20:53:56,466 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Football" AND e.isLive = true
RETURN e.start, e.title
ORDER BY e.start

2025-05-21 20:55:12,279 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are the completed football matches?
2025-05-21 20:55:14,919 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "Football" AND e.isLive = false
RETURN e

2025-05-21 20:57:09,792 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of super sport variety today
2025-05-21 20:57:11,731 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BELONGS_TO]->(s:Sport)
WHERE s.name = "SuperSport Variety" AND e.start CONTAINS date()
RETURN e

2025-05-21 20:58:22,900 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of SuperSport Blitz today
2025-05-21 20:58:24,786 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BROADCAST_ON]->(c:Channel)
WHERE c.name = "SuperSport Blitz" AND e.start CONTAINS date()
RETURN e

2025-05-21 20:59:13,893 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the upcoming programs of SuperSport Blitz
2025-05-21 20:59:15,687 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (c:Channel {name: "SuperSport Blitz"})<-[:BROADCAST_ON]-(e:Event)
WHERE e.start > datetime()
RETURN e

2025-05-21 20:59:40,469 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: List all the programs of SuperSport Blitz
2025-05-21 20:59:42,363 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event)-[:BROADCAST_ON]->(c:Channel)
WHERE c.name = "SuperSport Blitz"
RETURN e

2025-05-21 21:00:29,012 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the program time of "Sports Bulletin Around The Clock"?
2025-05-21 21:00:31,351 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Generated Cypher query: cypher
MATCH (e:Event {title: "Sports Bulletin Around The Clock"})
RETURN e.start AS StartTime, e.end AS EndTime

2025-05-21 21:04:22,931 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 09:56:14,049 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 09:59:06,926 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:11:10,455 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:21:30,370 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:25:17,891 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:41:08,150 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 10:41:09,882 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Querying knowledge graph: What are the upcoming football matches scheduled next?
2025-05-22 10:41:16,637 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `groq/llama-3.1-8b-instant` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 11:06:22,381 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 12:41:03,583 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 13:27:37,012 - super_sport_agent_neo4j_graph_db.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 14:02:34,282 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 14:02:35,793 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next football match?
2025-05-22 14:02:41,204 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `groq/llama-3.1-8b-instant` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 14:06:02,854 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 14:06:02,854 - ss_groq.agents.agent - INFO - Initializing agent with Groq LLM model: llama3-8b-8192
2025-05-22 14:11:10,444 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 14:11:58,143 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: When is the next football match?
2025-05-22 14:12:03,318 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (event:Event)-[:BELONGS_TO]->(sport:Sport {name:"football"})-[:BELONGS_TO]->(nextEvent:Event) WHERE event.end < nextEvent.start RETURN nextEvent.start AS nextMatchTime;
2025-05-22 14:12:30,526 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What are the upcoming football matches scheduled next?
2025-05-22 14:12:31,813 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (e:Event)-[:BELONGS_TO]->(s:Sport {name:"football"})-[:BELONGS_TO]->(c:Channel)<-[:BROADCAST_ON]-(e) WHERE e.start > datetime() AND e.end > datetime() RETURN e.title, e.start, e.end, e.thumbnailUri;
2025-05-22 14:13:01,761 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program Sports Bulletin Around The Clock?
2025-05-22 14:13:04,439 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (event:Event {title: "Sports Bulletin Around The Clock"})-[:BELONGS_TO]->(sport:Sport) RETURN event.start AS scheduledAirtime;
2025-05-22 23:34:44,276 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:34:45,560 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: upcoming football matches scheduled next week
2025-05-22 23:34:54,720 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: {code: Neo.ClientError.Statement.SyntaxError} {message: Invalid input ''7'': expected an expression, 'FOREACH', 'ORDER BY', 'CALL', 'CREATE', 'LOAD CSV', 'DELETE', 'DETACH', 'FINISH', 'INSERT', 'LIMIT', 'MATCH', 'MERGE', 'NODETACH', 'OFFSET', 'OPTIONAL', 'REMOVE', 'RETURN', 'SET', 'SKIP', 'UNION', 'UNWIND', 'USE', 'WITH' or <EOF> (line 1, column 184 (offset: 183))
"MATCH (e:Event)-[:BELONGS_TO]->(s:Sport {name: "football"})-[:AVAILABLE_ON]->(p:Package)-[:BROADCAST_ON]->(c:Channel) WHERE e.start >= datetime() AND e.start <= datetime() + interval '7' day AND e.isLive = false RETURN e.title, e.thumbnailUri, e.start;"
                                                                                                                                                                                        ^}
2025-05-22 23:34:55,192 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: past football matches
2025-05-22 23:34:58,875 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (e:Event)-[:BELONGS_TO]->(sport:Sport {name:"football"})-[:AVAILABLE_ON]->(package:Package)-[:BROADCAST_ON]->(channel:Channel) WHERE e.isLive = FALSE AND e.start <= "2023-03-01" AND e.end >= "2023-03-01" RETURN e, sport, package, channel;
2025-05-22 23:39:10,926 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: available rugby match
2025-05-22 23:39:12,847 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (e:Event)-[:BELONGS_TO]->(s:Sport {name: "Rugby"})-[:AVAILABLE_ON]->(p:Package)-[:BROADCAST_ON]->(c:Channel) WHERE e.isLive = true AND e.showmax = true RETURN e.title AS title, e.start AS start, e.thumbnailUri AS thumbnailUri, c.name AS channelName;
2025-05-22 23:39:35,241 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: scheduled airtime for Sports Bulletin Around The Clock
2025-05-22 23:39:36,849 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (e:Event)-[:BELONGS_TO]->(s:Sport) WHERE s.name = "Sports Bulletin Around The Clock" AND e.showmax = FALSE RETURN e.start AS start, e.end AS end;
2025-05-22 23:42:01,501 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: Sports Bulletin Around The Clock start time, end time 
2025-05-22 23:42:03,840 - ss_groq.services.knowledge_graph_service - INFO - Generated Cypher query: MATCH (e:Event {title: "Sports Bulletin Around The Clock"})-[:BELONGS_TO]->(sport:Sport)-[:AVAILABLE_ON]->(package:Package)-[:BROADCAST_ON]->(channel:Channel) 
WHERE e.start IS NOT NULL AND e.end IS NOT NULL 
RETURN e.start AS start, e.end AS end
2025-05-22 23:44:22,107 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:44:23,418 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program Sports Bulletin Around The Clock?
2025-05-22 23:44:27,711 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 400 - {'error': {'message': 'The model `llama-3.1-70b-versatile` has been decommissioned and is no longer supported. Please refer to https://console.groq.com/docs/deprecations for a recommendation on which model to use instead.', 'type': 'invalid_request_error', 'code': 'model_decommissioned'}}
2025-05-22 23:46:58,926 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:47:00,272 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program "Sports Bulletin Around The Clock"?
2025-05-22 23:47:05,057 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `llama-4-70b-8192` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 23:49:07,949 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:49:09,515 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program Sports Bulletin Around The Clock?
2025-05-22 23:49:14,194 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `llama-3.3-8b-8192` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 23:52:10,790 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:52:13,070 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program Sports Bulletin Around The Clock?
2025-05-22 23:52:17,691 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: Error code: 404 - {'error': {'message': 'The model `llama-3.1-8b-8192` does not exist or you do not have access to it.', 'type': 'invalid_request_error', 'code': 'model_not_found'}}
2025-05-22 23:54:24,024 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
2025-05-22 23:54:25,387 - ss_groq.services.knowledge_graph_service - INFO - Querying knowledge graph: What is the scheduled airtime for the program Sports Bulletin Around The Clock?
2025-05-22 23:54:30,793 - ss_groq.services.knowledge_graph_service - ERROR - Error querying knowledge graph: {code: Neo.ClientError.Statement.SyntaxError} {message: Variable `s` not defined (line 1, column 182 (offset: 181))
"MATCH (e:Event {title: 'Sports Bulletin Around The Clock'})-[:BELONGS_TO]-(sport:Sport)-[:AVAILABLE_ON]->(package:Package)-[:BROADCAST_ON]->(channel:Channel)-[:has_property {start: s, end: e}]->() RETURN s, e;"
                                                                                                                                                                                      ^}
2025-05-23 00:02:34,340 - ss_groq.services.knowledge_graph_service - INFO - Initialized knowledge graph service
