2025-05-13 13:10:55,620 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:10:55,624 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:10:55,626 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 13:10:55,628 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:10:55,630 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:10:55,632 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:16:51,856 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:16:51,858 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:16:51,859 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 13:16:51,860 - iptv_agent - INFO - <PERSON><PERSON> Agent logging initialized
2025-05-13 13:16:51,862 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:16:51,863 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:34:28,724 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:34:28,726 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:34:28,728 - iptv_tools - INFO - IPTV EPG Tools logging initialized
2025-05-13 13:34:28,729 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:34:28,730 - iptv_agent - INFO - API documentation vector store already exists.
2025-05-13 13:34:28,732 - iptv_agent - INFO - API documentation vector store already exists.
2025-05-13 13:38:04,325 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:38:04,328 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:38:04,329 - iptv_tools - INFO - IPTV EPG Tools logging initialized
2025-05-13 13:38:04,330 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:38:04,332 - iptv_agent - INFO - API documentation vector store already exists.
2025-05-13 13:39:17,916 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:39:17,919 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:39:17,920 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 13:39:17,921 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:39:17,922 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:39:17,924 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:49:38,187 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:49:38,191 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:49:38,193 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 13:49:38,194 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:49:38,196 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:49:38,197 - iptv_agent - INFO - Found documents to process. Processing them automatically.
2025-05-13 13:49:40,902 - iptv_agent - INFO - Successfully processed all documents on startup.
2025-05-13 13:49:40,903 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:49:40,904 - iptv_agent - INFO - Found documents to process. Processing them automatically.
2025-05-13 13:49:42,547 - iptv_agent - INFO - Successfully processed all documents on startup.
2025-05-13 14:00:11,324 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 14:00:11,326 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 14:00:11,328 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 14:00:11,329 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 14:00:11,331 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 14:00:11,333 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 14:00:18,021 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 14:00:18,023 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-13 14:00:33,641 - iptv_client - INFO - Limited response to 100 items (total: 37010)
2025-05-13 15:07:15,795 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:07:15,798 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:07:15,800 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:07:15,804 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:07:15,806 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:08:35,257 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:08:35,259 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:08:35,260 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:08:35,261 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:08:35,262 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:08:35,264 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:11:51,554 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:11:51,557 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:11:51,558 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:11:51,560 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:11:51,561 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:11:51,562 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:17:08,928 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:17:08,931 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:17:08,933 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:17:08,935 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:17:08,936 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:17:08,938 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:17:16,911 - iptv_tools - INFO - Created new API vector store
2025-05-13 15:17:20,541 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 15:17:20,545 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-13 15:17:36,041 - iptv_client - INFO - Limited response to 100 items (total: 37010)
2025-05-13 15:17:39,868 - iptv_tools - INFO - Added 33 chunks to API vector store with metadata: {'source': 'channels', 'country': None, 'category': 'news', 'language': None, 'timestamp': '2025-05-13T15:17:36.046212'}
2025-05-13 15:22:34,063 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:22:34,067 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:22:34,069 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:22:34,072 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:22:34,074 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:22:34,077 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:22:44,398 - iptv_tools - INFO - Loaded existing API vector store
2025-05-13 15:24:24,202 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 15:24:24,203 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/guides.json
2025-05-13 15:25:02,104 - iptv_tools - INFO - Added 1 chunks to API vector store with metadata: {'source': 'current_programs', 'channel': 'HBO', 'timestamp': '2025-05-13T15:25:01.629471'}
2025-05-13 15:27:43,930 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 15:27:43,930 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-13 15:34:28,736 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 15:34:28,739 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 15:34:28,741 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 15:34:28,744 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 15:34:28,746 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:34:28,748 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 15:34:37,324 - iptv_tools - INFO - Loaded existing API vector store
2025-05-13 15:35:05,226 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 15:35:05,227 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/guides.json
2025-05-13 15:35:48,429 - iptv_tools - INFO - Added 1 chunks to API vector store with metadata: {'source': 'current_programs', 'channel': '1Sports.in', 'timestamp': '2025-05-13T15:35:47.937298'}
2025-05-13 15:35:50,119 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 15:35:50,121 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/guides.json
2025-05-13 15:36:31,856 - iptv_tools - INFO - Added 1 chunks to API vector store with metadata: {'source': 'current_programs', 'channel': '10TV.in', 'timestamp': '2025-05-13T15:36:31.347934'}
2025-05-14 09:33:28,167 - iptv_client - INFO - IPTV Client logging initialized
2025-05-14 09:33:28,172 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-14 09:33:28,175 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-14 09:33:28,178 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-14 09:33:28,180 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-14 09:33:28,183 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-14 09:33:41,103 - iptv_tools - INFO - Loaded existing API vector store
2025-05-14 09:34:20,657 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-14 09:34:20,658 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-14 09:36:54,565 - iptv_client - INFO - IPTV Client logging initialized
2025-05-14 09:36:54,568 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-14 09:36:54,569 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-14 09:36:54,571 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-14 09:36:54,573 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-14 09:36:54,574 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-14 09:37:10,499 - iptv_tools - INFO - Loaded existing API vector store
2025-05-14 09:38:45,580 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-14 09:38:45,580 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-14 09:50:45,663 - iptv_tools - INFO - Added 11965 chunks to API vector store with metadata: {'source': 'channels', 'country': 'IN', 'category': 'news', 'language': 'hi', 'timestamp': '2025-05-14T09:39:10.104545'}
