# IPTV.org API Reference Document

## Table of Contents
1. [Channels API](#channels-api)
2. [Feeds API](#feeds-api)
3. [Streams API](#streams-api)
4. [Guides API](#guides-api)
5. [Categories API](#categories-api)
6. [Languages API](#languages-api)
7. [Countries API](#countries-api)
8. [Subdivisions API](#subdivisions-api)
9. [Regions API](#regions-api)
10. [Timezones API](#timezones-api)
11. [Blocklist API](#blocklist-api)
12. [Advanced API Combinations](#advanced-api-combinations)

## Channels API
**Endpoint**: `GET /channels.json`

### Response Format
```json
[
  {
    "id": "AnhuiTV.cn",
    "name": "Anhui TV",
    "alt_names": ["安徽卫视"],
    "network": "Anhui",
    "owners": ["China Central Television"],
    "country": "CN",
    "subdivision": "CN-AH",
    "city": "Hefei",
    "categories": ["general"],
    "is_nsfw": false,
    "launched": "2016-07-28",
    "closed": "2020-05-31",
    "replaced_by": "CCTV1.cn",
    "website": "http://www.ahtv.cn/",
    "logo": "https://example.com/logo.png"
  }
]
```

### Field Relationships
- `id`: Referenced by `channel` field in Feeds, Streams, Guides, and Blocklist APIs
- `country`: References the `code` field in Countries API
- `subdivision`: References the `code` field in Subdivisions API
- `categories`: References the `id` field in Categories API

### Questions by Field

#### ID Field
1. "What's the unique identifier for BBC One?"
2. "Find a channel with ID containing 'CNN'"
3. "How many channels have '.us' in their ID?"
4. "List all channel IDs from Italian TV providers"
5. "Which channel ID should I use to find Sky Sports?"

**Filter Example**: 
```python
[channel for channel in channels if "CNN" in channel["id"]]
```

#### Name Field
1. "What's the official name of MTV?"
2. "Find all channels with 'News' in their name"
3. "Is there a channel named 'Discovery Channel'?"
4. "List channels with 'Kids' or 'Children' in their name"
5. "How many channels have 'TV' in their name?"

**Filter Example**: 
```python
[channel for channel in channels if "news" in channel["name"].lower()]
```

#### Alt_names Field
1. "What are the alternative names for China Central Television?"
2. "Find channels with Japanese alternative names"
3. "Which channels have alternative names in Arabic?"
4. "Does CNN have any alternative names?"
5. "List channels with the most alternative names"

**Filter Example**: 
```python
import re
[channel for channel in channels if channel.get("alt_names") and any(re.search(r'[\u3040-\u30ff\u3400-\u4dbf\u4e00-\u9fff]', name) for name in channel["alt_names"])]
```

#### Network Field
1. "What channels belong to the NBC network?"
2. "Which network operates CNN?"
3. "List all channels in the BBC network"
4. "Are there any independent channels not belonging to any network?"
5. "Find channels from the HBO network"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("network") == "NBC"]
```

#### Owners Field
1. "Which channels are owned by Disney?"
2. "What TV channels does ViacomCBS own?"
3. "List channels with multiple owners"
4. "Find all channels owned by AT&T"
5. "Which public broadcasters own channels in Europe?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("owners") and "Disney" in channel["owners"]]
```

#### Country Field
1. "Which channels broadcast from Germany?"
2. "How many channels are from Canada?"
3. "List all US-based channels"
4. "Find channels from Asian countries"
5. "Which country has the most TV channels?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("country") == "DE"]
```

#### Subdivision Field
1. "Which channels broadcast from California?"
2. "Find TV channels from Ontario, Canada"
3. "List channels from Bavaria, Germany"
4. "Are there any channels from New South Wales, Australia?"
5. "Which subdivision has the most local channels?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("subdivision") == "US-CA"]
```

#### City Field
1. "What channels broadcast from New York City?"
2. "Find all channels from London"
3. "List channels broadcasting from Paris"
4. "Are there any channels from Tokyo?"
5. "Which city has the most TV broadcasters?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("city") == "New York"]
```

#### Categories Field
1. "What sports channels are available?"
2. "Find all news channels"
3. "List channels in the documentary category"
4. "Which channels have multiple categories?"
5. "Are there any channels in the 'religious' category?"

**Filter Example**: 
```python
[channel for channel in channels if "sports" in channel.get("categories", [])]
```

#### Is_nsfw Field
1. "Which channels contain adult content?"
2. "List all family-friendly channels"
3. "Are there any NSFW channels in the sports category?"
4. "Find NSFW channels that are accessible in Europe"
5. "What percentage of channels are marked as NSFW?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("is_nsfw") == True]
```

#### Launched Field
1. "Which channels were launched in 2020?"
2. "Find all channels launched before 2000"
3. "List channels launched in the last 5 years"
4. "What was the oldest launched channel still broadcasting?"
5. "How many channels were launched in the 1990s?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("launched") and channel["launched"].startswith("2020")]
```

#### Closed Field
1. "Which channels have stopped broadcasting?"
2. "Find channels that closed in 2021"
3. "List channels that are still active"
4. "Which channels closed after less than a year of broadcasting?"
5. "What was the most recent channel to close down?"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("closed") is not None]
```

#### Website Field
1. "What's the official website for BBC?"
2. "Find channels without a website"
3. "List channels with .org websites"
4. "Which news channels have secure (https) websites?"
5. "Find channels with websites containing 'tv' in the domain"

**Filter Example**: 
```python
[channel for channel in channels if channel.get("website") and ".org" in channel["website"]]
```

## Feeds API
**Endpoint**: `GET /feeds.json`

### Response Format
```json
[
  {
    "channel": "BBCOne.uk",
    "id": "EastMidlandsHD",
    "name": "East Midlands HD",
    "is_main": false,
    "broadcast_area": ["c/UK"],
    "timezones": ["Europe/London"],
    "languages": ["eng"],
    "format": "1080i"
  }
]
```

### Field Relationships
- `channel`: References the `id` field in Channels API
- `languages`: References the `code` field in Languages API
- `timezones`: References the `id` field in Timezones API
- `broadcast_area`: References country codes with "c/" prefix or subdivision codes with "s/" prefix

### Questions by Field

#### Channel Field
1. "What feeds are available for BBC One?"
2. "List all feeds for CNN"
3. "How many feeds does NBC have?"
4. "Find feeds for channels from the Discovery network"
5. "Which channel has the most feeds?"

**Filter Example**: 
```python
[feed for feed in feeds if feed.get("channel") == "BBCOne.uk"]
```

#### ID Field
1. "What's the ID for BBC One's East Midlands feed?"
2. "Find feeds with 'HD' in their ID"
3. "List all feed IDs for CNN"
4. "Are there any feeds with region identifiers in their IDs?"
5. "Which feed ID corresponds to the West Coast version of CBS?"

**Filter Example**: 
```python
[feed for feed in feeds if "HD" in feed.get("id", "")]
```

#### Name Field
1. "What's the full name of the BBC East Midlands feed?"
2. "Find all HD feeds by name"
3. "List feeds with regional names"
4. "Which feeds have 'International' in their name?"
5. "How many feeds include quality indicators in their name?"

**Filter Example**: 
```python
[feed for feed in feeds if "HD" in feed.get("name", "")]
```

#### Is_main Field
1. "Which feeds are the main broadcast for their channel?"
2. "Find all secondary/regional feeds"
3. "List main feeds for news channels"
4. "What percentage of feeds are marked as main feeds?"
5. "Which countries have the most alternative (non-main) feeds?"

**Filter Example**: 
```python
[feed for feed in feeds if feed.get("is_main") == True]
```

#### Broadcast_area Field
1. "Which feeds are available in Canada?"
2. "Find feeds broadcasting in California"
3. "List feeds available across multiple countries"
4. "Which regional feeds are exclusive to New York state?"
5. "Find feeds available in both the UK and Ireland"

**Filter Example**: 
```python
[feed for feed in feeds if "c/CA" in feed.get("broadcast_area", [])]
```

#### Timezones Field
1. "Which feeds operate in the Eastern Time Zone?"
2. "Find feeds broadcasting in multiple time zones"
3. "List feeds available in Pacific Time"
4. "Are there feeds specifically for Australian time zones?"
5. "Which time zone has the most dedicated feeds?"

**Filter Example**: 
```python
[feed for feed in feeds if "America/New_York" in feed.get("timezones", [])]
```

#### Languages Field
1. "What feeds broadcast in Spanish?"
2. "Find feeds with multilingual broadcasts"
3. "List all English-language feeds"
4. "Which German-language feeds are available?"
5. "Find feeds that broadcast in both English and French"

**Filter Example**: 
```python
[feed for feed in feeds if "spa" in feed.get("languages", [])]
```

#### Format Field
1. "Which feeds broadcast in 1080p?"
2. "Find all 4K/UHD feeds"
3. "List standard definition feeds"
4. "What percentage of feeds are in HD format?"
5. "Which channels offer the highest quality format feeds?"

**Filter Example**: 
```python
[feed for feed in feeds if feed.get("format") and "1080" in feed["format"]]
```

## Streams API
**Endpoint**: `GET /streams.json`

### Response Format
```json
[
  {
    "channel": "BBCOne.uk",
    "feed": "EastMidlandsHD",
    "url": "http://1111296894.rsc.cdn77.org/LS-ATL-54548-6/index.m3u8",
    "referrer": "http://example.com/",
    "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64)",
    "quality": "720p"
  }
]
```

### Field Relationships
- `channel`: References the `id` field in Channels API
- `feed`: References the `id` field in Feeds API for a specific channel

### Questions by Field

#### Channel Field
1. "What stream URLs are available for CNN?"
2. "Find all streams for BBC channels"
3. "How many different channels have streams?"
4. "List streams for sports channels"
5. "Which news channels have the most streams?"

**Filter Example**: 
```python
[stream for stream in streams if "CNN" in stream.get("channel", "")]
```

#### Feed Field
1. "What streams are available for BBC One's East Midlands feed?"
2. "Find streams for main feeds only"
3. "List streams for HD feeds"
4. "Are there any streams for international feeds?"
5. "Which feed has the most alternative streams?"

**Filter Example**: 
```python
[stream for stream in streams if stream.get("feed") == "EastMidlandsHD"]
```

#### URL Field
1. "What's the streaming URL for CNN International?"
2. "Find streams with HLS (.m3u8) URLs"
3. "List streams with URLs from a specific CDN"
4. "Which streams have HTTPS URLs?"
5. "Find streams with MPEG-DASH (.mpd) URLs"

**Filter Example**: 
```python
[stream for stream in streams if ".m3u8" in stream.get("url", "")]
```

#### Referrer Field
1. "Which streams require a specific HTTP referrer?"
2. "Find streams that don't need a referrer"
3. "List streams requiring referrers from a specific domain"
4. "What percentage of streams need a referrer set?"
5. "For which channels are referrer requirements most common?"

**Filter Example**: 
```python
[stream for stream in streams if stream.get("referrer") is not None]
```

#### User_agent Field
1. "Which streams require a custom user agent?"
2. "Find streams that work with any user agent"
3. "List streams requiring a specific browser user agent"
4. "What percentage of streams need a user agent specified?"
5. "Which user agent string is most commonly required?"

**Filter Example**: 
```python
[stream for stream in streams if stream.get("user_agent") is not None]
```

#### Quality Field
1. "Find all HD quality streams"
2. "Which streams are available in 1080p?"
3. "List all SD quality streams"
4. "What's the highest quality stream for CNN?"
5. "Which quality level is most common across all streams?"

**Filter Example**: 
```python
[stream for stream in streams if stream.get("quality") in ["720p", "1080p"]]
```

## Guides API
**Endpoint**: `GET /guides.json`

### Response Format
```json
[
  {
    "channel": "BBCOne.uk",
    "feed": "EastMidlandsHD",
    "site": "sky.co.uk",
    "site_id": "bbcone",
    "site_name": "BBC One",
    "lang": "en"
  }
]
```

### Field Relationships
- `channel`: References the `id` field in Channels API
- `feed`: References the `id` field in Feeds API for a specific channel
- `lang`: References a two-letter language code (different from the three-letter codes used in other APIs)

### Questions by Field

#### Channel Field
1. "What guide information is available for BBC channels?"
2. "Find program guides for sports channels"
3. "List all channels with guide data"
4. "How many news channels have guide information?"
5. "Which channel has the most guide sources?"

**Filter Example**: 
```python
[guide for guide in guides if "BBC" in guide.get("channel", "")]
```

#### Feed Field
1. "Is guide data available for BBC One's East Midlands feed?"
2. "Find guides for regional feeds"
3. "List guides for HD feeds only"
4. "Are there dedicated guides for international feeds?"
5. "Which feeds have multiple guide sources?"

**Filter Example**: 
```python
[guide for guide in guides if guide.get("feed") == "EastMidlandsHD"]
```

#### Site Field
1. "Which websites provide TV guide data?"
2. "Find guides from the Sky website"
3. "List all guide sources for BBC channels"
4. "Which site provides the most comprehensive guide coverage?"
5. "Are there any .org sites providing guide data?"

**Filter Example**: 
```python
[guide for guide in guides if "sky" in guide.get("site", "")]
```

#### Site_id Field
1. "What's the site identifier for CNN on the TVGuide website?"
2. "Find channels that share the same site_id across different sites"
3. "List all site_ids for BBC channels"
4. "Are there any discrepancies between channel IDs and site_ids?"
5. "Which site uses the most intuitive site_ids?"

**Filter Example**: 
```python
[guide for guide in guides if guide.get("site_id") == "cnn"]
```

#### Site_name Field
1. "How is BBC One named on different guide sites?"
2. "Find inconsistencies between official channel names and site names"
3. "List guide entries where site_name differs from the channel name"
4. "Which channels have the most variations in site names?"
5. "Are abbreviations common in site_names?"

**Filter Example**: 
```python
# This would require a merge with channel data
[guide for guide in guides if guide.get("site_name") != next((c["name"] for c in channels if c["id"] == guide["channel"]), None)]
```

#### Lang Field
1. "Which guide sources provide data in German?"
2. "Find English-language guides for international channels"
3. "List guide sources by language availability"
4. "What's the most common language for guide data?"
5. "Are there any guides available in Asian languages?"

**Filter Example**: 
```python
[guide for guide in guides if guide.get("lang") == "de"]
```

## Categories API
**Endpoint**: `GET /categories.json`

### Response Format
```json
[
  {
    "id": "documentary",
    "name": "Documentary"
  }
]
```

### Field Relationships
- `id`: Referenced by the `categories` array in Channels API

### Questions by Field

#### ID Field
1. "What's the category ID for sports channels?"
2. "List all available category IDs"
3. "Is there a category ID for educational content?"
4. "Find category IDs related to entertainment"
5. "Which category ID is used for news channels?"

**Filter Example**: 
```python
[category for category in categories if "sport" in category.get("id", "")]
```

#### Name Field
1. "What's the display name for the 'news' category?"
2. "List all category names alphabetically"
3. "Are there categories for children's programming?"
4. "Find categories related to movies or films"
5. "What category names include the word 'entertainment'?"

**Filter Example**: 
```python
[category for category in categories if "news" in category.get("name", "").lower()]
```

## Languages API
**Endpoint**: `GET /languages.json`

### Response Format
```json
[
  {
    "name": "French",
    "code": "fra"
  }
]
```

### Field Relationships
- `code`: Referenced by the `languages` array in Feeds API and by the `languages` array in Countries API

### Questions by Field

#### Name Field
1. "What languages are available for TV broadcasts?"
2. "List all European languages supported"
3. "Are Asian languages like Mandarin supported?"
4. "Find all Romance languages available"
5. "Which languages start with the letter 'S'?"

**Filter Example**: 
```python
[language for language in languages if language.get("name", "").startswith("S")]
```

#### Code Field
1. "What's the ISO code for Spanish?"
2. "Find the language with code 'ara'"
3. "List all three-letter language codes"
4. "Match language codes with their full names"
5. "Which language has the code 'deu'?"

**Filter Example**: 
```python
next((language for language in languages if language.get("code") == "ara"), None)
```

## Countries API
**Endpoint**: `GET /countries.json`

### Response Format
```json
[
  {
    "name": "Canada",
    "code": "CA",
    "languages": ["eng", "fra"],
    "flag": "🇨🇦"
  }
]
```

### Field Relationships
- `code`: Referenced by the `country` field in Channels API and the `country` field in Subdivisions API
- `languages`: References the `code` field in Languages API
- `code`: Referenced in broadcast_area with "c/" prefix in Feeds API

### Questions by Field

#### Name Field
1. "List all countries in alphabetical order"
2. "Find countries with 'Republic' in their name"
3. "What's the full name of country code 'DE'?"
4. "Which countries start with the letter 'A'?"
5. "How many countries are in the system?"

**Filter Example**: 
```python
[country for country in countries if "Republic" in country.get("name", "")]
```

#### Code Field
1. "What's the country code for Brazil?"
2. "Find the country with code 'JP'"
3. "List all European country codes"
4. "Which country has the code 'IN'?"
5. "Match country codes with their full names"

**Filter Example**: 
```python
next((country for country in countries if country.get("code") == "BR"), None)
```

#### Languages Field
1. "Which countries have English as an official language?"
2. "Find countries where Spanish is spoken"
3. "List multilingual countries"
4. "Which countries speak both English and French?"
5. "What country has the most official languages?"

**Filter Example**: 
```python
[country for country in countries if "eng" in country.get("languages", [])]
```

#### Flag Field
1. "What's the flag emoji for Germany?"
2. "Find countries with flags containing the color red"
3. "List flag emojis for all European countries"
4. "Which flag emoji represents Canada?"
5. "Match country flags with their names"

**Filter Example**: 
```python
next((country for country in countries if country.get("code") == "DE"), {}).get("flag")
```

## Subdivisions API
**Endpoint**: `GET /subdivisions.json`

### Response Format
```json
[
  {
    "country": "CA",
    "name": "Ontario",
    "code": "CA-ON"
  }
]
```

### Field Relationships
- `country`: References the `code` field in Countries API
- `code`: Referenced by the `subdivision` field in Channels API
- `code`: Referenced in broadcast_area with "s/" prefix in Feeds API

### Questions by Field

#### Country Field
1. "What subdivisions exist in Canada?"
2. "List all US states in the system"
3. "Find all subdivisions in Australia"
4. "Which country has the most subdivisions?"
5. "Are German states included as subdivisions?"

**Filter Example**: 
```python
[subdivision for subdivision in subdivisions if subdivision.get("country") == "CA"]
```

#### Name Field
1. "Is Bavaria included as a subdivision?"
2. "Find subdivisions with 'North' in their name"
3. "List all subdivision names alphabetically"
4. "Which subdivision names are the longest?"
5. "Are there any subdivisions named after saints?"

**Filter Example**: 
```python
[subdivision for subdivision in subdivisions if "North" in subdivision.get("name", "")]
```

#### Code Field
1. "What's the subdivision code for California?"
2. "Find the subdivision with code 'CA-ON'"
3. "List all subdivision codes for the United Kingdom"
4. "Which subdivision has the code 'US-TX'?"
5. "Match subdivision codes with their full names"

**Filter Example**: 
```python
next((subdivision for subdivision in subdivisions if subdivision.get("code") == "US-CA"), None)
```

## Regions API
**Endpoint**: `GET /regions.json`

### Response Format
```json
[
  {
    "code": "MAGHREB",
    "name": "Maghreb",
    "countries": ["DZ", "LY", "MA", "MR", "TN"]
  }
]
```

### Field Relationships
- `countries`: References the `code` field in Countries API

### Questions by Field

#### Code Field
1. "What's the code for the Middle East region?"
2. "Find regions with codes starting with 'E'"
3. "List all region codes"
4. "Is there a region code for Scandinavia?"
5. "Match region codes with their names"

**Filter Example**: 
```python
[region for region in regions if region.get("code", "").startswith("E")]
```

#### Name Field
1. "What regions are defined in the system?"
2. "Is 'Caribbean' included as a region?"
3. "Find regions with geographical terms in their name"
4. "List region names alphabetically"
5. "Which region name is the longest?"

**Filter Example**: 
```python
[region for region in regions if region.get("name") == "Caribbean"]
```

#### Countries Field
1. "Which countries make up the Maghreb region?"
2. "Find regions that include France"
3. "List all countries in the Scandinavia region"
4. "Which region contains the most countries?"
5. "Is Japan included in East Asia?"

**Filter Example**: 
```python
[region for region in regions if "FR" in region.get("countries", [])]
```

## Timezones API
**Endpoint**: `GET /timezones.json`

### Response Format
```json
[
  {
    "id": "Europe/London",
    "utc_offset": "+00:00",
    "countries": ["UK", "GG", "IM", "JE"]
  }
]
```

### Field Relationships
- `id`: Referenced by the `timezones` array in Feeds API
- `countries`: References the `code` field in Countries API

### Questions by Field

#### ID Field
1. "What time zones are defined for the US?"
2. "Find all European time zones"
3. "List time zones with 'America' in their ID"
4. "Is 'Asia/Tokyo' a valid time zone ID?"
5. "Which time zone IDs relate to Australia?"

**Filter Example**: 
```python
[timezone for timezone in timezones if "America" in timezone.get("id", "")]
```

#### UTC_offset Field
1. "Which time zones are at UTC+0?"
2. "Find all time zones with positive UTC offsets"
3. "List time zones in UTC-5"
4. "What time zones have half-hour offsets?"
5. "Which time zone has the largest positive offset?"

**Filter Example**: 
```python
[timezone for timezone in timezones if timezone.get("utc_offset") == "+00:00"]
```

#### Countries Field
1. "Which countries use the Europe/London time zone?"
2. "Find all time zones used in Russia"
3. "List countries that share the same time zone as France"
4. "Which time zone is used by the most countries?"
5. "What time zones are used in Canada?"

**Filter Example**: 
```python
[timezone for timezone in timezones if "RU" in timezone.get("countries", [])]
```

## Blocklist API
**Endpoint**: `GET /blocklist.json`

### Response Format
```json
[
  {
    "channel": "AnimalPlanetEast.us",
    "reason": "dmca",
    "ref": "https://github.com/iptv-org/iptv/issues/1831"
  }
]
```

### Field Relationships
- `channel`: References the `id` field in Channels API

### Questions by Field

#### Channel Field
1. "What US channels are on the blocklist?"
2. "Find all blocked BBC channels"
3. "List all sports channels that are blocked"
4. "How many Discovery Network channels are blocked?"
5. "Which country has the most blocked channels?"

**Filter Example**: 
```python
[item for item in blocklist if ".us" in item.get("channel", "")]
```

#### Reason Field
1. "Which channels are blocked due to DMCA complaints?"
2. "Find channels blocked for geographic restrictions"
3. "List all possible blocking reasons"
4. "What's the most common reason for blocking channels?"
5. "Are there any channels blocked for content violations?"

**Filter Example**: 
```python
[item for item in blocklist if item.get("reason") == "dmca"]
```

#### Ref Field
1. "What's the reference link for ESPN's blocking?"
2. "Find blocks with GitHub issue references"
3. "List all reference links for DMCA takedowns"
4. "Which blocks have the most detailed reference information?"
5. "Are there any blocks without reference links?"

**Filter Example**: 
```python
[item for item in blocklist if "github" in item.get("ref", "")]
```

## Advanced API Combinations

### Finding Channels by Multiple Criteria
1. "Find all sports channels from the UK that broadcast in HD"
   - Filter: Combine Channels API `country` and `categories` with Feeds API `format`

2. "List Spanish-language news channels available in the US"
   - Filter: Combine Channels API `categories`, Feeds API `languages` and `broadcast_area`

3. "Find documentary channels with guides available in German"
   - Filter: Combine Channels API `categories`, Guides API `lang`

4. "Which sports channels from NBC broadcast in the Eastern time zone?"
   - Filter: Combine Channels API `categories` and `network` with Feeds API `timezones`

5. "Find active children's channels with HD streams"
   - Filter: Combine Channels API `categories` and `closed` with Streams API `quality`

### Advanced Filtering Techniques

1. "Find channels available in all Scandinavian countries"
   - Filter: Use Regions API to get Scandinavian countries, then filter Feeds API

2. "List channels that broadcast in multiple languages including Spanish"
   - Filter: 
   ```python
   [feed for feed in feeds if "spa" in feed.get("languages", []) and len(feed.get("languages", [])) > 1]
   ```

3. "Find channels with alternative names in at least 3 different languages"
   - Filter: 
   ```python
   [channel for channel in channels if channel.get("alt_names") and len(channel.get("alt_names", [])) >= 3]
   ```

4. "Which channels have both SD and HD stream options?"
   - Filter: Compare stream qualities for the same channel
   ```python
   channel_qualities = {}
   for stream in streams:
       channel = stream.get("channel")
       quality = stream.get("quality")
       if not channel or not quality:
           continue
       if channel not in channel_qualities:
           channel_qualities[channel] = set()
       channel_qualities[channel].add("HD" if quality in ["720p", "1080p"] else "SD")
   
   [channel for channel, qualities in channel_qualities.items() if len(qualities) > 1]
   ```

5. "Find recently launched channels (last 2 years) with international feeds"
   - Filter: Combine launch date with feeds in multiple countries
   ```python
   import datetime
   
   two_years_ago = (datetime.datetime.now() - datetime.timedelta(days=730)).strftime("%Y-%m-%d")
   
   recent_channels = [channel["id"] for channel in channels 
                     if channel.get("launched") and channel.get("launched") > two_years_ago]
   
   international_feeds = {}
   for feed in feeds:
       channel = feed.get("channel")
       if not channel:
           continue
       if channel not in international_feeds:
           international_feeds[channel] = set()
       for area in feed.get("broadcast_area", []):
           if area.startswith("c/"):
               international_feeds[channel].add(area[2:])
   
   [channel for channel in recent_channels 
    if channel in international_feeds and len(international_feeds[channel]) > 1]
   ```

### Multi-API Combinations

1. "Get complete information about BBC One including all streams and guide sources"
   - Combine: Channels API + Feeds API + Streams API + Guides API for one channel
   ```python
   channel_id = "BBCOne.uk"
   channel_info = next((c for c in channels if c["id"] == channel_id), None)
   channel_feeds = [f for f in feeds if f.get("channel") == channel_id]
   channel_streams = [s for s in streams if s.get("channel") == channel_id]
   channel_guides = [g for g in guides if g.get("channel") == channel_id]
   
   complete_info = {
       "channel": channel_info,
       "feeds": channel_feeds,
       "streams": channel_streams,
       "guides": channel_guides
   }
   ```

2. "Find all sports channels from North America with their available stream URLs"
   - Combine: Regions API + Channels API + Streams API with category filtering
   ```python
   north_america_region = next((r for r in regions if r["name"] == "North America"), None)
   north_american_countries = north_america_region.get("countries", []) if north_america_region else []
   
   sports_channels = [c for c in channels 
                     if "sports" in c.get("categories", []) 
                     and c.get("country") in north_american_countries]
   
   sports_channel_ids = [c["id"] for c in sports_channels]
   
   sports_streams = [s for s in streams if s.get("channel") in sports_channel_ids]
   
   result = []
   for channel in sports_channels:
       channel_streams = [s for s in sports_streams if s.get("channel") == channel["id"]]
       result.append({
           "channel": channel,
           "streams": channel_streams
       })
   ```

3. "Create a list of European news channels with their streaming URLs and qualities"
   - Combine: Regions API + Channels API + Streams API with category filtering
   ```python
   europe_region = next((r for r in regions if r["name"] == "Europe"), None)
   european_countries = europe_region.get("countries", []) if europe_region else []
   
   news_channels = [c for c in channels 
                   if "news" in c.get("categories", []) 
                   and c.get("country") in european_countries]
   
   news_channel_ids = [c["id"] for c in news_channels]
   
   news_streams = [s for s in streams if s.get("channel") in news_channel_ids]
   
   result = []
   for channel in news_channels:
       channel_streams = [s for s in news_streams if s.get("channel") == channel["id"]]
       if channel_streams:
           result.append({
               "channel": channel["name"],
               "country": channel["country"],
               "streams": [{
                   "url": s.get("url"),
                   "quality": s.get("quality")
               } for s in channel_streams]
           })
   ```

4. "Find all active channels from the Discovery network with their guide information"
   - Combine: Channels API + Guides API with `network` filtering and `closed` field check
   ```python
   discovery_channels = [c for c in channels 
                        if c.get("network") == "Discovery" 
                        and c.get("closed") is None]
   
   discovery_channel_ids = [c["id"] for c in discovery_channels]
   
   discovery_guides = [g for g in guides if g.get("channel") in discovery_channel_ids]
   
   result = []
   for channel in discovery_channels:
       channel_guides = [g for g in discovery_guides if g.get("channel") == channel["id"]]
       result.append({
           "channel": channel,
           "guides": channel_guides
       })
   ```

5. "Get all available streams for HBO channels excluding any that are on the blocklist"
   - Combine: Channels API + Streams API + Blocklist API with exclusion filtering
   ```python
   blocked_channels = [b["channel"] for b in blocklist]
   
   hbo_channels = [c for c in channels 
                  if c.get("network") == "HBO" 
                  and c["id"] not in blocked_channels]
   
   hbo_channel_ids = [c["id"] for c in hbo_channels]
   
   hbo_streams = [s for s in streams 
                 if s.get("channel") in hbo_channel_ids]
   
   result = []
   for channel in hbo_channels:
       channel_streams = [s for s in hbo_streams if s.get("channel") == channel["id"]]
       result.append({
           "channel": channel["name"],
           "streams": [{
               "url": s.get("url"),
               "quality": s.get("quality")
           } for s in channel_streams]
       })
   ``` 