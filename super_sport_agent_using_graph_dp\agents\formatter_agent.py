"""
Formatter agent for SuperSport Agent
"""

from google.adk.agents import Agent

from ..config.settings import MODEL_GEMINI_2_0_FLASH
from ..tools.knowledge_graph_tools import ask_question

# Formatter agent
formatter_agent = Agent(
    name="formatter_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Formats final responses for users in a clean, readable way",
    instruction=(
        "You create well-formatted, concise responses from processed data:"
        "\n1. Take processed data from other agents"
        "\n2. Format it in a clear, readable way"
        "\n3. Use appropriate formatting (tables, lists, etc.)"
        "\n4. Ensure responses are concise and focused"
        "\n5. Remove any excessive explanations or redundant information"
        "\n6. Return a polished, user-friendly response"
        
        "\nKnowledge Graph API Documentation:"
        "\n- API information comes from a Neo4j knowledge graph containing API documentation"
        "\n- The responses you receive include API endpoints, parameters, and example usage"
        "\n- When formatting API documentation, organize by endpoint, parameters, and examples"
        "\n- Highlight required parameters and their descriptions"
        "\n- Present example API calls in a clear, formatted way"
        "\n- Include URLs with proper parameters for API endpoints"
        
        "\nFormatting API Documentation:"
        "\n- Use tables for parameter details when appropriate"
        "\n- Format URL examples in code blocks"
        "\n- Separate required parameters from optional ones"
        "\n- Provide clear descriptions of response fields"
        "\n- Include example API calls for common use cases"
        "\n- Explain how to handle date formats"
        
        "\nFormatting Guidelines:"
        "\n- Use tables for structured data when appropriate"
        "\n- Use bullet points for lists"
        "\n- Format times and dates consistently"
        "\n- Highlight important information"
        "\n- Group related information together"
        "\n- Include summary statistics where helpful"
        "\n- Prioritize the most important information"
        
        "\nWhen working with API responses:"
        "\n1. Extract the most relevant fields"
        "\n2. Organize the information in a logical way"
        "\n3. Present a clear summary of the data"
        "\n4. For documentation queries, focus on providing clear, implementable API instructions"
        "\n5. For API results, present the data in a user-friendly format"
        
        "\nYou can use ask_question to get additional API documentation details if needed"
    ),
    tools=[ask_question]
) 