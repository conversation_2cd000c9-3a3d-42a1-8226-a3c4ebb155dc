"""
IPTV Tools for the IPTV Agent
These tools provide agent access to the IPTV API using proper request and response models
"""

import os
import logging
import sys
import json
from datetime import datetime
from typing import Optional, List, Dict, Any, Callable
from .models import (
    ChannelRequest, ChannelsResponse,
    StreamRequest, StreamsResponse,
    CategoriesResponse, CountriesResponse,
    GuidesResponse,
    QueryRequest, QueryResponse
)
from .iptv_client import IptvApiClient
from .iptv_guide import get_guide_manager, format_guide_entry
from .utils import limit_response_tokens, preprocess_api_response

# Import central logger
from .logger import get_logger

# Get logger for this module
logger = get_logger('iptv_tools')
logger.info("IPTV Tools logging initialized")

# Global IPTV API client instance
_client = None

def get_client() -> IptvApiClient:
    """Get or create the global IPTV API client instance"""
    global _client
    if _client is None:
        _client = IptvApiClient()
    return _client

# IPTV API Tools

def iptv_get_channels(
    country: Optional[str],
    category: Optional[str],
    language: Optional[str],
    max_results: Optional[int],
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get channels from the IPTV API
    
    Args:
        country: Country code to filter by (can be None)
        category: Category to filter by (can be None)
        language: Language code to filter by (can be None)
        max_results: Maximum number of results to return (default: 10)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        # Create request model
        request = None
        if country or category or language:
            request = ChannelRequest(
                country=country,
                category=category,
                language=language
            )
        
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
        
        # Make request with query for intelligent preprocessing
        client = get_client()
        response = client.get_channels(request, query=query)
        
        # Apply token limits to ensure the response isn't too large
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict, 
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=query
        )
        
        # Log the size reduction
        original_size = len(str(response_dict))
        reduced_size = len(str(limited_response))
        logger.info(f"Reduced response size from {original_size} to {reduced_size} bytes")
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_channels: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_channel(channel_id: str) -> Dict[str, Any]:
    """
    Get a specific channel by ID
    
    Args:
        channel_id: The channel ID
        
    Returns:
        ChannelsResponse as dict
    """
    try:
        client = get_client()
        response = client.get_channel(channel_id)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(response_dict)
        
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_channel: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_streams(channel: Optional[str], quality: Optional[str], query: Optional[str] = None) -> Dict[str, Any]:
    """
    Get streams from the IPTV API
    
    Args:
        channel: Channel ID to filter by (can be None)
        quality: Quality to filter by (can be None)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        StreamsResponse as dict
    """
    try:
        # Create request model
        request = None
        if channel or quality:
            request = StreamRequest(
                channel=channel,
                quality=quality
            )
        
        # Make request with query for intelligent preprocessing
        client = get_client()
        response = client.get_streams(request, query=query)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=20,
            max_description_length=100,
            max_total_tokens=100000,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_streams: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_categories(max_results: Optional[int], query: Optional[str] = None) -> Dict[str, Any]:
    """
    Get categories from the IPTV API
    
    Args:
        max_results: Maximum number of results to return (default: 20)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        CategoriesResponse as dict
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 20
            
        client = get_client()
        response = client.get_categories(query=query)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_categories: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_countries(max_results: Optional[int], query: Optional[str] = None) -> Dict[str, Any]:
    """
    Get countries from the IPTV API
    
    Args:
        max_results: Maximum number of results to return (default: 20)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        CountriesResponse as dict
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 20
            
        client = get_client()
        response = client.get_countries(query=query)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_countries: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_guides(channel: Optional[str], max_results: Optional[int], query: Optional[str] = None) -> Dict[str, Any]:
    """
    Get program guide information from the IPTV API
    
    Args:
        channel: Optional channel ID to filter by
        max_results: Maximum number of results to return (default: 10)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        GuidesResponse as dict
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
            
        client = get_client()
        response = client.get_guides(channel, query=query)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_guides: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_current_programs(channel: Optional[str], max_results: Optional[int], query: Optional[str] = None) -> Dict[str, Any]:
    """
    Get currently airing TV programs
    
    Args:
        channel: Optional channel ID to filter by
        max_results: Maximum number of results to return (default: 10)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        GuidesResponse as dict with currently airing programs
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
            
        # Create combined query including time context
        combined_query = "current now" 
        if query:
            combined_query += f" {query}"
        if channel:
            combined_query += f" channel {channel}"
            
        guide_manager = get_guide_manager()
        response = guide_manager.get_current_programs(channel)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=combined_query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_current_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_get_upcoming_programs(
    channel: Optional[str], 
    hours_ahead: int,
    max_results: Optional[int],
    query: Optional[str] = None
) -> Dict[str, Any]:
    """
    Get upcoming TV programs for the next hours
    
    Args:
        channel: Optional channel ID to filter by
        hours_ahead: Number of hours to look ahead (default: 24)
        max_results: Maximum number of results to return (default: 10)
        query: Optional user's natural language query for intelligent filtering
        
    Returns:
        GuidesResponse as dict with upcoming programs
    """
    try:
        # Use default value for hours_ahead if none provided
        if hours_ahead is None:
            hours_ahead = 24
            
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
            
        # Create combined query including time context
        combined_query = "upcoming future" 
        if query:
            combined_query += f" {query}"
        if channel:
            combined_query += f" channel {channel}"
            
        guide_manager = get_guide_manager()
        response = guide_manager.get_upcoming_programs(channel, hours_ahead)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=combined_query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_get_upcoming_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_search_programs(
    query: str,
    channel: Optional[str],
    max_results: Optional[int]
) -> Dict[str, Any]:
    """
    Search for TV programs matching a query
    
    Args:
        query: Search query to match against program titles and descriptions
        channel: Optional channel ID to filter by
        max_results: Maximum number of results to return (default: 10)
        
    Returns:
        GuidesResponse as dict with matching programs
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
            
        guide_manager = get_guide_manager()
        response = guide_manager.search_programs(query, channel)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_search_programs: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "data": []
        }

def iptv_query(query: str, max_results: int) -> Dict[str, Any]:
    """
    Process a natural language query
    
    Args:
        query: The natural language query
        max_results: Maximum number of results to return per endpoint (default: 10)
        
    Returns:
        QueryResponse as dict
    """
    try:
        # Set default max_results if not provided
        if max_results is None:
            max_results = 10
            
        # Create request model
        request = QueryRequest(
            query=query,
            max_results=max_results
        )
        
        # Make request
        client = get_client()
        response = client.query(request)
        
        # Apply token limits
        response_dict = response.dict()
        limited_response = limit_response_tokens(
            response_dict,
            max_items=max_results,
            max_description_length=100,
            max_total_tokens=100000,
            query=query
        )
        
        # Return processed response as dict
        return limited_response
    except Exception as e:
        logger.error(f"Error in iptv_query: {str(e)}")
        return {
            "status": "error",
            "message": f"Error: {str(e)}",
            "query": query,
            "data": [],
            "endpoints_queried": []
        }

# List of IPTV tools for the agent to use
IPTV_TOOLS = [
    iptv_get_channels,
    iptv_get_channel,
    iptv_get_streams,
    iptv_get_categories,
    iptv_get_countries,
    iptv_get_guides,
    iptv_get_current_programs,
    iptv_get_upcoming_programs,
    iptv_search_programs,
    iptv_query,
] 