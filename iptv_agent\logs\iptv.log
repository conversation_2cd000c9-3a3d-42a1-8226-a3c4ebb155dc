2025-05-13 13:00:59,135 - iptv_client - INFO - IPTV Client logging initialized
2025-05-13 13:00:59,139 - iptv_guide - INFO - IPTV Guide module initialized
2025-05-13 13:00:59,144 - iptv_tools - INFO - IPTV Tools logging initialized
2025-05-13 13:00:59,146 - iptv_agent - INFO - IPTV Agent logging initialized
2025-05-13 13:00:59,148 - iptv_agent - INFO - Found documents to process. Processing them automatically.
2025-05-13 13:01:01,376 - iptv_agent - INFO - Successfully processed all documents on startup.
2025-05-13 13:01:01,379 - iptv_agent - INFO - Vector store already exists. Setting processed_documents flag to True.
2025-05-13 13:01:08,800 - iptv_client - INFO - Initialized IPTV API client with base URL: https://iptv-org.github.io/api
2025-05-13 13:01:08,802 - iptv_client - INFO - Making request to https://iptv-org.github.io/api/channels.json
2025-05-13 13:01:23,312 - iptv_client - INFO - Limited response to 100 items (total: 37010)
