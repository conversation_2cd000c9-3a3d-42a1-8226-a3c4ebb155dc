import os
import sys
import logging
import traceback
from PyPDF2 import PdfReader
import webvtt
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai
from langchain_community.vectorstores import FAISS
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from datetime import datetime, timezone
import pytz
import requests
import json
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

# Import IPTV tools
from .iptv_tools import (
    iptv_get_channels,
    iptv_get_channel,
    iptv_get_streams,
    iptv_get_categories,
    iptv_get_countries,
    iptv_get_guides,
    iptv_get_current_programs,
    iptv_get_upcoming_programs,
    iptv_search_programs,
    iptv_query,
    IPTV_TOOLS
)
from .utils import limit_response_tokens

# Import central logger
from .logger import get_logger

# Get logger for this module
logger = get_logger('iptv_agent')
logger.info("IPTV Agent logging initialized")

# Load environment variables
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable is not set")
else:
    genai.configure(api_key=GOOGLE_API_KEY)

# Project paths
current_project_path = os.path.abspath(os.path.dirname(__file__))
documents_dir = os.path.join(current_project_path, "documents")
vector_store_path = os.path.join(current_project_path, "faiss_index")
os.makedirs(documents_dir, exist_ok=True)
os.makedirs(os.path.dirname(vector_store_path), exist_ok=True)

# Flag to indicate if documents have been processed
processed_documents = False

def list_files_in_documents_dir() -> dict:
    """
    List all available files in the project's documents directory.
    Returns a dictionary with lists of PDF, VTT, and text files.
    """
    try:
        files = os.listdir(documents_dir)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        vtt_files = [f for f in files if f.lower().endswith('.vtt')]
        text_files = [f for f in files if f.lower().endswith(('.txt', '.md', '.text'))]
        return {
            "documents_dir": documents_dir,
            "pdf_files": pdf_files,
            "vtt_files": vtt_files,
            "text_files": text_files
        }
    except Exception as e:
        return {"error": str(e), "documents_dir": documents_dir}

def get_pdf_text(pdf_path):
    """Extract text from a PDF file"""
    text = ""
    try:
        pdf_reader = PdfReader(pdf_path)
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        return f"Error processing PDF: {str(e)}"

def get_text_file_content(text_path):
    """Read content from a text file"""
    try:
        with open(text_path, 'r', encoding='utf-8') as file:
            text = file.read()
        return text
    except UnicodeDecodeError:
        try:
            with open(text_path, 'r', encoding='latin-1') as file:
                text = file.read()
            return text
        except Exception as e:
            return f"Error processing text file: {str(e)}"
    except Exception as e:
        return f"Error processing text file: {str(e)}"

def get_vtt_text(vtt_path):
    """Extract text from a VTT file"""
    text = ""
    try:
        for caption in webvtt.read(vtt_path):
            text += f"{caption.text}\n"
        return text
    except Exception as e:
        return f"Error processing VTT: {str(e)}"

def get_text_chunks(text):
    """Split text into chunks for processing"""
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
    return text_splitter.split_text(text)

def create_vector_store(text_chunks):
    """Create a vector store from text chunks"""
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
    vector_store.save_local(vector_store_path)
    return "Vector store created successfully"

def process_all_files() -> dict:
    """
    Process all PDF, VTT, and text files in the documents directory automatically.
    Returns a summary of the processing results.
    """
    global processed_documents
    try:
        files = list_files_in_documents_dir()
        pdf_files = files.get("pdf_files", [])
        vtt_files = files.get("vtt_files", [])
        text_files = files.get("text_files", [])
        all_text = ""
        processed_count = 0
        for pdf_file in pdf_files:
            file_path = os.path.join(documents_dir, pdf_file)
            text = get_pdf_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for vtt_file in vtt_files:
            file_path = os.path.join(documents_dir, vtt_file)
            text = get_vtt_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for text_file in text_files:
            file_path = os.path.join(documents_dir, text_file)
            text = get_text_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        if all_text:
            chunks = get_text_chunks(all_text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": f"Processed {processed_count} files and created vector store"}
        else:
            return {"status": "warning", "message": "No text was extracted from any files"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def search_similar_content(query: str, num_results: int = 3) -> dict:
    """
    Search for similar content in the vector store based on the query.
    Returns the most relevant text chunks without processing through QA chain.
    
    Args:
        query (str): The search query
        num_results (int, optional): Number of similar documents to return. Defaults to 3.
    
    Returns:
        dict: Dictionary containing status and search results
    """
    global processed_documents
    try:
        if not processed_documents:
            return {"status": "error", "context": [], "message": "No documents have been processed yet. Please process documents first."}
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        docs = vector_store.similarity_search(query, k=num_results)
        return {"status": "success", "context": [doc.page_content for doc in docs]}
    except Exception as e:
        return {"status": "error", "context": [], "message": f"Error: {str(e)}"}

def process_file(file_path: str) -> dict:
    """
    Process a file (PDF or VTT) from a given file path.
    This can be a local path or a URL.
    
    Returns a dict with status and extracted text.
    """
    global processed_documents
    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            return {"status": "error", "message": f"File not found: {file_path}"}
        
        # Determine file type by extension
        file_extension = os.path.splitext(file_path)[1].lower()
        text = ""
        
        if file_extension == ".pdf":
            text = get_pdf_text(file_path)
        elif file_extension == ".vtt":
            text = get_vtt_text(file_path)
        elif file_extension in [".txt", ".md", ".text"]:
            text = get_text_file_content(file_path)
        else:
            return {"status": "error", "message": f"Unsupported file type: {file_extension}"}
        
        # Check if text was extracted successfully
        if text.startswith("Error"):
            return {"status": "error", "message": text}
        
        # Create text chunks
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        
        return {"status": "success", "message": f"Processed file: {file_path}", "text": text[:500] + "..." if len(text) > 500 else text}
    except Exception as e:
        return {"status": "error", "message": f"Error processing file: {str(e)}"}

def create_vector_store_from_text(text: str) -> dict:
    """
    Process text content into chunks and create a vector store.
    Returns status of the operation.
    """
    global processed_documents
    try:
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        return {"status": "success", "message": "Vector store created from text"}
    except Exception as e:
        return {"status": "error", "message": f"Error creating vector store: {str(e)}"}

def process_and_create_vector_store(file_path: str) -> dict:
    """
    Process a file and create a vector store from its content in one step.
    This is a convenience function that combines process_file and create_vector_store.
    """
    result = process_file(file_path)
    if result.get("status") == "success":
        return {"status": "success", "message": f"Processed file and created vector store: {file_path}"}
    else:
        return result

def auto_process_documents():
    """
    Automatically process documents on startup to ensure the vector store is ready.
    This function checks for existing documents and processes them.
    """
    global processed_documents
    # Check if the vector store already exists
    if os.path.exists(vector_store_path) and os.path.isdir(vector_store_path) and len(os.listdir(vector_store_path)) > 0:
        logger.info("Vector store already exists. Setting processed_documents flag to True.")
        processed_documents = True
        return
    
    # Check if there are documents to process
    files = list_files_in_documents_dir()
    pdf_files = files.get("pdf_files", [])
    vtt_files = files.get("vtt_files", [])
    text_files = files.get("text_files", [])
    
    if pdf_files or vtt_files or text_files:
        logger.info("Found documents to process. Processing them automatically.")
        result = process_all_files()
        if result.get("status") == "success":
            logger.info("Successfully processed all documents on startup.")
        else:
            logger.warning(f"Failed to process documents on startup: {result.get('message')}")
    else:
        logger.info("No documents found in the documents directory.")

# Call the auto-process function on module import
auto_process_documents()

# --- Define Model Constants for easier use ---
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"
MODEL_GEMINI_2_0_PRO = "gemini-2.0-pro"  # Better for complex reasoning
MODEL_GPT_4O = "openai/gpt-4o"
MODEL_CLAUDE_SONNET = "anthropic/claude-3-sonnet-20240229"
MODEL_CLAUDE_OPUS = "anthropic/claude-3-opus-20240229"  # Best for advanced reasoning
MODEL_GROQ_LLAMA_3_8B_8192 = "groq/llama-3.1-8b-instant"

print("\nEnvironment configured.")

# Create single API handler subagent
api_handler = Agent(
    name="api_handler",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Subagent that executes API calls for IPTV services immediately upon receiving control",
    instruction=(
        "You are the API execution specialist. YOUR ONLY JOB is to IMMEDIATELY EXECUTE an API call when you receive control."
        "\n\nWHEN YOU RECEIVE CONTROL FROM ROOT AGENT:"
        "\n1. DO NOT analyze, explain, or discuss - EXECUTE AN API CALL IMMEDIATELY"
        "\n2. Choose the appropriate function based on the user query:"
        "\n   - For channels information → iptv_get_channels()"
        "\n   - For streams information → iptv_get_streams()"
        "\n   - For categories information → iptv_get_categories()"
        "\n   - For countries information → iptv_get_countries()"
        "\n   - For program guide information → iptv_get_guides()"
        "\n   - For currently airing shows → iptv_get_current_programs()"
        "\n   - For upcoming programs → iptv_get_upcoming_programs()"
        "\n   - For searching program content → iptv_search_programs()"
        
        "\n\nHIGHLY IMPORTANT EXECUTION RULES:"
        "\n- Your VERY FIRST action must be to call one of the API functions"
        "\n- DO NOT say what you're going to do, just DO IT"
        "\n- DO NOT discuss or explain your reasoning"
        "\n- DO NOT respond with text before calling the API"
        "\n- CALL THE API FUNCTION DIRECTLY as your first action"
        "\n- ALWAYS include max_results parameter (no default values in functions)"
        
        "\n\nEXAMPLE QUERIES AND REQUIRED ACTIONS:"
        "\n- If query about 'sports channels' → EXECUTE: iptv_get_channels(country=None, category='sports', language=None, max_results=10)"
        "\n- If query about watching 'CNN' → EXECUTE: iptv_get_streams(channel='CNN', quality=None, max_results=5)"
        "\n- If query about categories → EXECUTE: iptv_get_categories(max_results=20)"
        "\n- If query about countries → EXECUTE: iptv_get_countries(max_results=20)"
        "\n- If query about 'what's on CNN' → EXECUTE: iptv_get_guides(channel='CNN', max_results=10)"
        "\n- If query about 'program schedule' → EXECUTE: iptv_get_guides(channel=None, max_results=10)"
        "\n- If query about 'what's showing now' → EXECUTE: iptv_get_current_programs(channel=None, max_results=10)"
        "\n- If query about 'upcoming sports shows' → EXECUTE: iptv_search_programs(query='sports', channel=None, max_results=10)"
        "\n- If query about 'tomorrow's movies' → EXECUTE: iptv_get_upcoming_programs(channel=None, hours_ahead=24, max_results=10)"
        
        "\n\nFORMAT OF PARAMETERS:"
        "\n- For strings: Use quotes (category='sports')"
        "\n- For None values: Use None (country=None)"
        "\n- For numbers: Use plain numbers (max_results=5)"
        "\n- DO NOT rely on default parameter values, always specify all parameters"
        
        "\n\nTOKEN MANAGEMENT:"
        "\n- ALWAYS limit results to maximum 10 items"
        "\n- For queries that might return large datasets, use max_results=5"
        "\n- When showing stream details, limit to 3-5 streams maximum"
        "\n- Include only essential fields in your response"
        "\n- Truncate long descriptions to 100 characters"
        
        "\n\nAFTER RECEIVING API RESULTS:"
        "\n- Format results in a VERY concise table or list"
        "\n- Include maximum 5-10 items to stay within token limits"
        "\n- For large responses, summarize rather than showing all data"
        "\n- Wrap results in [API_RESULT] tags"
        "\n- Total response should be under 2000 tokens"
        
        "\n\nREMINDER: YOUR IMMEDIATE ACTION SHOULD BE TO CALL THE API - NO TEXT BEFORE THIS"
    ),
    tools=[
        iptv_get_channels,
        iptv_get_channel,
        iptv_get_streams,
        iptv_get_categories, 
        iptv_get_countries,
        iptv_get_guides,
        iptv_get_current_programs,
        iptv_get_upcoming_programs,
        iptv_search_programs,
        iptv_query
    ]
)

# Define the root agent with all tools and subagents
iptv_agent = Agent(
    name="iptv_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description=(
        "Root agent that handles user queries about IPTV channels, streams, and program guides"
    ),
    instruction=(
        "You are the main IPTV assistant. When user asks about channels, programs, guides, etc:"
        
        "\n\n1. First check if documents need processing with process_all_files()"
        "\n2. Then IMMEDIATELY transfer to api_handler with the user query:"
        "\n   transfer_to_agent(agent_name='api_handler')"
        
        "\n\nCRITICAL INSTRUCTIONS:"
        "\n- Transfer to api_handler should be your IMMEDIATE action after process_all_files"
        "\n- Do NOT search for documentation or do additional processing"
        "\n- Do NOT explain what you're going to do, just DO it"
        "\n- When transferring, include ONLY the original user question"
        "\n- The api_handler will directly execute the appropriate API call"
        
        "\n\nWHEN YOU RECEIVE THE USER QUESTION:"
        "\n1. Call process_all_files()"
        "\n2. IMMEDIATELY call transfer_to_agent(agent_name='api_handler')"
        "\n3. After receiving results, present them to the user"
        
        "\n\nPOSSIBLE USER QUERIES INCLUDE:"
        "\n- Channels (sports, news, entertainment, etc.)"
        "\n- Streams for specific channels"
        "\n- Categories of content"
        "\n- Countries available"
        "\n- Program guides and schedules"
        "\n- What's currently playing on specific channels"
        "\n- Upcoming shows in the next 24 hours"
        "\n- Searching for specific types of programs"
        "\n- TV listings for a channel"
        
        "\n\nKEY CAPABILITIES:"
        "\n- Channel information (iptv_get_channels)"
        "\n- Streaming URLs (iptv_get_streams)"
        "\n- Program guides (iptv_get_guides)"
        "\n- Currently airing shows (iptv_get_current_programs)"
        "\n- Upcoming programs (iptv_get_upcoming_programs)"
        "\n- Program search by content (iptv_search_programs)"
        
        "\n\nEXAMPLE OF EXACT RESPONSE PATTERN:"
        "\nUser: 'What sports channels are available?'"
        "\nYour first response: [Call process_all_files()]"
        "\nYour next response: [Call transfer_to_agent(agent_name='api_handler')]"
        "\nThen present results from api_handler"
    ),
    tools=[
        # RAG tools
        list_files_in_documents_dir,
        process_all_files,
        process_file,
        create_vector_store_from_text,
        process_and_create_vector_store,
        search_similar_content
    ],
    sub_agents=[api_handler]
) 