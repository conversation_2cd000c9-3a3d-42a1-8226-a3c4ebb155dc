{"pets": [{"id": 1, "name": "<PERSON><PERSON><PERSON>", "status": "available", "category": {"id": 1, "name": "Dogs"}, "photoUrls": ["https://example.com/photos/fluffy1.jpg", "https://example.com/photos/fluffy2.jpg"], "tags": [{"id": 1, "name": "friendly"}, {"id": 4, "name": "small"}]}, {"id": 2, "name": "<PERSON>", "status": "available", "category": {"id": 1, "name": "Dogs"}, "photoUrls": ["https://example.com/photos/rex1.jpg"], "tags": [{"id": 2, "name": "large"}, {"id": 3, "name": "trained"}]}, {"id": 3, "name": "Whiskers", "status": "pending", "category": {"id": 2, "name": "Cats"}, "photoUrls": ["https://example.com/photos/whiskers1.jpg", "https://example.com/photos/whiskers2.jpg"], "tags": [{"id": 1, "name": "friendly"}, {"id": 5, "name": "playful"}]}, {"id": 4, "name": "<PERSON><PERSON>", "status": "available", "category": {"id": 3, "name": "Fish"}, "photoUrls": ["https://example.com/photos/goldie1.jpg"], "tags": [{"id": 6, "name": "colorful"}]}, {"id": 5, "name": "<PERSON>", "status": "sold", "category": {"id": 1, "name": "Dogs"}, "photoUrls": ["https://example.com/photos/buddy1.jpg", "https://example.com/photos/buddy2.jpg"], "tags": [{"id": 3, "name": "trained"}, {"id": 7, "name": "senior"}]}, {"id": 6, "name": "Tweety", "status": "available", "category": {"id": 4, "name": "Birds"}, "photoUrls": ["https://example.com/photos/tweety1.jpg"], "tags": [{"id": 8, "name": "vocal"}, {"id": 9, "name": "exotic"}]}, {"id": 7, "name": "Luna", "status": "pending", "category": {"id": 2, "name": "Cats"}, "photoUrls": ["https://example.com/photos/luna1.jpg"], "tags": [{"id": 5, "name": "playful"}, {"id": 10, "name": "kitten"}]}, {"id": 8, "name": "<PERSON>", "status": "available", "category": {"id": 5, "name": "Rabbits"}, "photoUrls": ["https://example.com/photos/rocky1.jpg", "https://example.com/photos/rocky2.jpg"], "tags": [{"id": 4, "name": "small"}, {"id": 11, "name": "fluffy"}]}, {"id": 9, "name": "Spot", "status": "sold", "category": {"id": 1, "name": "Dogs"}, "photoUrls": ["https://example.com/photos/spot1.jpg"], "tags": [{"id": 1, "name": "friendly"}, {"id": 12, "name": "spotted"}]}, {"id": 10, "name": "Slither", "status": "available", "category": {"id": 6, "name": "Reptiles"}, "photoUrls": ["https://example.com/photos/slither1.jpg", "https://example.com/photos/slither2.jpg"], "tags": [{"id": 9, "name": "exotic"}, {"id": 13, "name": "rare"}]}], "categories": [{"id": 1, "name": "Dogs"}, {"id": 2, "name": "Cats"}, {"id": 3, "name": "Fish"}, {"id": 4, "name": "Birds"}, {"id": 5, "name": "Rabbits"}, {"id": 6, "name": "Reptiles"}, {"id": 7, "name": "Small Pets"}], "tags": [{"id": 1, "name": "friendly"}, {"id": 2, "name": "large"}, {"id": 3, "name": "trained"}, {"id": 4, "name": "small"}, {"id": 5, "name": "playful"}, {"id": 6, "name": "colorful"}, {"id": 7, "name": "senior"}, {"id": 8, "name": "vocal"}, {"id": 9, "name": "exotic"}, {"id": 10, "name": "kitten"}, {"id": 11, "name": "fluffy"}, {"id": 12, "name": "spotted"}, {"id": 13, "name": "rare"}], "photos": [{"id": 1, "pet_id": 1, "url": "https://example.com/photos/fluffy1.jpg", "additionalMetadata": "Main profile photo", "created_at": "2023-01-15T10:30:00Z"}, {"id": 2, "pet_id": 1, "url": "https://example.com/photos/fluffy2.jpg", "additionalMetadata": "Playing in the yard", "created_at": "2023-01-16T14:20:00Z"}, {"id": 3, "pet_id": 2, "url": "https://example.com/photos/rex1.jpg", "additionalMetadata": "Standing tall", "created_at": "2023-01-20T09:15:00Z"}, {"id": 4, "pet_id": 3, "url": "https://example.com/photos/whiskers1.jpg", "additionalMetadata": "Lounging on couch", "created_at": "2023-01-25T16:45:00Z"}, {"id": 5, "pet_id": 3, "url": "https://example.com/photos/whiskers2.jpg", "additionalMetadata": "Playing with yarn", "created_at": "2023-01-26T11:10:00Z"}], "inventory": [{"id": 1, "status": "available", "quantity": 5, "updated_at": "2023-06-01T08:00:00Z"}, {"id": 2, "status": "pending", "quantity": 2, "updated_at": "2023-06-01T08:00:00Z"}, {"id": 3, "status": "sold", "quantity": 3, "updated_at": "2023-06-01T08:00:00Z"}]}