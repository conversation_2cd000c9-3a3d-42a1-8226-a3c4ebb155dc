# SuperSport Agent Refactored Structure

## Directory Structure
```
super_sport_agent/
├── __init__.py              # Package initialization 
├── agents/                  # All agent implementations
│   ├── __init__.py          # Exports the agents
│   ├── root_agent.py        # Main orchestrator agent
│   ├── api_agent.py         # Agent for API operations
│   ├── document_agent.py    # Agent for document operations
│   └── formatter_agent.py   # Agent for response formatting
├── tools/                   # Tool implementations
│   ├── __init__.py          # Tool exports
│   ├── api_tools.py         # API related tools
│   ├── document_tools.py    # Document processing tools
│   └── search_tools.py      # Search related tools
├── services/                # Service layer
│   ├── __init__.py
│   ├── document_service.py  # Document processing service
│   ├── vector_service.py    # Vector store services
│   └── api_service.py       # API client service
├── models/                  # Data models
│   ├── __init__.py
│   ├── api_models.py        # API request/response models
│   └── agent_models.py      # Agent-specific models
├── config/                  # Configuration
│   ├── __init__.py
│   └── settings.py          # Configuration settings
├── utils/                   # Utilities
│   ├── __init__.py
│   └── logger.py            # Logging utility
├── documents/               # API documentation files
└── faiss_index/             # Vector store for RAG functionality
```

## Core Principles of Refactoring

1. **Separation of Concerns**
   - Each module has a clear responsibility
   - No overlapping functionalities between modules

2. **No Hardcoded Logic**
   - Business logic moved to tools or subagents
   - Configuration parameters in settings file

3. **Clear Agent Hierarchy**
   - Root agent coordinates other agents
   - Each subagent has specialized capabilities
   - Clear communication between agents

4. **Modular Tool Design**
   - Tools defined separately from agents
   - Tools can be shared across agents
   - Tools have clear input/output contracts

5. **Service Layer**
   - Services provide business logic
   - Agents use services through tools
   - Services implement core functionality

## Agent Responsibilities

### Root Agent
- Orchestrates the overall workflow
- Determines which subagent to call
- Aggregates and presents final results

### API Agent
- Handles all API interactions
- Determines which API endpoint to call
- Formats API requests and processes responses

### Document Agent
- Processes document files
- Manages vector store operations
- Performs semantic search on documents

### Formatter Agent
- Formats responses for presentation
- Ensures consistent output format
- Applies user preferences to responses

## Implementation Plan

1. Create the directory structure
2. Move models to their respective files
3. Implement the service layer
4. Create the tools using the services
5. Implement the agents using the tools
6. Update the package exports
7. Remove obsolete code 