"""
Configuration settings for the SuperSport Agent
"""

import os
import logging
from pathlib import Path

# Base project paths
PROJECT_ROOT = Path(__file__).parent.parent.absolute()
LOGS_DIR = PROJECT_ROOT / "logs"

# Ensure directories exist
LOGS_DIR.mkdir(exist_ok=True)

# LLM settings
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")  # WARNING: You must set this environment variable
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"
MODEL_GROQ_LLAMA_3_8B_8192 = "groq/llama-3.1-8b-instant"

# Neo4j settings - get from environment or use defaults for local development
NEO4J_URI = os.getenv("NEO4J_URI", "neo4j+s://8d1df2d2.databases.neo4j.io")
NEO4J_USERNAME = os.getenv("NEO4J_USERNAME", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "do5LmmTGz4Uhx02MgjWRtmN4dERjfEVKbqsCdeABzek")
NEO4J_DATABASE = os.getenv("NEO4J_DATABASE", "neo4j")

# Logging settings
LOG_FILE = LOGS_DIR / "super_sport.log"
LOG_LEVEL = logging.DEBUG
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s' 