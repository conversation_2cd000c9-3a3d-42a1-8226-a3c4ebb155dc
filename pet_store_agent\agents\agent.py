"""
Agent for Pet Store Knowledge Graph
"""

import google.generativeai as genai
from google.adk.agents import Agent
from typing import Dict, Any

from ..utils.logger import get_logger
from ..config.settings import MODEL_GEMINI_2_0_FLASH, GOOGLE_API_KEY
from ..services.knowledge_graph_service import KnowledgeGraphService

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI with API key
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY not set at agent level. Agent functionality will not work properly.")

# Create the knowledge graph service for use across the agent
knowledge_graph_service = KnowledgeGraphService()

def query_graph(question: str) -> Dict[str, Any]:
    """
    Query the knowledge graph using Lang<PERSON><PERSON><PERSON>'s GraphCypherQAChain

    Args:
        question: The question to ask about Pet Store data

    Returns:
        Dict containing the answer and query details
    """
    return knowledge_graph_service.query_knowledge_graph(question)

# Create a single agent for Pet Store
pet_store_agent = Agent(
    name="pet_store_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    # model=LiteLlm(model=MODEL_GROQ_LLAMA_3_8B_8192),
    description="Pet Store assistant that answers questions using the knowledge graph",
    instruction=(
        "You are a Pet Store assistant. For every user question, always call query_graph() and return the response from the knowledge graph in natural language."

        "\n\nYour process:"
        "\n1. Always call query_graph() with the user's question"
        "\n2. Return the answer from the knowledge graph in a natural, conversational way"
        "\n3. If the knowledge graph doesn't have information, say so clearly"
    ),
    tools=[query_graph]
)

# Export the agent for ADK compatibility
agent = pet_store_agent