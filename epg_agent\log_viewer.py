#!/usr/bin/env python3
"""
Log Viewer for EPG Agent
A utility to view and filter the logs generated by the EPG Agent components.
"""

import os
import sys
import argparse
from datetime import datetime

def list_log_files():
    """List all log files in the logs directory"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    logs_dir = os.path.join(current_dir, "logs")
    
    if not os.path.exists(logs_dir):
        print(f"Logs directory does not exist: {logs_dir}")
        return []
    
    log_files = [f for f in os.listdir(logs_dir) if f.endswith('.log')]
    log_files.sort(reverse=True)  # Sort with newest first
    
    return [os.path.join(logs_dir, log_file) for log_file in log_files]

def print_log_files():
    """Print a list of available log files"""
    log_files = list_log_files()
    
    if not log_files:
        print("No log files found.")
        return
    
    print(f"Found {len(log_files)} log files:")
    for i, log_file in enumerate(log_files):
        filename = os.path.basename(log_file)
        size = os.path.getsize(log_file) // 1024  # Size in KB
        mtime = datetime.fromtimestamp(os.path.getmtime(log_file))
        
        print(f"{i+1}. {filename} - {size} KB - Last modified: {mtime.strftime('%Y-%m-%d %H:%M:%S')}")

def view_log(log_file, filter_str=None, last_lines=None):
    """View the contents of a log file with optional filtering"""
    if not os.path.exists(log_file):
        print(f"Log file does not exist: {log_file}")
        return
    
    with open(log_file, 'r') as file:
        lines = file.readlines()
    
    if last_lines and last_lines > 0:
        lines = lines[-last_lines:]
    
    if filter_str:
        lines = [line for line in lines if filter_str in line]
    
    if not lines:
        print("No matching log entries found.")
        return
    
    for line in lines:
        print(line.strip())

def main():
    """Main function"""
    parser = argparse.ArgumentParser(description="EPG Agent Log Viewer")
    parser.add_argument("--list", action="store_true", help="List available log files")
    parser.add_argument("--file", type=str, help="View a specific log file by name or index")
    parser.add_argument("--filter", type=str, help="Filter log entries containing this string")
    parser.add_argument("--last", type=int, help="Show only the last N lines")
    parser.add_argument("--latest", action="store_true", help="View the most recent log file")
    
    args = parser.parse_args()
    
    # If no arguments provided, default to listing the log files
    if len(sys.argv) == 1:
        args.list = True
    
    if args.list:
        print_log_files()
        return
    
    log_files = list_log_files()
    if not log_files:
        print("No log files found.")
        return
    
    if args.latest:
        log_file = log_files[0]  # First file (newest)
    elif args.file:
        # Try to interpret as index
        try:
            index = int(args.file) - 1
            if 0 <= index < len(log_files):
                log_file = log_files[index]
            else:
                # Try as filename
                for file in log_files:
                    if args.file in file:
                        log_file = file
                        break
                else:
                    print(f"Log file not found: {args.file}")
                    return
        except ValueError:
            # Not an integer, try as filename
            for file in log_files:
                if args.file in file:
                    log_file = file
                    break
            else:
                print(f"Log file not found: {args.file}")
                return
    else:
        log_file = log_files[0]  # Default to most recent
    
    print(f"\nViewing log file: {os.path.basename(log_file)}")
    if args.filter:
        print(f"Filtering for: '{args.filter}'")
    if args.last:
        print(f"Showing last {args.last} lines")
    print("-" * 80)
    
    view_log(log_file, args.filter, args.last)

if __name__ == "__main__":
    main() 