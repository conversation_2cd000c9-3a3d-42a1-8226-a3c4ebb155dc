"""
Search tools for SuperSport Agent
"""

import re
from typing import Dict, Any, Optional, List, Callable, Union
import traceback
from urllib.parse import urlparse, parse_qs
from datetime import datetime, timedelta

from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate

from ..utils.logger import get_logger
from ..services.vector_service import VectorService
from ..services.document_service import DocumentService
from ..config.settings import MODEL_GEMINI_PRO
from ..models.agent_models import VectorSearchResult, QuestionAnswerResult
from .api_tools import super_sport_get_tv_guide, super_sport_query

# Get logger for this module
logger = get_logger(__name__)

# Singleton services
_vector_service = None
_document_service = None

def get_vector_service() -> VectorService:
    """Get or create the singleton vector service"""
    global _vector_service
    if _vector_service is None:
        _vector_service = VectorService()
    return _vector_service

def get_document_service() -> DocumentService:
    """Get or create the singleton document service"""
    global _document_service
    if _document_service is None:
        _document_service = DocumentService()
    return _document_service

def create_vector_store_from_text(text: str) -> Dict[str, Any]:
    """
    Process text content into chunks and create a vector store.
    
    Args:
        text: Text content to process
        
    Returns:
        Dictionary with status of the operation
    """
    try:
        vector_service = get_vector_service()
        result = vector_service.create_from_text(text)
        return result.dict()
    except Exception as e:
        logger.error(f"Error in create_vector_store_from_text: {str(e)}")
        return {
            "status": "error",
            "message": f"Error creating vector store: {str(e)}",
            "query": ""
        }

def search_similar_content(query: str, num_results: Optional[int] = None) -> Dict[str, Any]:
    """
    Search for similar content in the vector store based on the query.
    If vector store doesn't exist, automatically process all documents first.
    
    Args:
        query: The search query
        num_results: Number of similar documents to return (default: 3)
        
    Returns:
        Dictionary containing search results
    """
    try:
        if num_results is None:
            num_results = 3
            
        vector_service = get_vector_service()
        vector_store = vector_service.get_vector_store()
        
        # Auto process documents if vector store doesn't exist
        if not vector_store:
            logger.info("Vector store not found. Automatically processing all documents...")
            document_service = get_document_service()
            process_result = document_service.process_all_files()
            
            if process_result.status != "success" or not hasattr(process_result, 'content') or not process_result.content:
                logger.warning("Failed to process documents automatically")
                return {
                    "status": "warning",
                    "message": "No documents processed. Please check if documents exist in the documents directory.",
                    "query": query,
                    "total_results": 0
                }
            
            # Create vector store from processed content
            vector_result = vector_service.create_from_text(process_result.content)
            if vector_result.status != "success":
                logger.warning("Failed to create vector store")
                return {
                    "status": "warning",
                    "message": "Documents processed but vector store creation failed.",
                    "query": query,
                    "total_results": 0
                }
        
        # Now perform the search with the vector store (original or newly created)
        result = vector_service.search_similar_content(query, num_results)
        return result.dict()
    except Exception as e:
        logger.error(f"Error in search_similar_content: {str(e)}")
        return {
            "status": "error",
            "message": f"Error searching for content: {str(e)}",
            "query": query,
            "total_results": 0
        }

def ask_question(question: str) -> Dict[str, Any]:
    """
    Ask a question about the processed documents.
    
    Args:
        question: The question to ask
        
    Returns:
        Dictionary with answer and context
    """
    try:
        # Auto process documents if they haven't been processed yet
        vector_service = get_vector_service()
        vector_store = vector_service.get_vector_store()
        
        if not vector_store:
            # Try to process all documents if vector store doesn't exist
            logger.info("Vector store not found. Attempting to process documents...")
            document_service = DocumentService()
            process_result = document_service.process_all_files()
            
            if process_result.status != "success" or not hasattr(process_result, 'content') or not process_result.content:
                logger.warning("Failed to process documents automatically")
                return {
                    "status": "error",
                    "question": question,
                    "answer": "I don't have any processed documents to work with. Please process the documents first."
                }
            
            # Create vector store from processed content
            vector_result = vector_service.create_from_text(process_result.content)
            if vector_result.status != "success":
                logger.warning("Failed to create vector store")
                return {
                    "status": "error",
                    "question": question,
                    "answer": "I processed the documents but couldn't create a vector store. Please try again."
                }
                
            # Refresh the vector store reference
            vector_store = vector_service.get_vector_store()
            if not vector_store:
                logger.error("Vector store still not available after processing")
                return {
                    "status": "error",
                    "question": question,
                    "answer": "I couldn't create the necessary vector store. Please check the logs."
                }
        
        # Search for relevant content
        search_result = vector_service.search_similar_content(question)
        
        if search_result.status != "success" or not search_result.context:
            logger.warning(f"No relevant content found for question: {question}")
            return {
                "status": "warning",
                "question": question,
                "answer": "I couldn't find any relevant information to answer your question."
            }
        
        # Join context data
        context = "\n\n".join(search_result.context)
        
        # Set up QA chain
        template = """
        You are an assistant for question-answering on the SuperSport API.
        Use the following context to answer the question.
        If you don't know the answer, say that you don't know and ask the user to provide more details.
        
        CONTEXT:
        {context}
        
        QUESTION:
        {question}
        
        When responding, also identify any SuperSport API endpoints that would be relevant for this query.
        Use the format 'METHOD URL' (e.g., 'GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za').
        Include any relevant query parameters.
        Substitute placeholders like CURRENT_DATE with real dates. Today is {date}.
        
        Your answer should be comprehensive but focused on the question.
        """
        
        QA_PROMPT = PromptTemplate(
            template=template,
            input_variables=["context", "question", "date"]
        )
        
        llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_PRO)
        chain = load_qa_chain(llm, chain_type="stuff", prompt=QA_PROMPT)
        
        # Get today's date for the prompt
        today = datetime.now().strftime("%Y-%m-%d")
        
        # Run the chain
        response = chain(
            {"input_documents": [{"page_content": context}], "question": question, "date": today},
            return_only_outputs=True
        )
        
        # Extract API endpoints if they exist
        api_endpoints = []
        api_pattern = r'(GET|POST|PUT|DELETE)\s+(https?://[^\s]+)'
        matches = re.findall(api_pattern, response["output_text"])
        
        for method, url in matches:
            api_endpoints.append({"method": method, "url": url})
        
        # Create response
        result = QuestionAnswerResult(
            status="success",
            question=question,
            answer=response["output_text"],
            api_endpoints=api_endpoints,
            context=search_result.context
        )
        
        return result.dict()
    
    except Exception as e:
        logger.error(f"Error in ask_question: {str(e)}\n{traceback.format_exc()}")
        return {
            "status": "error",
            "question": question,
            "answer": f"An error occurred while processing your question: {str(e)}"
        }

def execute_local_api_call(api_response: str) -> Dict[str, Any]:
    """
    Execute a local API call based on the response from the LLM.
    
    Args:
        api_response: The LLM response containing the endpoint URL
        
    Returns:
        Dictionary with the API response
    """
    try:
        # Look for API endpoint pattern in the answer
        api_pattern = r'(GET|POST|PUT|DELETE)\s+(https?://[^\s]+)'
        api_matches = re.findall(api_pattern, api_response)
        
        if not api_matches:
            return {
                "status": "error", 
                "message": "No API endpoint found in the response", 
                "data": None
            }
        
        # Use the first matched API endpoint
        method, url = api_matches[0]
        
        # Extract parameters from the URL query string
        parsed_url = urlparse(url)
        params = parse_qs(parsed_url.query)
        
        # Convert lists to single values for simpler processing
        for key, value in params.items():
            if isinstance(value, list) and len(value) == 1:
                params[key] = value[0]
        
        # Handle special date parameters - replace placeholder values with actual dates
        for key in params:
            if isinstance(params[key], str):
                # Handle CURRENT_DATE placeholder
                if params[key] == "CURRENT_DATE":
                    params[key] = datetime.now().strftime("%Y-%m-%d")
                # Handle date addition placeholders like CURRENT_DATE+7 or CURRENT_DATE+14
                elif params[key].startswith("CURRENT_DATE+"):
                    try:
                        days_to_add = int(params[key].split("+")[1])
                        params[key] = (datetime.now() + timedelta(days=days_to_add)).strftime("%Y-%m-%d")
                    except (ValueError, IndexError):
                        # If parsing fails, keep original value
                        logger.warning(f"Could not parse date expression: {params[key]}")
        
        # Determine which SuperSport API to call based on the URL path
        path = parsed_url.path.lower()
        
        if "tvguide" in path:
            # Set default parameters (with today's date)
            today = datetime.now().strftime("%Y-%m-%d")
            country_code = params.get("countryCode", "za")
            start_date_time = params.get("startDateTime", today)
            end_date_time = params.get("endDateTime", today)
            channel_only = params.get("channelOnly", "false").lower() == "true"
            live_only = params.get("liveOnly", "false").lower() == "true"
            
            logger.info(f"Calling super_sport_get_tv_guide with: country_code={country_code}, " +
                      f"start_date_time={start_date_time}, end_date_time={end_date_time}, " +
                      f"channel_only={channel_only}, live_only={live_only}")
            
            # Call the API
            return super_sport_get_tv_guide(
                country_code=country_code,
                start_date_time=start_date_time,
                end_date_time=end_date_time,
                channel_only=channel_only,
                live_only=live_only,
                query=params.get("query")
            )
        else:
            # Default to generic query if path is not recognized
            query = f"API call to {url}"
            return super_sport_query(query=query)
            
    except Exception as e:
        logger.error(f"Error executing API call: {str(e)}\n{traceback.format_exc()}")
        return {
            "status": "error",
            "message": f"Error executing API call: {str(e)}",
            "data": None
        }

# List of search tools for easy reference
SEARCH_TOOLS: List[Callable] = [
    create_vector_store_from_text,
    search_similar_content,
    ask_question,
    execute_local_api_call
] 