import os
import sys
import logging
import traceback
from PyPDF2 import PdfReader
import webvtt
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain_google_genai import GoogleGenerativeAIEmbeddings
import google.generativeai as genai
from langchain_community.vectorstores import FAISS
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain.chains.question_answering import load_qa_chain
from langchain.prompts import PromptTemplate
from datetime import datetime, timezone
import pytz
import requests
import json
from google.adk.agents import Agent
from google.adk.models.lite_llm import LiteLlm

# Import IPTV tools
from .iptv_tools import (
    iptv_get_channels,
    iptv_get_channel,
    iptv_get_streams,
    iptv_get_categories,
    iptv_get_countries,
    iptv_get_guides,
    iptv_get_current_programs,
    iptv_get_upcoming_programs,
    iptv_search_programs,
    iptv_query,
    IPTV_TOOLS
)
from .utils import limit_response_tokens

# Import central logger
from .logger import get_logger

# Get logger for this module
logger = get_logger('iptv_agent')
logger.info("IPTV Agent logging initialized")

# Load environment variables
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
CLAUDE_API_KEY = os.getenv("CLAUDE_API_KEY")
GROQ_API_KEY = os.getenv("GROQ_API_KEY")
if not GOOGLE_API_KEY:
    logger.warning("GOOGLE_API_KEY environment variable is not set")
else:
    genai.configure(api_key=GOOGLE_API_KEY)

# Project paths
current_project_path = os.path.abspath(os.path.dirname(__file__))
documents_dir = os.path.join(current_project_path, "documents")
vector_store_path = os.path.join(current_project_path, "faiss_index")
os.makedirs(documents_dir, exist_ok=True)
os.makedirs(os.path.dirname(vector_store_path), exist_ok=True)

# Flag to indicate if documents have been processed
processed_documents = False

def list_files_in_documents_dir() -> dict:
    """
    List all available files in the project's documents directory.
    Returns a dictionary with lists of PDF, VTT, markdown, and text files.
    """
    try:
        files = os.listdir(documents_dir)
        pdf_files = [f for f in files if f.lower().endswith('.pdf')]
        vtt_files = [f for f in files if f.lower().endswith('.vtt')]
        text_files = [f for f in files if f.lower().endswith(('.txt', '.text'))]
        markdown_files = [f for f in files if f.lower().endswith('.md')]
        return {
            "documents_dir": documents_dir,
            "pdf_files": pdf_files,
            "vtt_files": vtt_files,
            "text_files": text_files,
            "markdown_files": markdown_files
        }
    except Exception as e:
        return {"error": str(e), "documents_dir": documents_dir}

def get_pdf_text(pdf_path):
    """Extract text from a PDF file"""
    text = ""
    try:
        pdf_reader = PdfReader(pdf_path)
        for page in pdf_reader.pages:
            text += page.extract_text()
        return text
    except Exception as e:
        return f"Error processing PDF: {str(e)}"

def get_markdown_file_content(md_path):
    """Read content from a markdown file"""
    try:
        with open(md_path, 'r', encoding='utf-8') as file:
            text = file.read()
        return text
    except UnicodeDecodeError:
        try:
            with open(md_path, 'r', encoding='latin-1') as file:
                text = file.read()
            return text
        except Exception as e:
            return f"Error processing markdown file: {str(e)}"
    except Exception as e:
        return f"Error processing markdown file: {str(e)}"

def get_text_file_content(text_path):
    """Read content from a text file"""
    try:
        with open(text_path, 'r', encoding='utf-8') as file:
            text = file.read()
        return text
    except UnicodeDecodeError:
        try:
            with open(text_path, 'r', encoding='latin-1') as file:
                text = file.read()
            return text
        except Exception as e:
            return f"Error processing text file: {str(e)}"
    except Exception as e:
        return f"Error processing text file: {str(e)}"

def get_vtt_text(vtt_path):
    """Extract text from a VTT file"""
    text = ""
    try:
        for caption in webvtt.read(vtt_path):
            text += f"{caption.text}\n"
        return text
    except Exception as e:
        return f"Error processing VTT: {str(e)}"

def get_text_chunks(text):
    """Split text into chunks for processing"""
    text_splitter = RecursiveCharacterTextSplitter(chunk_size=10000, chunk_overlap=1000)
    return text_splitter.split_text(text)

def create_vector_store(text_chunks):
    """Create a vector store from text chunks"""
    embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
    vector_store = FAISS.from_texts(text_chunks, embedding=embeddings)
    vector_store.save_local(vector_store_path)
    return "Vector store created successfully"

def process_all_files() -> dict:
    """
    Process all PDF, VTT, and text files in the documents directory automatically.
    Returns a summary of the processing results.
    """
    global processed_documents
    try:
        files = list_files_in_documents_dir()
        pdf_files = files.get("pdf_files", [])
        vtt_files = files.get("vtt_files", [])
        text_files = files.get("text_files", [])
        markdown_files = files.get("markdown_files", [])
        all_text = ""
        processed_count = 0
        for pdf_file in pdf_files:
            file_path = os.path.join(documents_dir, pdf_file)
            text = get_pdf_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for vtt_file in vtt_files:
            file_path = os.path.join(documents_dir, vtt_file)
            text = get_vtt_text(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for text_file in text_files:
            file_path = os.path.join(documents_dir, text_file)
            text = get_text_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        for md_file in markdown_files:
            file_path = os.path.join(documents_dir, md_file)
            text = get_markdown_file_content(file_path)
            if not text.startswith("Error"):
                all_text += text + "\n\n"
                processed_count += 1
        if all_text:
            chunks = get_text_chunks(all_text)
            create_vector_store(chunks)
            processed_documents = True
            return {"status": "success", "message": f"Processed {processed_count} files and created vector store"}
        else:
            return {"status": "warning", "message": "No text was extracted from any files"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def search_similar_content(query: str, num_results: int = 3) -> dict:
    """
    Search for similar content in the vector store based on the query.
    Returns the most relevant text chunks without processing through QA chain.
    
    Args:
        query (str): The search query
        num_results (int, optional): Number of similar documents to return. Defaults to 3.
    
    Returns:
        dict: Dictionary containing status and search results
    """
    global processed_documents
    try:
        if not processed_documents:
            return {"status": "error", "context": [], "message": "No documents have been processed yet. Please process documents first."}
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        docs = vector_store.similarity_search(query, k=num_results)
        return {"status": "success", "context": [doc.page_content for doc in docs]}
    except Exception as e:
        return {"status": "error", "context": [], "message": f"Error: {str(e)}"}

def process_file(file_path: str) -> dict:
    """
    Process a file (PDF, VTT, markdown, or text) from a given file path.
    This can be a local path or a URL.
    
    Returns a dict with status and extracted text.
    """
    global processed_documents
    try:
        # Check if the file exists
        if not os.path.exists(file_path):
            return {"status": "error", "message": f"File not found: {file_path}"}
        
        # Determine file type by extension
        file_extension = os.path.splitext(file_path)[1].lower()
        text = ""
        
        if file_extension == ".pdf":
            text = get_pdf_text(file_path)
        elif file_extension == ".vtt":
            text = get_vtt_text(file_path)
        elif file_extension == ".md":
            text = get_markdown_file_content(file_path)
        elif file_extension in [".txt", ".text"]:
            text = get_text_file_content(file_path)
        else:
            return {"status": "error", "message": f"Unsupported file type: {file_extension}"}
        
        # Check if text was extracted successfully
        if text.startswith("Error"):
            return {"status": "error", "message": text}
        
        # Create text chunks
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        
        return {"status": "success", "message": f"Processed file: {file_path}", "text": text[:500] + "..." if len(text) > 500 else text}
    except Exception as e:
        return {"status": "error", "message": f"Error processing file: {str(e)}"}

def create_vector_store_from_text(text: str) -> dict:
    """
    Process text content into chunks and create a vector store.
    Returns status of the operation.
    """
    global processed_documents
    try:
        chunks = get_text_chunks(text)
        create_vector_store(chunks)
        processed_documents = True
        return {"status": "success", "message": "Vector store created from text"}
    except Exception as e:
        return {"status": "error", "message": f"Error creating vector store: {str(e)}"}

def process_and_create_vector_store(file_path: str) -> dict:
    """
    Process a file and create a vector store from its content in one step.
    This is a convenience function that combines process_file and create_vector_store.
    """
    result = process_file(file_path)
    if result.get("status") == "success":
        return {"status": "success", "message": f"Processed file and created vector store: {file_path}"}
    else:
        return result

def auto_process_documents():
    """
    Automatically process documents on startup to ensure the vector store is ready.
    This function checks for existing documents and processes them.
    """
    global processed_documents
    # Check if the vector store already exists
    if os.path.exists(vector_store_path) and os.path.isdir(vector_store_path) and len(os.listdir(vector_store_path)) > 0:
        logger.info("Vector store already exists. Setting processed_documents flag to True.")
        processed_documents = True
        return
    
    # Check if there are documents to process
    files = list_files_in_documents_dir()
    pdf_files = files.get("pdf_files", [])
    vtt_files = files.get("vtt_files", [])
    text_files = files.get("text_files", [])
    markdown_files = files.get("markdown_files", [])
    
    if pdf_files or vtt_files or text_files or markdown_files:
        logger.info("Found documents to process. Processing them automatically.")
        result = process_all_files()
        if result.get("status") == "success":
            logger.info("Successfully processed all documents on startup.")
        else:
            logger.warning(f"Failed to process documents on startup: {result.get('message')}")
    else:
        logger.info("No documents found in the documents directory.")

# Call the auto-process function on module import
auto_process_documents()

# --- Define Model Constants for easier use ---
MODEL_GEMINI_2_0_FLASH = "gemini-2.0-flash"
MODEL_GEMINI_2_0_PRO = "gemini-2.0-pro"  # Better for complex reasoning
MODEL_GPT_4O = "openai/gpt-4o"
MODEL_CLAUDE_SONNET = "anthropic/claude-3-sonnet-20240229"
MODEL_CLAUDE_OPUS = "anthropic/claude-3-opus-20240229"  # Best for advanced reasoning
MODEL_GROQ_LLAMA_3_8B_8192 = "groq/llama-3.1-8b-instant"

print("\nEnvironment configured.")

# ---- Specialized Subagents ----

# 1. API Handler - Only executes API calls
api_handler = Agent(
    name="api_handler",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Executes API calls for IPTV services with intelligent vector-based filtering",
    instruction=(
        "You ONLY execute API calls when requested. Do not explain, just execute."
        "\n1. Choose the right IPTV function based on the query"
        "\n2. Call the function with appropriate parameters"
        "\n3. IMPORTANT: ALWAYS pass the original user query as the 'query' parameter to enable intelligent filtering"
        "\n4. Return the filtered API response"
        
        "\n\nADVANCED VECTOR-BASED FILTERING:"
        "\nThe system now uses vector search from the document database to extract filtering criteria."
        "\nThis means:"
        "\n1. You MUST always pass the original query to the API tools through the 'query' parameter"
        "\n2. The backend will:"
        "\n   - Use vector search to find relevant context in the knowledge base"
        "\n   - Extract precise filtering criteria based on the query and context"
        "\n   - Apply these filters to the API response"
        "\n   - Return a filtered, relevant response"
        
        "\n\nEXAMPLES:"
        "\n- iptv_get_channels(country=None, category=None, language=None, max_results=10, query=user_query)"
        "\n- iptv_get_streams(channel=None, quality=None, query=user_query)"
        "\n- iptv_get_guides(channel=None, max_results=10, query=user_query)"
        
        "\n\nIMPORTANT NOTES:"
        "\n1. Don't try to analyze or extract filter options yourself"
        "\n2. Let the backend handle the extraction using vector search"
        "\n3. Just pass the complete user query to enable the system to do the filtering"
        "\n4. You can still set specific filter parameters if explicitly mentioned in the query"
    ),
    tools=[
        iptv_get_channels,
        iptv_get_channel,
        iptv_get_streams,
        iptv_get_categories, 
        iptv_get_countries,
        iptv_get_guides,
        iptv_get_current_programs,
        iptv_get_upcoming_programs,
        iptv_search_programs,
        iptv_query,
        search_similar_content
    ]
)

# 2. Results Processor - Filters and processes large API responses 
results_processor = Agent(
    name="results_processor",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Processes and formats API responses for presentation",
    instruction=(
        "You process and format pre-filtered API responses for clear presentation:"
        "\n1. Analyze the API response which has already been filtered at the API level"
        "\n2. Note any 'filter_applied' or 'original_count' fields which indicate automatic filtering"
        "\n3. Format the results for clear presentation"
        "\n4. Create appropriate information groupings"
        "\n5. Organize data in a logical structure"
        "\n6. Return a well-formatted response ready for presentation"
        
        "\n\nHANDLING PRE-FILTERED RESPONSES:"
        "\n- The API tools now perform intelligent filtering before returning results"
        "\n- Look for metadata like 'filter_applied' and 'original_count' to understand what filtering was applied"
        "\n- If response already contains only a few results, focus on formatting rather than further filtering"
        "\n- For still-large datasets, apply additional organization or summarization as needed"
        
        "\n\nFORMATTING GUIDELINES:"
        "\n- Use tables or bulleted lists for structured data"
        "\n- Group related information together"
        "\n- Include summary statistics (e.g., 'Showing 5 of 157 total channels')"
        "\n- Format timestamps in a user-friendly way"
        "\n- Highlight key information"
        
        "\n\nDon't add unnecessary explanations - focus on organizing and presenting the data clearly"
    ),
    tools=[
        search_similar_content
    ]
)

# 3. Guide Specialist - Handles TV program guides specifically
guide_specialist = Agent(
    name="guide_specialist",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Specialized in processing TV program guide data",
    instruction=(
        "You specialize in handling TV program guide data:"
        "\n1. Process guide data from iptv_get_guides, iptv_get_current_programs, or iptv_get_upcoming_programs"
        "\n2. Filter guide listings based on time, channel, category, etc."
        "\n3. Format program listings in a readable format"
        "\n4. Highlight currently airing shows vs. upcoming shows"
        "\n5. Organize listings by time or channel"
        "\n6. Return well-formatted, relevant guide information"
    ),
    tools=[
        iptv_get_guides,
        iptv_get_current_programs,
        iptv_get_upcoming_programs,
        iptv_search_programs
    ]
)

# 4. Document Manager - Handles processing and searching documents
document_manager = Agent(
    name="document_manager",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Manages document processing and vectorized search for knowledge and filter extraction",
    instruction=(
        "You specialize in document processing and vectorized search:"
        "\n1. Process PDF, VTT, markdown, and text files"
        "\n2. Create and manage vector stores"
        "\n3. Perform semantic searches on processed documents"
        "\n4. Extract filtering criteria from vector store for IPTV API queries"
        "\n5. Return well-formatted search results"
        
        "\n\nKEY RESPONSIBILITIES:"
        "\n- When a user asks about IPTV data, use search_similar_content() to find relevant information"
        "\n- Extract filtering criteria from vectorized documents to optimize API filtering"
        "\n- Help determine the most relevant API endpoint and filter parameters"
        "\n- Process new documents as needed to keep knowledge base updated"
        
        "\n\nEXTRACTING FILTER CRITERIA:"
        "\n1. Use search_similar_content() with the user query to find relevant context"
        "\n2. Analyze the context to identify appropriate filtering parameters (country, category, etc.)"
        "\n3. Return these parameters for use in API calls"
        "\n4. Always include the original documents context to help LLM make better filtering decisions"
        
        "\n\nWhen working with other agents:"
        "\n1. When api_handler needs filtering criteria, provide the vector search results"
        "\n2. When results_processor needs additional context, provide relevant document excerpts"
        "\n3. Collaborate with other agents by sharing vector search findings"
    ),
    tools=[
        list_files_in_documents_dir,
        process_all_files,
        process_file,
        create_vector_store_from_text,
        process_and_create_vector_store,
        search_similar_content
    ]
)

# 5. Response Formatter - Handles final user response formatting
response_formatter = Agent(
    name="response_formatter",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Formats final responses for users in a clean, readable way",
    instruction=(
        "You create well-formatted, concise responses from processed data:"
        "\n1. Take processed data from other agents"
        "\n2. Format it in a clear, readable way"
        "\n3. Use appropriate formatting (tables, lists, etc.)"
        "\n4. Ensure responses are concise and focused"
        "\n5. Remove any excessive explanations or redundant information"
        "\n6. Return a polished, user-friendly response"
    )
)

# Root agent coordinates all subagents
iptv_agent = Agent(
    name="iptv_agent",
    model=MODEL_GEMINI_2_0_FLASH,
    description="Orchestrates IPTV information retrieval with vector-based filtering",
    instruction=(
        "You are the orchestrator of the IPTV assistant system. Your role is to:"
        
        "\n\n1. Analyze the user query to determine what information they need"
        "\n2. Coordinate the specialized subagents in the optimal sequence"
        
        "\n\nVECTOR-BASED FILTERING WORKFLOW:"
        "\nThe system now uses a specialized workflow for optimal filtering:"
        "\n1. First, use document_manager to search for relevant context in the vector database"
        "\n2. Pass the original user query AND this context to api_handler"
        "\n3. The backend system will automatically:"
        "\n   - Extract precise filtering criteria from the query and vector context"
        "\n   - Apply these filters to reduce API response size"
        "\n   - Return only the most relevant data"
        "\n4. Send filtered results to results_processor for formatting and presentation"
        
        "\n\nOPTIMAL AGENT SEQUENCE:"
        "\nFor most IPTV data queries, use this sequence:"
        "\n1. document_manager → Search vector DB for relevant context"
        "\n2. api_handler → Make API call with user query (filtering done automatically in backend)"
        "\n3. results_processor → Format filtered results"
        "\n4. response_formatter → Create final user response"
        
        "\n\nADDITIONAL INSTRUCTIONS:"
        "\n- For TV guide queries, include guide_specialist in the workflow after api_handler"
        "\n- For document-only queries, use only document_manager and response_formatter"
        "\n- When transferring between agents, always include the original user query"
        "\n- For api_handler, always pass the complete original query to enable vector-based filtering"
        
        "\n\nWhen transferring between agents:"
        "\n1. Include the original user query for context"
        "\n2. Include vector search results when relevant"
        "\n3. Add clear instructions on what the agent should do next"
    ),
    tools=[
        list_files_in_documents_dir,
        process_all_files,
        search_similar_content
    ],
    sub_agents=[
        api_handler,
        results_processor,
        guide_specialist,
        document_manager,
        response_formatter
    ]
) 