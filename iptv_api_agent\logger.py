import os
import sys
import logging

# Set up logging
current_project_path = os.path.abspath(os.path.dirname(__file__))
logs_dir = os.path.join(current_project_path, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Use a single log file for all components
log_file = os.path.join(logs_dir, "iptv.log")

# Configure root logger formatter
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def get_logger(name):
    """
    Get a configured logger instance for the specified module.
    All loggers will write to the same central log file.
    
    Args:
        name (str): The name of the logger (typically __name__)
        
    Returns:
        logging.Logger: A configured logger instance
    """
    logger = logging.getLogger(name)
    
    # Only add handlers if they don't exist already to prevent duplicate handlers
    if not logger.handlers:
        logger.setLevel(logging.DEBUG)
        
        # Create file handler
        file_handler = logging.FileHandler(log_file)
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        
        # Create console handler for immediate feedback
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        
        # Add the handlers to the logger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
    return logger 