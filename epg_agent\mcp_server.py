import os
import logging
import sys
from fastapi import FastAPI, HTTPException, Depends
from fastapi.middleware.cors import CORSMiddleware
from typing import Optional
from datetime import datetime

# Import our models and client
from .models import (
    ChannelRequest, ChannelsResponse,
    FeedRequest, FeedsResponse,
    StreamRequest, StreamsResponse,
    CategoriesResponse, LanguagesResponse, CountriesResponse,
    SubdivisionsResponse, RegionsResponse, TimezonesResponse, BlocklistResponse,
    QueryRequest, QueryResponse
)
from .epg_client import EpgApiClient

# Set up logging
current_dir = os.path.dirname(os.path.abspath(__file__))
logs_dir = os.path.join(current_dir, "logs")
os.makedirs(logs_dir, exist_ok=True)

# Create a timestamp for the log file
log_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
log_file = os.path.join(logs_dir, f"mcp_server_{log_timestamp}.log")

# Configure logger
logger = logging.getLogger('mcp_server')
logger.setLevel(logging.DEBUG)

# Create file handler
file_handler = logging.FileHandler(log_file)
file_handler.setLevel(logging.DEBUG)

# Create formatter and add it to the handler
formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
file_handler.setFormatter(formatter)

# Add the file handler to the logger
logger.addHandler(file_handler)

# Also add a console handler for immediate feedback
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setLevel(logging.INFO)
console_handler.setFormatter(formatter)
logger.addHandler(console_handler)

logger.info(f"MCP Server logging initialized. Log file: {log_file}")

# Define the API application
app = FastAPI(
    title="EPG API Server",
    description="MCP server for EPG API services and retrieval",
    version="1.0.0"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create a dependency for the API client
def get_api_client():
    """Dependency to get the API client"""
    return EpgApiClient()

# API endpoints
@app.get("/")
async def root():
    return {"message": "EPG API Server is running"}

@app.get("/channels", response_model=ChannelsResponse)
async def get_channels(
    country: Optional[str] = None,
    category: Optional[str] = None,
    language: Optional[str] = None,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get channels from the EPG API
    
    Args:
        country: Optional country code to filter by
        category: Optional category to filter by
        language: Optional language code to filter by
    """
    request = None
    if country or category or language:
        request = ChannelRequest(
            country=country,
            category=category,
            language=language
        )
    return client.get_channels(request)

@app.get("/channels/{channel_id}", response_model=ChannelsResponse)
async def get_channel_by_id(
    channel_id: str,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get a specific channel by ID
    
    Args:
        channel_id: The channel ID
    """
    return client.get_channel(channel_id)

@app.get("/feeds", response_model=FeedsResponse)
async def get_feeds(
    channel: Optional[str] = None,
    region: Optional[str] = None,
    country: Optional[str] = None,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get feeds from the EPG API
    
    Args:
        channel: Optional channel ID to filter by
        region: Optional region code to filter by
        country: Optional country code to filter by
    """
    request = None
    if channel or region or country:
        request = FeedRequest(
            channel=channel,
            region=region,
            country=country
        )
    return client.get_feeds(request)

@app.get("/feeds/{feed_id}", response_model=FeedsResponse)
async def get_feed_by_id(
    feed_id: str,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get a specific feed by ID
    
    Args:
        feed_id: The feed ID
    """
    return client.get_feed(feed_id)

@app.get("/streams", response_model=StreamsResponse)
async def get_streams(
    channel: Optional[str] = None,
    quality: Optional[str] = None,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get streams from the EPG API
    
    Args:
        channel: Optional channel ID to filter by
        quality: Optional quality to filter by
    """
    request = None
    if channel or quality:
        request = StreamRequest(
            channel=channel,
            quality=quality
        )
    return client.get_streams(request)

@app.get("/streams/{channel_id}", response_model=StreamsResponse)
async def get_streams_by_channel(
    channel_id: str,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Get streams for a specific channel
    
    Args:
        channel_id: The channel ID
    """
    request = StreamRequest(channel=channel_id)
    response = client.get_streams(request)
    if response.status == "success" and not response.data:
        raise HTTPException(
            status_code=404, 
            detail=f"No streams found for channel {channel_id}"
        )
    return response

@app.get("/categories", response_model=CategoriesResponse)
async def get_categories(client: EpgApiClient = Depends(get_api_client)):
    """Get categories from the EPG API"""
    return client.get_categories()

@app.get("/languages", response_model=LanguagesResponse)
async def get_languages(client: EpgApiClient = Depends(get_api_client)):
    """Get languages from the EPG API"""
    return client.get_languages()

@app.get("/countries", response_model=CountriesResponse)
async def get_countries(client: EpgApiClient = Depends(get_api_client)):
    """Get countries from the EPG API"""
    return client.get_countries()

@app.get("/subdivisions", response_model=SubdivisionsResponse)
async def get_subdivisions(client: EpgApiClient = Depends(get_api_client)):
    """Get subdivisions from the EPG API"""
    return client.get_subdivisions()

@app.get("/regions", response_model=RegionsResponse)
async def get_regions(client: EpgApiClient = Depends(get_api_client)):
    """Get regions from the EPG API"""
    return client.get_regions()

@app.get("/timezones", response_model=TimezonesResponse)
async def get_timezones(client: EpgApiClient = Depends(get_api_client)):
    """Get timezones from the EPG API"""
    return client.get_timezones()

@app.get("/blocklist", response_model=BlocklistResponse)
async def get_blocklist(client: EpgApiClient = Depends(get_api_client)):
    """Get blocklist from the EPG API"""
    return client.get_blocklist()

@app.post("/query", response_model=QueryResponse)
async def process_query(
    request: QueryRequest,
    client: EpgApiClient = Depends(get_api_client)
):
    """
    Process a natural language query about EPG data
    
    Args:
        request: The query request
    """
    return client.query(request)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000) 