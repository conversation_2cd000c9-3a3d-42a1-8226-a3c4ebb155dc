# SuperSport API Documentation

## Base URL
```
https://supersport.com/apix/guide/v5.3
```

## Available Endpoint

### TV Guide API
**Endpoint**: `GET /tvguide`

Returns a comprehensive TV guide with detailed information about upcoming programs and sports events.

**Parameters**:

| Parameter | Type   | Required | Description |
|-----------|--------|----------|-------------|
| countryCode | String | Yes | Country code (e.g., "za" for South Africa) |
| channelOnly | Boolean | No | When set to "true", returns only channel information without program data |
| startDateTime | String | Yes | Start date for the program guide (YYYY-MM-DD) |
| endDateTime | String | Yes | End date for the program guide (YYYY-MM-DD) |
| liveOnly | Boolean | No | When set to "true", returns only live events |

**Example Request**:
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&channelOnly=false&startDateTime=2025-05-10&endDateTime=2025-05-15&liveOnly=false
```

**Response Format**:
```json
[
    {
        "sport": "Hockey",
        "end": "05/10/2025 01:30:00",
        "isLive": false,
        "start": "05/10/2025 00:30:00",
        "title": "SCH HOC '25 U14A: StM DSG v StADC",
        "name": "SuperSport School HD",
        "rating": "Family",
        "synopsis": "'School Hockey - U14A: St Mary's DSG Kloof vs St Anne's Diocesan School'. From St Mary's Diocesan School for Girls - Durban, South Africa.",
        "thumbnailUri": "https://03mcdecdnimagerepository.blob.core.windows.net/epguideimage/img/388002_S_PS_SCH_HOC_SchoolHockey_LBG_19_80.png",
        "channel": [
            {
                "stream": "857d1b32-d88b-4510-9f6d-7bd33502c342",
                "channelCode": "35L",
                "name": "SuperSport School HD",
                "icon": "/media/enblcrch/play.png",
                "id": "857d1b32-d88b-4510-9f6d-7bd33502c342",
                "mobileIcon": "/media/um3g5anb/supersport_schools_100x100.png",
                "liveIcon": "/media/um3g5anb/supersport_schools_100x100.png",
                "squareIcon": "/media/um3g5anb/supersport_schools_100x100.png",
                "channelNumber": 216
            }
        ],
        "packages": [
            "DSTV_NOW"
        ],
        "showmax": false,
        "subGenres": [
            "All Sport",
            "Hockey"
        ]
    },
    // Additional events omitted for brevity
]
```

**Field Descriptions**:

Each event in the response contains the following properties:

- `sport`: The primary sport category for the event (e.g., "Hockey", "Rugby", "Football")
- `end`: End time of the event in "MM/DD/YYYY HH:MM:SS" format
- `isLive`: Boolean indicating if the event is a live broadcast
- `start`: Start time of the event in "MM/DD/YYYY HH:MM:SS" format
- `title`: The full title of the program/event
- `name`: The name of the channel broadcasting the event
- `rating`: Content rating (e.g., "Family", "PG13")
- `synopsis`: Detailed description of the program/event
- `thumbnailUri`: URL to the program/event thumbnail image
- `channel`: Array containing detailed information about the broadcasting channel(s)
  - `stream`: Unique identifier for the broadcast stream
  - `channelCode`: Channel code in the SuperSport system
  - `name`: Display name of the channel
  - `icon`: Path to the channel icon image
  - `id`: Unique identifier for the channel
  - `mobileIcon`: Path to the channel icon optimized for mobile devices
  - `liveIcon`: Path to the channel icon used for live broadcasts
  - `squareIcon`: Path to the square-format channel icon
  - `channelNumber`: The channel number in the broadcast lineup
- `packages`: Array of subscription packages that include access to this content
- `showmax`: Boolean indicating if the content is available on Showmax
- `subGenres`: Array of sub-genre categories that apply to the event

## Data Relationships

### Programs and Channels

The SuperSport API provides a relationship between programs/events and the channels that broadcast them:

1. Each event contains a `channel` array with detailed channel information
2. Events are linked to their broadcasting channels via the channel `id` and `stream` properties
3. Multiple events can reference the same channel, creating a many-to-one relationship

### Sport Categories and Sub-Genres

The API response includes hierarchical categorization of content:

1. Each event has a primary `sport` category (e.g., "Rugby", "Football", "Hockey")
2. Events also include `subGenres` that provide more specific categorization within the sport
3. This creates a hierarchical relationship for content classification

## Filtering Strategies

Since there's a single API endpoint, different types of filtering need to be implemented client-side or through query parameters:

### Finding All Live Sports

To get a list of all currently broadcasting live sports events, use the `liveOnly` parameter:

```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&liveOnly=true&startDateTime=CURRENT_DATE&endDateTime=CURRENT_DATE
```

### Filtering by Date Range

To get events for a specific date range, use the `startDateTime` and `endDateTime` parameters:

```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=2025-05-10&endDateTime=2025-05-11
```

### Client-Side Filtering

For more specific filters, process the API response to implement filtering logic:

- **Sport-specific filtering**: Filter events where `sport` equals a specific value
- **Channel-specific filtering**: Filter events where `channel` contains a specific channel ID
- **Rating filtering**: Filter events where `rating` equals a specific value
- **Package filtering**: Filter events where `packages` contains a specific subscription package

## Enhanced Date Handling Support

The SuperSport Agent system supports a variety of natural language date formats that are automatically converted to the required YYYY-MM-DD format for API calls:

### Future Date Formats
- `today` - Current date
- `tomorrow` - Next day
- `today+N` - N days from now (e.g., `today+3` for 3 days ahead)

### Past Date Formats
- `yesterday` - Previous day
- `today-N` - N days before today (e.g., `today-5` for 5 days ago) 
- `N days ago` - N days before today (e.g., `3 days ago` for 3 days in the past)
- `last week` - 7 days ago
- `last month` - 30 days ago (approximate)
- `last year` - 365 days ago (approximate)

### Natural Calendar Date Formats
- Month-day formats like `May 10` or `June 15`
- Day-month formats like `10 May` or `15 June`

All these formats are accepted in query parameters and automatically converted to the proper format required by the API, making it easier to search for both historical and future events.

## Required Mandatory Fields

For the TV Guide API endpoint, the following parameters are required:

- `countryCode`: Country code (e.g., "za" for South Africa)
- `startDateTime`: Start date in YYYY-MM-DD format
- `endDateTime`: End date in YYYY-MM-DD format

## Common User Questions and Implementation

Users typically don't know the API structure but will ask natural questions about sports programming. Here are common questions and how to implement the answers using the TV Guide API:

### 1. "What sports are on TV tonight?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=TODAY&endDateTime=TODAY&liveOnly=false
```

Then filter the response to include only events starting between 6 PM and midnight, group by sport category, and present the results.

### 2. "When is the next rugby match?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE&endDateTime=CURRENT_DATE+14&liveOnly=false
```

Then filter the response for events where `sport` equals "Rugby", sort by start date/time, and select the first upcoming event.

### 3. "What's on SuperSport School HD channel today?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=TODAY&endDateTime=TODAY&liveOnly=false
```

Then filter the response for events where channel `name` equals "SuperSport School HD" and sort by start time.

### 4. "Which channels are showing live football?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE&endDateTime=CURRENT_DATE+1&liveOnly=true
```

Then filter the response for events where `sport` equals "Football", extract the unique channel information, and present the results.

### 5. "What school hockey matches are on this weekend?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=UPCOMING_SATURDAY&endDateTime=UPCOMING_SUNDAY&liveOnly=false
```

Then filter the response for events where `sport` equals "Hockey" and `title` or `synopsis` contains keywords like "SCH HOC" or "School Hockey", sort by start time, and present the results.

### 6. "What family-friendly sports content is available today?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=TODAY&endDateTime=TODAY&liveOnly=false
```

Then filter the response for events where `rating` equals "Family", sort by start time, and present the results.

### 7. "What DSTV_NOW sports content is available now?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE&endDateTime=CURRENT_DATE&liveOnly=true
```

Then filter the response for events where `packages` array contains "DSTV_NOW" and present the results.

### 8. "What's currently playing on channel 216?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE&endDateTime=CURRENT_DATE&liveOnly=false
```

Then filter the response for events where `channel.channelNumber` equals 216, determine the current time, find the event that spans the current time, and present the information.

### 9. "What football matches were on last week?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE-7&endDateTime=CURRENT_DATE-1&liveOnly=false
```

Then filter the response for events where `sport` equals "Football", sort by start date/time, and present the historical matches.

### 10. "Show me rugby matches from last month"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE-30&endDateTime=CURRENT_DATE-1&liveOnly=false
```

Then filter the response for events where `sport` equals "Rugby", sort by start date/time, and present the historical matches from the past month.

### 11. "What happened on May 10th on SuperSport channels?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=2025-05-10&endDateTime=2025-05-10&liveOnly=false
```

Then sort events by channel and start time to show the full historical TV guide for that specific date.

### 12. "What were the results of football matches from yesterday?"

**Implementation:**
```
GET https://supersport.com/apix/guide/v5.3/tvguide?countryCode=za&startDateTime=CURRENT_DATE-1&endDateTime=CURRENT_DATE-1&liveOnly=false
```

Then filter the response for events where `sport` equals "Football", sort by start time, and present results. Note that the API provides scheduling information, not match results - the synopsis may contain some results information for completed events.