"""
Main entry point for the SuperSport Agent application
"""

import os
from super_sport_agent_neo4j_graph_db import super_sport_agent, query_graph

def main():
    """Run the SuperSport Agent with the knowledge graph"""
    print("SuperSport Agent with Knowledge Graph")
    print("-------------------------------------")
    
    # Check if the required API key is set
    if not os.getenv("GOOGLE_API_KEY"):
        print("Error: GOOGLE_API_KEY environment variable is not set.")
        print("Please set it before running the application.")
        return
    
    # Check Neo4j connection details
    if not all([os.getenv("NEO4J_URI"), os.getenv("NEO4J_USERNAME"), os.getenv("NEO4J_PASSWORD")]):
        print("Warning: One or more Neo4j environment variables are not set.")
        print("Using default values, which may not work if they're incorrect.")
    
    try:
        # Make a sample query to the knowledge graph
        print("\nTesting Knowledge Graph connection...")
        result = query_graph("What sport categories are available?")
        
        if result["status"] == "success":
            print("✓ Successfully connected to the knowledge graph!")
            print(f"Query: {result['question']}")
            print(f"Answer: {result['answer']}")
            print(f"Cypher Query: {result['cypher_query']}")
        else:
            print("✗ Could not retrieve data from the knowledge graph.")
            print(f"Status: {result['status']}")
            print(f"Message: {result['answer']}")
        
        print("\nThe SuperSport Agent is ready for use.")
        print("You can import it in your application with:")
        print("from super_sport_agent_neo4j_graph_db import super_sport_agent, query_graph")
        
    except Exception as e:
        print(f"Error initializing SuperSport Agent: {str(e)}")


if __name__ == "__main__":
    main()
