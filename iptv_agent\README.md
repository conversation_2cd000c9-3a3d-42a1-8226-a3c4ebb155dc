# IPTV Agent with API Documentation Processing

This project provides an intelligent agent for processing and answering questions about IPTV API documentation by identifying the exact API endpoints to call based on documentation context.

## Overview

The system consists of two main components in a hierarchical agent structure:

1. **Root Agent**: Processes user queries based on API documentation
2. **API Identifier Subagent**: Identifies the exact API endpoint to call from the API documentation

## Key Features

- Documentation-driven API identification
- Hierarchical agent architecture using Google's ADK (Agent Development Kit)
- Root agent delegates API identification to a specialized subagent
- RAG capabilities using local documents for API documentation
- Exact API endpoint identification from documentation

## Setup

### Requirements

- Python 3.8+
- Required packages: langchain, langchain_google_genai, PyPDF2, webvtt, FAISS, requests, google-generativeai, google-adk

### Installation

1. Install the required packages:

```bash
pip install langchain langchain_google_genai langchain_community PyPDF2 webvtt-py faiss-cpu google-generativeai requests pydantic google-adk
```

2. Set up environment variables:

```bash
export GOOGLE_API_KEY=your_google_api_key
```

### Starting the System

Run the demo script to test the agent's capabilities:

```bash
cd iptv_agent
python -m iptv_agent_demo
```

## Agent Architecture

The system uses a hierarchical agent architecture:

### Root Agent

The main agent that:
1. Processes user queries
2. Retrieves relevant API documentation
3. Delegates to the API Identifier subagent
4. Executes the identified API call
5. Presents results to the user

### API Identifier Subagent

A specialized subagent that:
1. Analyzes API documentation context
2. Identifies the exact API endpoint to call
3. Extracts endpoint URL and parameters
4. Explains which API to use and why

## Using the Agent

The agent provides several tools for working with IPTV data:

### Document Processing Tools

- `list_files_in_documents_dir()`: List available document files
- `process_all_files()`: Process all documents in the directory
- `process_file(file_path)`: Process a specific document file
- `create_vector_store_from_text(text)`: Process text content into a vector store

### API Tools

- `iptv_get_channels()`: Get channels data from the IPTV API
- `iptv_get_streams()`: Get streams data from the IPTV API
- `iptv_get_categories()`: Get categories data from the IPTV API
- `iptv_get_countries()`: Get countries data from the IPTV API
- `iptv_query(query)`: Query IPTV data using natural language

### Search and Q&A Tools

- `search_similar_content(query, num_results)`: Search for relevant content in processed documents
- `ask_question(question)`: Ask a question about the processed documents

## Example Queries

Once documents are processed, you can ask questions like:

- "What API endpoint should I use to get a list of all channels?"
- "Which API provides stream URLs for channels?"
- "How can I get information about categories?"
- "What's the API for getting country information?"

## Adding Documents

Place your API documentation files in the `documents` directory:

- PDF files (*.pdf)
- Text files (*.txt, *.md)
- VTT subtitle files (*.vtt)

The agent will automatically process these files and make them available for querying.

## Demo Application

The included demo application (`iptv_agent_demo.py`) showcases the agent's capabilities:

1. Document processing
2. API identifier subagent demonstration
3. Root agent demonstration
4. Interactive agent demo

## Architecture

```
iptv_agent/
├── iptv_agent.py      # Main agent implementation
├── iptv_client.py     # IPTV API client
├── iptv_tools.py      # API tools for the agent
├── models.py          # Data models
├── iptv_agent_demo.py # Demo application
├── __init__.py        # Package initialization
├── documents/         # API documentation files
└── faiss_index/       # Vector store for RAG functionality
```

## Troubleshooting

- If document processing fails, ensure the document files are not corrupted.
- If API requests fail, check your internet connection and the API status.
- If the agent is not responding, check that the GOOGLE_API_KEY environment variable is set correctly.
- If you're not getting good answers, try adding more detailed API documentation to the documents directory. 