"""
API models for SuperSport Agent
"""

from typing import List, Dict, Optional, Any, Union
from pydantic import BaseModel, Field
from datetime import date, datetime
from enum import Enum

# Models for SuperSport API Requests
class ApiRequest(BaseModel):
    """Base model for API requests"""
    pass

class TVGuideRequest(ApiRequest):
    """Request parameters for TV Guide API"""
    countryCode: str = Field(..., description="Country code (e.g., 'za' for South Africa)")
    startDateTime: str = Field(..., description="Start date for the program guide (YYYY-MM-DD)")
    endDateTime: str = Field(..., description="End date for the program guide (YYYY-MM-DD)")
    channelOnly: Optional[bool] = Field(False, description="When true, returns only channel information without program data")
    liveOnly: Optional[bool] = Field(False, description="When true, returns only live events")

class QueryRequest(ApiRequest):
    """Natural language query request"""
    query: str = Field(..., description="The natural language query to process")
    max_results: Optional[int] = Field(10, description="Maximum number of results to return")

# Models for SuperSport API Responses
class ApiResponse(BaseModel):
    """Base model for API responses"""
    status: str = Field(..., description="Status of the API response (success/error)")
    message: Optional[str] = Field(None, description="Response message, particularly for errors")

class Channel(BaseModel):
    """Channel model based on the SuperSport API schema"""
    stream: str
    channelCode: Optional[str] = Field(None, alias="channel_code")
    name: str
    icon: Optional[str] = None
    id: str
    mobileIcon: Optional[str] = Field(None, alias="mobile_icon")
    liveIcon: Optional[str] = Field(None, alias="live_icon")
    squareIcon: Optional[str] = Field(None, alias="square_icon")
    channelNumber: Optional[int] = Field(None, alias="channel_number")

    class Config:
        populate_by_name = True
        extra = "ignore"  # Ignore extra fields that might be in the API response

class SportEvent(BaseModel):
    """Sport event model based on the SuperSport API schema"""
    sport: str
    end: str
    isLive: bool = Field(alias="is_live")
    start: str
    title: str
    name: str
    rating: Optional[str] = None
    synopsis: Optional[str] = None
    thumbnailUri: Optional[str] = Field(None, alias="thumbnail_uri")
    channel: List[Channel]
    packages: Optional[List[str]] = Field(default_factory=list)
    showmax: Optional[bool] = False
    subGenres: Optional[List[str]] = Field(default_factory=list, alias="sub_genres")

    class Config:
        populate_by_name = True
        extra = "ignore"  # Ignore extra fields that might be in the API response

# Response models for specific endpoints
class TVGuideResponse(ApiResponse):
    """Response model for TV Guide endpoint"""
    data: Optional[List[SportEvent]] = []
    total_items: Optional[int] = 0

class LiveSportsResponse(ApiResponse):
    """Response model for live sports endpoint"""
    data: Optional[List[SportEvent]] = []
    total_items: Optional[int] = 0

class UpcomingSportsResponse(ApiResponse):
    """Response model for upcoming sports endpoint"""
    data: Optional[List[SportEvent]] = []
    total_items: Optional[int] = 0

class SportCategoryResponse(ApiResponse):
    """Response model for sport categories"""
    data: Optional[List[str]] = []
    total_items: Optional[int] = 0

class ChannelsResponse(ApiResponse):
    """Response model for channels endpoint"""
    data: Optional[List[Channel]] = []
    total_items: Optional[int] = 0

class QueryResponseItem(BaseModel):
    """Item in query response"""
    source: str = Field(..., description="Source endpoint of the data")
    data: Any = Field(..., description="The actual data")

class QueryResponse(ApiResponse):
    """Response model for natural language queries"""
    query: str = Field(..., description="The original query")
    data: List[QueryResponseItem] = []
    endpoints_queried: List[str] = []
    context: Optional[List[str]] = [] 