"""
Knowledge Graph service for Neo4j operations
"""

import os
import logging
import json
from typing import Dict, Any, Optional, List, Union
from datetime import datetime

import google.generativeai as genai
from langchain_community.graphs import Neo4jGraph
from langchain.chains import GraphCypher<PERSON><PERSON><PERSON><PERSON>
from langchain_google_genai import Cha<PERSON><PERSON><PERSON>gleGenerativeA<PERSON>
from langchain_groq import ChatGroq
from langchain.prompts import PromptTemplate

from ..utils.logger import get_logger
from ..config.settings import (
    GOOGLE_API_KEY,
    GROQ_API_KEY,
    MODEL_GEMINI_2_0_FLASH,
    MODEL_GROQ_LLAMA_3_8B_8192,
    MODEL_GROQ_LLAMA_3,
    NEO4J_URI,
    NEO4J_USERNAME,
    NEO4J_PASSWORD,
    NEO4J_DATABASE
)

# Get logger for this module
logger = get_logger(__name__)

# Configure Google Generative AI with API key
if GOOGLE_API_KEY:
    genai.configure(api_key=GOOGLE_API_KEY)
else:
    logger.warning("GOOGLE_API_KEY not set. LLM functionality will not work properly.")

# Configure GROQ API key
if not GROQ_API_KEY:
    logger.warning("GROQ_API_KEY not set. GROQ LLM functionality will not work properly.")

def serialize_neo4j_objects(obj: Any) -> Any:
    """
    Recursively convert Neo4j objects to serializable formats
    
    Args:
        obj: Object that may contain Neo4j datetime objects
        
    Returns:
        Serializable version of the object
    """
    # Handle Neo4j DateTime objects
    if hasattr(obj, '__class__') and 'neo4j.time' in str(obj.__class__):
        if hasattr(obj, 'to_native'):
            # Convert Neo4j DateTime to Python datetime, then to ISO string
            return obj.to_native().isoformat()
        else:
            # Fallback to string representation
            return str(obj)
    
    # Handle dictionaries
    elif isinstance(obj, dict):
        return {key: serialize_neo4j_objects(value) for key, value in obj.items()}
    
    # Handle lists
    elif isinstance(obj, list):
        return [serialize_neo4j_objects(item) for item in obj]
    
    # Handle tuples
    elif isinstance(obj, tuple):
        return tuple(serialize_neo4j_objects(item) for item in obj)
    
    # Return as-is for other types
    else:
        return obj

class KnowledgeGraphService:
    """Service for Neo4j knowledge graph operations using LangChain"""
    
    def __init__(self):
        """Initialize the knowledge graph service"""
        self._graph: Optional[Neo4jGraph] = None
        self._qa_chain: Optional[GraphCypherQAChain] = None
        logger.info("Initialized knowledge graph service")
    
    def get_neo4j_graph(self) -> Optional[Neo4jGraph]:
        """
        Get the Neo4j graph connection
        
        Returns:
            Neo4jGraph instance or None if not available
        """
        try:
            # If we already have a loaded graph connection
            if self._graph:
                return self._graph
            
            # Create new Neo4j graph connection
            self._graph = Neo4jGraph(
                url=NEO4J_URI,
                username=NEO4J_USERNAME, 
                password=NEO4J_PASSWORD,
                database=NEO4J_DATABASE
            )
            
            return self._graph
            
        except Exception as e:
            logger.error(f"Error connecting to Neo4j: {str(e)}")
            return None
    
    def get_qa_chain(self) -> GraphCypherQAChain:
        """
        Get the QA chain for the graph
        
        Returns:
            GraphCypherQAChain instance
        """
        if self._qa_chain:
            return self._qa_chain
            
        # Get Neo4j graph connection
        graph = self.get_neo4j_graph()
        if not graph:
            raise ValueError("Neo4j connection not available")
            
        # Create LLM
        # llm = ChatGoogleGenerativeAI(model=MODEL_GEMINI_2_0_FLASH)
        llm = ChatGroq(model=MODEL_GROQ_LLAMA_3, groq_api_key=GROQ_API_KEY)
        
        # Retrieve the graph schema
        schema = graph.get_schema
        
        # Set up the schema-aware QA chain
        template = """
        Task: Generate a Cypher statement to query the graph database.

        Instructions:
        Use only relationship types and properties provided in schema.
        Do not use other relationship types or properties that are not provided.

        schema:
        {schema}

        Note: Do not include explanations or apologies in your answers.
        Do not answer questions that ask anything other than creating Cypher statements.
        Do not include any text other than generated Cypher statements.

        Question: {question}"""

        question_prompt = PromptTemplate(
            template=template,
            input_variables=["schema", "question"]
        )
        
        # Create the chain with schema-aware prompt
        self._qa_chain = GraphCypherQAChain.from_llm(
            llm=llm,
            graph=graph,
            cypher_prompt=question_prompt,
            verbose=True,
            return_intermediate_steps=True,
            allow_dangerous_requests=True  # Required for database access
        )
        
        return self._qa_chain
    
    def query_knowledge_graph(self, question: str) -> Dict[str, Any]:
        """
        Query the knowledge graph using LangChain's GraphCypherQAChain
        
        Args:
            question: The question to ask
            
        Returns:
            Dict containing the answer and intermediate steps (with serializable objects)
        """
        try:
            logger.info(f"Querying knowledge graph: {question}")
            
            # Get the QA chain
            chain = self.get_qa_chain()
            
            # Query the knowledge graph
            result = chain({"query": question})
            
            # Log the result
            logger.info(f"Generated Cypher query: {result.get('intermediate_steps', [{}])[0].get('query', 'No query found')}")
            
            # Serialize the result to handle Neo4j objects
            serialized_result = serialize_neo4j_objects(result)
            
            # Return a structured result with serialized objects
            return {
                "status": "success",
                "question": question,
                "answer": serialized_result.get("result", "No answer found"),
                "cypher_query": serialized_result.get("intermediate_steps", [{}])[0].get("query", ""),
                "context": serialized_result.get("intermediate_steps", [{}])[0].get("context", []),
                "raw_result": serialized_result
            }
            
        except Exception as e:
            logger.error(f"Error querying knowledge graph: {str(e)}")
            return {
                "status": "error",
                "question": question,
                "answer": f"An error occurred while querying the knowledge graph: {str(e)}",
                "cypher_query": "",
                "context": [],
                "raw_result": {}
            } 