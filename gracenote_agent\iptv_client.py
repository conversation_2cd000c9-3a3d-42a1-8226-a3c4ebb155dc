import os
import logging
import requests
from typing import List, Dict, Optional, Any, Type, Union, TypeVar, Generic
import urllib3
from datetime import datetime
import os.path
from .models import (
    ApiRequest, ApiResponse,
    ChannelRequest, ChannelsResponse, Channel,
    StreamRequest, StreamsResponse, Stream,
    CategoriesResponse, Category,
    CountriesResponse, Country,
    GuidesResponse, Guide,
    QueryRequest, QueryResponse, QueryResponseItem
)

# Import central logger
from .logger import get_logger
from .utils import limit_response_tokens, preprocess_api_response, safe_convert_to_dict

# Suppress SSL warnings - only use this in development
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

# Get logger for this module
logger = get_logger('iptv_client')
logger.info("IPTV Client logging initialized")

T = TypeVar('T', bound=ApiResponse)

class IptvApiClient:
    """Client for interacting with the IPTV API"""
    
    def __init__(self, base_url: Optional[str] = None):
        """
        Initialize the IPTV API client
        
        Args:
            base_url: Base URL for the IPTV API. Defaults to the REMOTE_API_BASE_URL environment
                     variable or "https://iptv-org.github.io/api"
        """
        self.base_url = base_url or os.getenv("REMOTE_API_BASE_URL", "https://iptv-org.github.io/api")
        logger.info(f"Initialized IPTV API client with base URL: {self.base_url}")
    
    def _make_request(self, 
                     endpoint: str, 
                     params: Optional[Dict[str, Any]] = None, 
                     response_model: Type[T] = ApiResponse,
                     query: Optional[str] = None) -> T:
        """
        Make a request to the IPTV API
        
        Args:
            endpoint: The API endpoint to request (e.g., "channels.json")
            params: Optional query parameters
            response_model: Pydantic model to use for the response
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            The API response parsed into the specified model
        """
        url = f"{self.base_url}/{endpoint}"
        logger.info(f"Making request to {url}")
        
        try:
            # Disable SSL verification - this is a workaround for SSL certificate issues
            # In production, you should fix the certificate issue instead of disabling verification
            response = requests.get(url, params=params, verify=False)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"Response received from {url}: {len(data)} items")
            
            # For list responses, create the appropriate response object
            if isinstance(data, list):
                # Get the total number of items
                total_items = len(data)
                logger.info(f"Total items in response: {total_items}")
                
                # Special handling for guide data, which may have a different structure
                if endpoint == "guides.json":
                    try:
                        # Create the response object with valid items only
                        valid_items = []
                        for item in data:
                            # Ensure required fields exist
                            if 'id' not in item:
                                item['id'] = f"guide_{len(valid_items)}"
                            if 'channel' not in item or item['channel'] is None:
                                item['channel'] = "unknown"
                            if 'title' not in item:
                                item['title'] = "Untitled Program"
                            
                            try:
                                # Try to create a valid guide model
                                guide_model = Guide(**item)
                                valid_items.append(guide_model)
                            except Exception as e:
                                logger.warning(f"Skipping invalid guide item: {e}")
                        
                        # Create response model
                        api_response = response_model(
                            status="success",
                            data=valid_items,
                            total_items=len(valid_items)
                        )
                        
                        # If query is provided, filter the response
                        if query:
                            # Convert response to dict for preprocessing
                            response_dict = safe_convert_to_dict(api_response)
                            # Preprocess and filter the response
                            filtered_dict = preprocess_api_response(response_dict, query, max_items=50)
                            # Update the response data and total items
                            api_response.data = filtered_dict.get("data", [])
                            api_response.total_items = len(api_response.data)
                            if "filter_applied" in filtered_dict:
                                setattr(api_response, "filter_applied", filtered_dict["filter_applied"])
                            if "original_count" in filtered_dict:
                                setattr(api_response, "original_count", filtered_dict["original_count"])
                        
                        logger.info(f"Returning {len(api_response.data)} items after filtering (from {total_items})")
                        return api_response
                    except Exception as e:
                        logger.error(f"Error processing guide data: {str(e)}")
                        return response_model(
                            status="error",
                            message=f"Error processing guide data: {str(e)}",
                            data=[],
                            total_items=0
                        )
                
                # For other endpoints, proceed normally
                try:
                    # Create the initial response object
                    api_response = response_model(
                        status="success",
                        data=data,
                        total_items=total_items
                    )
                    
                    # If query is provided, filter the response
                    if query:
                        # Convert response to dict for preprocessing
                        response_dict = safe_convert_to_dict(api_response)
                        # Preprocess and filter the response
                        filtered_dict = preprocess_api_response(response_dict, query, max_items=50)
                        # Update the response data and total items
                        api_response.data = filtered_dict.get("data", [])
                        api_response.total_items = len(api_response.data)
                        if "filter_applied" in filtered_dict:
                            setattr(api_response, "filter_applied", filtered_dict["filter_applied"])
                        if "original_count" in filtered_dict:
                            setattr(api_response, "original_count", filtered_dict["original_count"])
                    
                    logger.info(f"Returning {len(api_response.data)} items after filtering (from {total_items})")
                    return api_response
                except Exception as e:
                    logger.error(f"Error creating response model: {str(e)}")
                    return response_model(
                        status="error", 
                        message=f"Error processing response: {str(e)}",
                        data=[]
                    )
            else:
                # For single-item responses
                try:
                    # Create response model
                    api_response = response_model(
                        status="success",
                        data=[data]
                    )
                    
                    # If query is provided, filter the response
                    if query:
                        # Convert response to dict for preprocessing
                        response_dict = safe_convert_to_dict(api_response)
                        # Preprocess and filter the response
                        filtered_dict = preprocess_api_response(response_dict, query, max_items=50)
                        # Update the response data
                        api_response.data = filtered_dict.get("data", [])
                        api_response.total_items = len(api_response.data)
                    
                    return api_response
                except Exception as e:
                    logger.error(f"Error creating response model: {str(e)}")
                    return response_model(
                        status="error", 
                        message=f"Error processing response: {str(e)}",
                        data=[]
                    )
        
        except requests.RequestException as e:
            logger.error(f"Request error: {str(e)}")
            return response_model(
                status="error",
                message=f"Request error: {str(e)}",
                data=[]
            )
        except ValueError as e:
            logger.error(f"JSON parsing error: {str(e)}")
            return response_model(
                status="error",
                message=f"Error parsing response: {str(e)}",
                data=[]
            )
        except Exception as e:
            logger.error(f"Unexpected error: {str(e)}")
            return response_model(
                status="error",
                message=f"Unexpected error: {str(e)}",
                data=[]
            )
    
    def get_channels(self, request: Optional[ChannelRequest] = None, query: Optional[str] = None) -> ChannelsResponse:
        """
        Get channels from the IPTV API
        
        Args:
            request: Optional request parameters
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            ChannelsResponse: The channels response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("channels.json", params, ChannelsResponse, query)
    
    def get_channel(self, channel_id: str) -> ChannelsResponse:
        """
        Get a specific channel by ID
        
        Args:
            channel_id: The channel ID
            
        Returns:
            ChannelsResponse: The channel response
        """
        all_channels = self.get_channels()
        if all_channels.status != "success":
            return all_channels
        
        # Find the specific channel
        matching_channels = [ch for ch in all_channels.data if ch.id == channel_id]
        
        if matching_channels:
            return ChannelsResponse(
                status="success",
                data=matching_channels,
                total_items=len(matching_channels)
            )
        else:
            return ChannelsResponse(
                status="error",
                message=f"Channel not found: {channel_id}",
                data=[],
                total_items=0
            )
    
    def get_streams(self, request: Optional[StreamRequest] = None, query: Optional[str] = None) -> StreamsResponse:
        """
        Get streams from the IPTV API
        
        Args:
            request: Optional request parameters
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            StreamsResponse: The streams response
        """
        params = request.dict(exclude_none=True) if request else None
        return self._make_request("streams.json", params, StreamsResponse, query)
    
    def get_categories(self, query: Optional[str] = None) -> CategoriesResponse:
        """
        Get categories from the IPTV API
        
        Args:
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            CategoriesResponse: The categories response
        """
        return self._make_request("categories.json", None, CategoriesResponse, query)
    
    def get_countries(self, query: Optional[str] = None) -> CountriesResponse:
        """
        Get countries from the IPTV API
        
        Args:
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            CountriesResponse: The countries response
        """
        return self._make_request("countries.json", None, CountriesResponse, query)
    
    def get_guides(self, channel_id: Optional[str] = None, query: Optional[str] = None) -> GuidesResponse:
        """
        Get program guides information from the IPTV API
        
        Args:
            channel_id: Optional channel ID to filter by
            query: Optional natural language query for preprocessing filtering
            
        Returns:
            GuidesResponse: The guides response
        """
        try:
            # Construct query that includes channel_id if provided
            combined_query = query or ""
            if channel_id:
                combined_query = f"channel {channel_id} {combined_query}".strip()
            
            all_guides = self._make_request("guides.json", None, GuidesResponse, combined_query)
            
            # If a channel ID is provided and we haven't filtered in the _make_request,
            # filter the guides here
            if channel_id and all_guides.status == "success" and not hasattr(all_guides, "filter_applied"):
                matching_guides = [g for g in all_guides.data if g.channel == channel_id]
                return GuidesResponse(
                    status="success",
                    data=matching_guides,
                    total_items=len(matching_guides),
                    filter_applied=f"channel: {channel_id}",
                    original_count=len(all_guides.data)
                )
            return all_guides
        except Exception as e:
            logger.error(f"Error in get_guides: {str(e)}")
            return GuidesResponse(
                status="error",
                message=f"Error retrieving guides: {str(e)}",
                data=[],
                total_items=0
            )
    
    def query(self, request: QueryRequest) -> QueryResponse:
        """
        Process a natural language query by making the appropriate API calls
        
        Args:
            request: The query request
            
        Returns:
            QueryResponse: The query response
        """
        query = request.query.lower()
        max_results = request.max_results
        results = []
        endpoints_queried = []
        
        # Simple keyword-based query processing
        if any(kw in query for kw in ["channel", "tv", "station", "network"]):
            endpoints_queried.append("channels")
            channels_resp = self.get_channels(query=query)
            if channels_resp.status == "success":
                results.append(QueryResponseItem(
                    source="channels",
                    data=channels_resp.data
                ))
                
        if any(kw in query for kw in ["stream", "watch", "link", "url"]):
            endpoints_queried.append("streams")
            streams_resp = self.get_streams(query=query)
            if streams_resp.status == "success":
                results.append(QueryResponseItem(
                    source="streams",
                    data=streams_resp.data
                ))
                
        if any(kw in query for kw in ["category", "type", "genre"]):
            endpoints_queried.append("categories")
            categories_resp = self.get_categories(query=query)
            if categories_resp.status == "success":
                results.append(QueryResponseItem(
                    source="categories",
                    data=categories_resp.data
                ))
                
        if any(kw in query for kw in ["country", "countries", "region", "location"]):
            endpoints_queried.append("countries")
            countries_resp = self.get_countries(query=query)
            if countries_resp.status == "success":
                results.append(QueryResponseItem(
                    source="countries",
                    data=countries_resp.data
                ))
        
        if any(kw in query for kw in ["guide", "program", "show", "schedule", "what's on", "whats on", "listing"]):
            endpoints_queried.append("guides")
            guides_resp = self.get_guides(query=query)
            if guides_resp.status == "success":
                results.append(QueryResponseItem(
                    source="guides",
                    data=guides_resp.data
                ))
                
        # If no specific queries were run, default to channels
        if not results:
            endpoints_queried.append("channels")
            channels_resp = self.get_channels(query=query)
            if channels_resp.status == "success":
                results.append(QueryResponseItem(
                    source="channels",
                    data=channels_resp.data
                ))
        
        return QueryResponse(
            status="success",
            query=request.query,
            data=results,
            endpoints_queried=endpoints_queried
        ) 