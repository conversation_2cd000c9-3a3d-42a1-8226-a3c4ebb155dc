"""
Utility functions for the IPTV Agent
"""

import json
import re
import os
import logging
from typing import Dict, Any, List, Optional, Union
from langchain_google_genai import GoogleGenerativeAIEmbeddings, ChatGoogleGenerativeAI
from langchain_community.vectorstores import FAISS
from langchain.prompts import PromptTemplate
import google.generativeai as genai

# Import logger
from .logger import get_logger

# Get logger for this module
logger = get_logger('utils')

# Get vector store path from the environment or use a default
current_project_path = os.path.abspath(os.path.dirname(os.path.dirname(__file__)))
vector_store_path = os.path.join(current_project_path, "iptv_api_agent", "faiss_index")

def get_filter_criteria_from_vector_db(query: str) -> Dict[str, Any]:
    """
    Extract filtering criteria from a natural language query using the vector DB and LLM reasoning.
    
    Args:
        query (str): The user's natural language query
        
    Returns:
        dict: Extracted filter criteria (country, category, language, etc.)
    """
    # Initialize default criteria
    criteria = {
        "country": None,
        "category": None,
        "language": None,
        "channel": None,
        "quality": None,
        "time_context": None
    }
    
    try:
        # Check if vector store exists
        if not os.path.exists(vector_store_path) or not os.path.isdir(vector_store_path):
            logger.warning("Vector store not found, falling back to hardcoded filters")
            return extract_filter_criteria(query)
        
        # Get embeddings model
        embeddings = GoogleGenerativeAIEmbeddings(model="models/embedding-001")
        
        # Load vector store
        vector_store = FAISS.load_local(vector_store_path, embeddings, allow_dangerous_deserialization=True)
        
        # Search for relevant documents
        docs = vector_store.similarity_search(query, k=3)
        
        # If no documents found, fallback to hardcoded filters
        if not docs:
            logger.warning("No relevant documents found in vector DB, falling back to hardcoded filters")
            return extract_filter_criteria(query)
        
        # Extract context from documents
        context = "\n".join([doc.page_content for doc in docs])
        
        # Create LLM model for reasoning
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            logger.warning("GOOGLE_API_KEY not found, falling back to hardcoded filters")
            return extract_filter_criteria(query)
        
        genai.configure(api_key=api_key)
        
        # Create prompt for filter extraction
        filter_prompt = PromptTemplate(
            input_variables=["context", "query"],
            template="""
            Based on the following context and user query, extract the appropriate filter criteria for an IPTV API request.
            
            CONTEXT:
            {context}
            
            USER QUERY:
            {query}
            
            Extract the following filter criteria in JSON format (use null if not applicable):
            1. country: A valid country code (e.g., "US", "IN", "GB") based on the country mentioned
            2. category: The content category (e.g., "news", "sports", "entertainment")
            3. language: A valid language code (e.g., "en", "es", "hi") 
            4. channel: Specific channel name mentioned
            5. quality: Quality preference ("high", "standard", or null)
            6. time_context: Time reference ("current", "upcoming", "today", etc.)
            
            Return ONLY a valid JSON object without explanation or additional text.
            """
        )
        
        # Get LLM model
        llm = ChatGoogleGenerativeAI(model="gemini-2.0-flash", temperature=0)
        
        # Format the prompt with context and query
        formatted_prompt = filter_prompt.format(context=context, query=query)
        
        # Get response from LLM
        response = llm.invoke(formatted_prompt)
        
        # Extract JSON from response
        response_text = response.content
        
        try:
            # Try to parse JSON from the response
            import re
            # Find JSON object in the response
            json_match = re.search(r'(\{.*\})', response_text, re.DOTALL)
            if json_match:
                json_str = json_match.group(1)
                filter_criteria = json.loads(json_str)
                
                # Update criteria with values from LLM
                for key in criteria:
                    if key in filter_criteria and filter_criteria[key] not in [None, "", "null"]:
                        criteria[key] = filter_criteria[key]
                
                logger.info(f"Successfully extracted filter criteria from vector DB: {criteria}")
                return criteria
            else:
                logger.warning("Could not extract JSON from LLM response, falling back to hardcoded filters")
                return extract_filter_criteria(query)
        except Exception as e:
            logger.error(f"Error parsing LLM response: {e}, falling back to hardcoded filters")
            return extract_filter_criteria(query)
    
    except Exception as e:
        logger.error(f"Error extracting filters from vector DB: {e}, falling back to hardcoded filters")
        return extract_filter_criteria(query)

def extract_filter_criteria(query: str) -> Dict[str, Any]:
    """
    Extract filtering criteria from a natural language query using hardcoded rules.
    This is a fallback when vector DB extraction fails.
    
    Args:
        query (str): The user's natural language query
        
    Returns:
        dict: Extracted filter criteria (country, category, language, etc.)
    """
    query = query.lower()
    criteria = {
        "country": None,
        "category": None,
        "language": None,
        "channel": None,
        "quality": None,
        "time_context": None
    }
    
    # Check for country mentions
    countries = {
        "india": "IN", "indian": "IN", "us": "US", "usa": "US", "american": "US", 
        "uk": "GB", "british": "GB", "canada": "CA", "canadian": "CA",
        "australia": "AU", "australian": "AU", "france": "FR", "french": "FR",
        "germany": "DE", "german": "DE", "japan": "JP", "japanese": "JP",
        "china": "CN", "chinese": "CN", "russia": "RU", "russian": "RU",
        "brazil": "BR", "brazilian": "BR", "italy": "IT", "italian": "IT",
        "spain": "ES", "spanish": "ES", "mexico": "MX", "mexican": "MX"
    }
    
    for country_name, country_code in countries.items():
        if country_name in query:
            criteria["country"] = country_code
            break
    
    # Check for category mentions
    categories = [
        "news", "sports", "entertainment", "movies", "music", "documentary", 
        "kids", "science", "business", "technology", "politics", "comedy",
        "drama", "action", "reality", "game show", "talk show", "lifestyle"
    ]
    
    for category in categories:
        if category in query:
            criteria["category"] = category
            break
    
    # Check for language mentions
    languages = {
        "english": "en", "spanish": "es", "french": "fr", "german": "de",
        "hindi": "hi", "chinese": "zh", "japanese": "ja", "korean": "ko",
        "russian": "ru", "arabic": "ar", "portuguese": "pt", "italian": "it"
    }
    
    for lang_name, lang_code in languages.items():
        if lang_name in query:
            criteria["language"] = lang_code
            break
    
    # Check for channel mentions - look for capitalized words that might be channel names
    channel_match = re.search(r'\b([A-Z][A-Z0-9]*(?:\s+[A-Z][A-Z0-9]*)*)\b', query)
    if channel_match:
        criteria["channel"] = channel_match.group(1)
    
    # Check for quality mentions
    if any(q in query for q in ["hd", "high definition", "high quality", "4k", "ultra hd", "good quality"]):
        criteria["quality"] = "high"
    elif any(q in query for q in ["sd", "standard definition", "low quality"]):
        criteria["quality"] = "standard"
    
    # Check for time context
    time_contexts = {
        "now": "current", "current": "current", "currently": "current", "live": "current",
        "today": "today", "tonight": "tonight", "tomorrow": "tomorrow", 
        "this week": "this_week", "upcoming": "upcoming", "soon": "upcoming",
        "morning": "morning", "afternoon": "afternoon", "evening": "evening"
    }
    
    for time_term, context in time_contexts.items():
        if time_term in query:
            criteria["time_context"] = context
            break
    
    return criteria

def preprocess_api_response(response: Dict[str, Any], query: str, max_items: int = 20) -> Dict[str, Any]:
    """
    Preprocess API response by applying intelligent filtering based on the user query
    before the response is sent to the agent.
    
    Args:
        response (dict): The raw API response
        query (str): The original user query
        max_items (int): Maximum number of items to return
        
    Returns:
        dict: Filtered and processed response
    """
    if not response or not isinstance(response, dict):
        return response
    
    # Extract filter criteria from the query using vector DB
    criteria = get_filter_criteria_from_vector_db(query)
    logger.info(f"Filter criteria extracted for query '{query}': {criteria}")
    
    # Only process if we have data
    if "data" not in response or not isinstance(response["data"], list) or not response["data"]:
        return response
    
    filtered_data = response["data"]
    filter_explanation = []
    
    # Apply country filter
    if criteria["country"] and any(hasattr(item, "country") for item in filtered_data):
        country_code = criteria["country"]
        filtered_data = [item for item in filtered_data if getattr(item, "country", None) == country_code]
        filter_explanation.append(f"country: {country_code}")
    
    # Apply category filter
    if criteria["category"] and any(hasattr(item, "categories") for item in filtered_data):
        category = criteria["category"]
        filtered_data = [
            item for item in filtered_data 
            if hasattr(item, "categories") and 
               item.categories and 
               any(cat.lower() == category.lower() for cat in item.categories)
        ]
        filter_explanation.append(f"category: {category}")
    
    # Apply language filter
    if criteria["language"] and any(hasattr(item, "language") for item in filtered_data):
        language = criteria["language"]
        filtered_data = [item for item in filtered_data if getattr(item, "language", None) == language]
        filter_explanation.append(f"language: {language}")
    
    # Apply channel filter
    if criteria["channel"] and any(hasattr(item, "name") for item in filtered_data):
        channel_name = criteria["channel"]
        filtered_data = [
            item for item in filtered_data 
            if hasattr(item, "name") and 
               item.name and 
               channel_name.lower() in item.name.lower()
        ]
        filter_explanation.append(f"channel: {channel_name}")
    
    # Apply quality filter
    if criteria["quality"] and any(hasattr(item, "quality") for item in filtered_data):
        quality = criteria["quality"]
        filtered_data = [
            item for item in filtered_data 
            if hasattr(item, "quality") and 
               item.quality and 
               (quality.lower() in item.quality.lower())
        ]
        filter_explanation.append(f"quality: {quality}")
    
    # Limit to max_items
    if len(filtered_data) > max_items:
        filtered_data = filtered_data[:max_items]
    
    # Create a new response with filtered data
    filtered_response = response.copy()
    filtered_response["data"] = filtered_data
    filtered_response["total_items"] = len(filtered_data)
    
    # Add filtering metadata
    if filter_explanation:
        filtered_response["filter_applied"] = ", ".join(filter_explanation)
        filtered_response["original_count"] = len(response["data"])
    
    return filtered_response

def limit_response_tokens(data: Dict[str, Any], 
                         max_items: int = 10, 
                         max_description_length: int = 100, 
                         max_total_tokens: int = 5000,
                         query: Optional[str] = None) -> Dict[str, Any]:
    """
    Limit the size of the API response to prevent token limit errors.
    
    Args:
        data (dict): The API response data
        max_items (int): Maximum number of items to include in lists
        max_description_length (int): Maximum length for text fields
        max_total_tokens (int): Approximate max tokens for entire response
        query (str): Optional user query for intelligent filtering
        
    Returns:
        dict: Truncated data that should fit within token limits
    """
    if not data or not isinstance(data, dict):
        return data
    
    # If query is provided, first apply intelligent filtering
    if query:
        data = preprocess_api_response(data, query, max_items=max_items)
        
    # Handle data lists
    if "data" in data and isinstance(data["data"], list):
        # Limit number of items
        data["data"] = data["data"][:max_items]
        
        # Truncate long text fields in each item
        for item in data["data"]:
            if isinstance(item, dict):
                for key, value in item.items():
                    if isinstance(value, str) and len(value) > max_description_length:
                        item[key] = value[:max_description_length] + "..."
    
    # Estimate token size and further truncate if needed
    json_str = json.dumps(data)
    estimated_tokens = len(json_str) / 4  # Rough approximation: 4 chars ~= 1 token
    
    if estimated_tokens > max_total_tokens and "data" in data and isinstance(data["data"], list):
        # Further reduce items if still too large
        reduced_items = max(1, int(max_items * (max_total_tokens / estimated_tokens)))
        data["data"] = data["data"][:reduced_items]
        data["note"] = f"Response truncated to {reduced_items} items due to token limits."
    
    return data

def safe_convert_to_dict(obj: Any) -> Union[Dict, List, Any]:
    """
    Safely convert a complex object (like Pydantic models) to dictionary.
    Handles nested objects, lists, and primitive types.
    
    Args:
        obj: Object to convert
        
    Returns:
        Union[Dict, List, Any]: Dictionary representation of the object
    """
    if hasattr(obj, "dict") and callable(getattr(obj, "dict")):
        # It's a Pydantic model
        return obj.dict()
    elif isinstance(obj, list):
        # It's a list, convert each item
        return [safe_convert_to_dict(item) for item in obj]
    elif isinstance(obj, dict):
        # It's already a dict, convert each value
        return {k: safe_convert_to_dict(v) for k, v in obj.items()}
    else:
        # It's a primitive type
        return obj 